"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(DashboardLayout)/layout",{

/***/ "(app-pages-browser)/./src/app/components/shared/OrderNotificationOverlay.tsx":
/*!****************************************************************!*\
  !*** ./src/app/components/shared/OrderNotificationOverlay.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/context/OrderNotificationContext */ \"(app-pages-browser)/./src/app/context/OrderNotificationContext.tsx\");\n/* harmony import */ var flowbite_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! flowbite-react */ \"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HiCheckCircle,HiRefresh,HiXCircle!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/esm/locale/ar/index.js\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationTriangle!=!react-icons/hi2 */ \"(app-pages-browser)/./node_modules/react-icons/hi2/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst OrderNotificationOverlay = (param)=>{\n    let { restaurantId } = param;\n    _s();\n    const { pendingOrders, isConnected, isLoading, error, acceptOrder, rejectOrder, refreshOrders } = (0,_app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__.useOrderNotification)();\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentOrdersModal, setShowCurrentOrdersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRejectConfirmModal, setShowRejectConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderToReject, setOrderToReject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [processingOrderId, setProcessingOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentOrders, setCurrentOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [preparingOrders, setPreparingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Show modal when there are pending orders\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"OrderNotificationOverlay.useEffect\": ()=>{\n            console.log(\"OrderOverlay: Pending orders count changed to \".concat(pendingOrders.length));\n            if (pendingOrders.length > 0) {\n                console.log('OrderOverlay: Showing modal for pending orders');\n                setShowModal(true);\n            } else {\n                console.log('OrderOverlay: Hiding modal - no pending orders');\n                setShowModal(false);\n            }\n        }\n    }[\"OrderNotificationOverlay.useEffect\"], [\n        pendingOrders.length\n    ]);\n    // Fetch preparing orders (status = 1) - same pattern as pending orders\n    const fetchPreparingOrders = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\": async ()=>{\n            if (!restaurantId) {\n                console.log('OrderOverlay: No restaurant ID provided for preparing orders');\n                return;\n            }\n            try {\n                console.log(\"OrderOverlay: Fetching preparing orders for restaurant \".concat(restaurantId));\n                const accessToken = localStorage.getItem('accessToken');\n                if (!accessToken) {\n                    throw new Error('No access token found');\n                }\n                const apiUrl = \"\".concat(\"http://localhost:3001\", \"/orders/restaurant/\").concat(restaurantId, \"?page=1&size=50\");\n                console.log(\"OrderOverlay: Making request to \".concat(apiUrl));\n                const response = await fetch(apiUrl, {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(accessToken),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log(\"OrderOverlay: Response status: \".concat(response.status));\n                if (!response.ok) {\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const responseData = await response.json();\n                console.log(\"OrderOverlay: Received response for preparing orders:\", responseData);\n                // Handle paginated response format - same as pending orders\n                const orders = responseData.items || responseData.data || [];\n                console.log(\"OrderOverlay: Received \".concat(orders.length, \" total orders\"));\n                // Filter only preparing orders (Status = 1)\n                const preparing = orders.filter({\n                    \"OrderNotificationOverlay.useCallback[fetchPreparingOrders].preparing\": (order)=>order.Status === 1\n                }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders].preparing\"]);\n                console.log(\"OrderOverlay: Found \".concat(preparing.length, \" preparing orders\"));\n                setPreparingOrders(preparing);\n                // Log preparing orders for debugging\n                if (preparing.length > 0) {\n                    console.log('OrderOverlay: Preparing orders:', preparing.map({\n                        \"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\": (o)=>({\n                                id: o.OrderID,\n                                status: o.Status,\n                                total: o.CartTotalPrice\n                            })\n                    }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\"]));\n                }\n            } catch (err) {\n                console.error('OrderOverlay: Error fetching preparing orders:', err);\n            }\n        }\n    }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\"], [\n        restaurantId\n    ]);\n    // Fetch preparing orders periodically - same pattern as pending orders\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"OrderNotificationOverlay.useEffect\": ()=>{\n            fetchPreparingOrders(); // Initial fetch\n            const interval = setInterval(fetchPreparingOrders, 15000); // Update every 15 seconds\n            return ({\n                \"OrderNotificationOverlay.useEffect\": ()=>clearInterval(interval)\n            })[\"OrderNotificationOverlay.useEffect\"];\n        }\n    }[\"OrderNotificationOverlay.useEffect\"], [\n        fetchPreparingOrders\n    ]);\n    const handleAcceptOrder = async (orderId)=>{\n        try {\n            setProcessingOrderId(orderId);\n            await acceptOrder(orderId);\n        } catch (err) {\n            console.error('Failed to accept order:', err);\n        } finally{\n            setProcessingOrderId(null);\n        }\n    };\n    const handleRejectOrder = async (orderId)=>{\n        setOrderToReject(orderId);\n        setShowRejectConfirmModal(true);\n    };\n    const confirmRejectOrder = async ()=>{\n        if (!orderToReject) return;\n        try {\n            setProcessingOrderId(orderToReject);\n            await rejectOrder(orderToReject);\n            setShowRejectConfirmModal(false);\n            setOrderToReject(null);\n        } catch (err) {\n            console.error('Failed to reject order:', err);\n        } finally{\n            setProcessingOrderId(null);\n        }\n    };\n    const fetchCurrentOrders = async ()=>{\n        setShowCurrentOrdersModal(true);\n        // Use the already fetched preparing orders\n        setCurrentOrders(preparingOrders);\n    };\n    const formatPrice = (price)=>{\n        return \"\".concat(price.toFixed(2), \" د.ل\");\n    };\n    const getImageUrl = (imageUrl)=>{\n        if (!imageUrl) return '/images/placeholder-food.jpg';\n        if (imageUrl.startsWith('http')) return imageUrl;\n        return \"\".concat(\"http://localhost:3001\", \"/\").concat(imageUrl.replace(/^\\/+/, ''));\n    };\n    // Connection status indicator\n    const ConnectionStatus = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-4 left-4 z-50\",\n            children: !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                color: \"failure\",\n                icon: _barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle,\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"فقدان الاتصال بالخادم\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"xs\",\n                            color: \"failure\",\n                            onClick: refreshOrders,\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 52\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 160,\n            columnNumber: 5\n        }, undefined);\n    // Order notification badge - Mind-blowing design\n    const NotificationBadge = ()=>{\n        if (pendingOrders.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-6 right-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 hover:from-orange-600 hover:via-red-600 hover:to-pink-700 text-white px-8 py-6 rounded-3xl shadow-2xl cursor-pointer transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm\",\n                onClick: ()=>setShowModal(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-orange-400 via-red-400 to-pink-500 rounded-3xl blur-xl opacity-75 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                            className: \"w-7 h-7 text-white animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-12 h-12 border-2 border-white/50 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-black text-xl tracking-wide\",\n                                                children: \"طلبات جديدة عاجلة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-yellow-300 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/90 text-sm font-semibold\",\n                                        children: [\n                                            pendingOrders.length,\n                                            \" طلب يحتاج موافقة فورية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-black text-white\",\n                                            children: pendingOrders.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-14 h-14 bg-white/10 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-3 left-3 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Current Orders Button - Mind-blowing design\n    const CurrentOrdersButton = ()=>{\n        if (preparingOrders.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: fetchCurrentOrders,\n                className: \"relative bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-700 text-white px-8 py-6 rounded-3xl shadow-2xl transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-500 rounded-3xl blur-xl opacity-75 group-hover:opacity-90 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 group-hover:rotate-12 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-7 h-7 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-12 h-12 border-2 border-white/30 rounded-full animate-spin opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-black text-xl tracking-wide\",\n                                                children: \"الطلبات الحالية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-300 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/90 text-sm font-semibold\",\n                                        children: [\n                                            preparingOrders.length,\n                                            \" طلب قيد التحضير\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40 group-hover:bg-white/30 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-black text-white\",\n                                            children: preparingOrders.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-3 bg-white/40 rounded-full animate-bounce opacity-60\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 translate-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 -translate-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-3 left-3 w-1 h-1 bg-green-300 rounded-full animate-pulse opacity-80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 right-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionStatus, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationBadge, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrentOrdersButton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showModal,\n                onClose: ()=>setShowModal(false),\n                size: \"4xl\",\n                position: \"center\",\n                dismissible: false,\n                className: \"backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-0 rounded-2xl shadow-2xl overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                                className: \"border-b border-gray-100 bg-white rounded-t-2xl px-8 py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-semibold text-gray-900 mb-1\",\n                                                            children: \"طلبات جديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                pendingOrders.length,\n                                                                \" طلب يحتاج إلى موافقة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center shadow-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: pendingOrders.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                                className: \"max-h-[75vh] overflow-y-auto bg-gradient-to-br from-gray-50 to-white p-6\",\n                                children: pendingOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiCheckCircle, {\n                                            className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600\",\n                                            children: \"لا توجد طلبات في الانتظار\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: pendingOrders.map((order)=>{\n                                        var _order_Products;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-br from-white via-orange-50 to-red-50 rounded-3xl p-8 shadow-2xl border-2 border-orange-100 hover:shadow-3xl transition-all duration-500 transform hover:scale-[1.03] hover:rotate-1 group overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-orange-200 to-red-200 rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-15 group-hover:opacity-25 transition-opacity duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-between items-start mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-8 h-8 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 375,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 376,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 w-16 h-16 border-2 border-orange-300 rounded-2xl animate-ping opacity-50\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-orange-800\",\n                                                                                children: \"!\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3 mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-2xl font-black text-gray-900 tracking-wide\",\n                                                                                    children: [\n                                                                                        \"طلب رقم #\",\n                                                                                        order.OrderID\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 390,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex gap-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-2 h-2 bg-orange-400 rounded-full animate-bounce\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 394,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-2 h-2 bg-red-400 rounded-full animate-bounce\",\n                                                                                            style: {\n                                                                                                animationDelay: '0.2s'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 395,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-2 h-2 bg-pink-400 rounded-full animate-bounce\",\n                                                                                            style: {\n                                                                                                animationDelay: '0.4s'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 396,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 393,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 bg-blue-50 px-3 py-1 rounded-full border border-blue-200\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-blue-600\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 403,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 402,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-semibold text-blue-700\",\n                                                                                            children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(order.OrderDate), {\n                                                                                                addSuffix: true,\n                                                                                                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                                                            })\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 405,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 401,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                order.CustomerPhoneNum && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 bg-green-50 px-3 py-1 rounded-full border border-green-200\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-green-600\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 416,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-semibold text-green-700\",\n                                                                                            children: order.CustomerPhoneNum\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 418,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 414,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 400,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-end gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-orange-500 via-red-500 to-pink-600 text-white px-6 py-3 rounded-2xl text-sm font-bold shadow-xl\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 bg-white rounded-full animate-pulse shadow-lg\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 430,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-lg\",\n                                                                                        children: \"في الانتظار\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 bg-yellow-300 rounded-full animate-bounce\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 432,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 bg-gradient-to-r from-orange-400 via-red-400 to-pink-500 rounded-2xl blur-lg opacity-50 -z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right bg-green-50 px-4 py-3 rounded-2xl border-2 border-green-200 shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-3xl font-black text-green-600 mb-1\",\n                                                                            children: formatPrice(order.CartTotalPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-semibold text-green-700\",\n                                                                            children: \"المجموع الكلي\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                order.Note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"ملاحظات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 bg-gray-50 p-2 rounded\",\n                                                            children: order.Note\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3 text-blue-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: \"المنتجات المطلوبة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        ((_order_Products = order.Products) === null || _order_Products === void 0 ? void 0 : _order_Products.length) || 0,\n                                                                        \" منتج\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: order.Products && order.Products.length > 0 ? order.Products.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-16 h-16 rounded-xl overflow-hidden bg-gray-100\",\n                                                                                        children: item.Product.Image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            src: getImageUrl(item.Product.Image),\n                                                                                            alt: item.Product.ProductName,\n                                                                                            className: \"w-full h-full object-cover\",\n                                                                                            onError: (e)=>{\n                                                                                                const target = e.target;\n                                                                                                target.src = '/images/placeholder-food.jpg';\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 485,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-full h-full flex items-center justify-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-6 h-6 text-gray-400\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    fillRule: \"evenodd\",\n                                                                                                    d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                                                    clipRule: \"evenodd\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 497,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 496,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 495,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 483,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n                                                                                        children: item.Quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 min-w-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-start mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"font-semibold text-gray-900 truncate\",\n                                                                                                children: item.Product.ProductName\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 511,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-right ml-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        className: \"text-lg font-bold text-green-600\",\n                                                                                                        children: formatPrice(item.Product.Price * item.Quantity)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 515,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: [\n                                                                                                            formatPrice(item.Product.Price),\n                                                                                                            \" \\xd7 \",\n                                                                                                            item.Quantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 518,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 514,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 510,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    item.IngredientUsages && item.IngredientUsages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-medium text-gray-700 mb-2\",\n                                                                                                children: \"المكونات المختارة:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 527,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex flex-wrap gap-1\",\n                                                                                                children: item.IngredientUsages.map((usage, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(usage.IsNeeded ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"w-1.5 h-1.5 rounded-full mr-1 \".concat(usage.IsNeeded ? 'bg-green-500' : 'bg-red-500')\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                                lineNumber: 538,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            usage.Ingredient.IngredientName,\n                                                                                                            usage.IsNeeded ? ' ✓' : ' ✗'\n                                                                                                        ]\n                                                                                                    }, idx, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 530,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 528,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 526,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center py-8 bg-gray-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-gray-400 mx-auto mb-3\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500 font-medium\",\n                                                                        children: \"لا توجد منتجات في هذا الطلب\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                (order.RestaurantName || order.DriverFirstName || order.DriverLastName || order.DistanceInMeters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: \"معلومات إضافية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 space-y-3\",\n                                                            children: [\n                                                                order.RestaurantName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 579,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"المطعم:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: order.RestaurantName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (order.DriverFirstName || order.DriverLastName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 592,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"السائق:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"\".concat(order.DriverFirstName || '', \" \").concat(order.DriverLastName || '').trim()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                order.DistanceInMeters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 605,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 604,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"المسافة:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 607,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                (order.DistanceInMeters / 1000).toFixed(2),\n                                                                                \" كم\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-6 pt-6 border-t-2 border-gradient-to-r from-orange-200 to-red-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAcceptOrder(order.OrderID),\n                                                            disabled: processingOrderId === order.OrderID,\n                                                            className: \"relative flex-1 bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 hover:from-emerald-600 hover:via-green-600 hover:to-teal-700 text-white font-black py-6 px-8 rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-110 hover:-rotate-1 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none group overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-green-400 to-teal-500 rounded-2xl blur-xl opacity-75 group-hover:opacity-90 transition-opacity duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: processingOrderId === order.OrderID ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: \"جاري المعالجة...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 636,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\",\n                                                                                        style: {\n                                                                                            animationDelay: '0.2s'\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 637,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\",\n                                                                                        style: {\n                                                                                            animationDelay: '0.4s'\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 638,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiCheckCircle, {\n                                                                                        className: \"w-7 h-7 group-hover:scale-125 transition-transform duration-300\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 644,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute inset-0 w-7 h-7 bg-white rounded-full opacity-20 animate-ping\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 645,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl tracking-wide\",\n                                                                                children: \"قبول وبدء التحضير\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-3 h-3 bg-white rounded-full animate-pulse shadow-lg\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 648,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-3 left-3 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 655,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRejectOrder(order.OrderID),\n                                                            disabled: processingOrderId === order.OrderID,\n                                                            className: \"relative flex-1 bg-gradient-to-br from-red-500 via-rose-500 to-pink-600 hover:from-red-600 hover:via-rose-600 hover:to-pink-700 text-white font-black py-6 px-8 rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-110 hover:rotate-1 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none group overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-red-400 via-rose-400 to-pink-500 rounded-2xl blur-xl opacity-75 group-hover:opacity-90 transition-opacity duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: processingOrderId === order.OrderID ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: \"جاري المعالجة...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 674,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\",\n                                                                                        style: {\n                                                                                            animationDelay: '0.2s'\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 675,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-white rounded-full animate-bounce\",\n                                                                                        style: {\n                                                                                            animationDelay: '0.4s'\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 676,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 673,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                                                                        className: \"w-7 h-7 group-hover:scale-125 transition-transform duration-300\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 682,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute inset-0 w-7 h-7 bg-white rounded-full opacity-20 animate-ping\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 683,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl tracking-wide\",\n                                                                                children: \"رفض الطلبية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-3 h-3 bg-white rounded-full animate-pulse shadow-lg\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-3 left-3 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, order.OrderID, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Footer, {\n                        className: \"bg-white border-t border-gray-100 rounded-b-2xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    color: \"gray\",\n                                    onClick: refreshOrders,\n                                    disabled: isLoading,\n                                    children: [\n                                        isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 28\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 52\n                                        }, undefined),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    color: \"light\",\n                                    onClick: ()=>setShowModal(false),\n                                    children: \"إغلاق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 703,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showRejectConfirmModal,\n                onClose: ()=>setShowRejectConfirmModal(false),\n                size: \"md\",\n                position: \"center\",\n                className: \"backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                            className: \"border-b border-gray-100 bg-gradient-to-r from-red-50 to-red-100 rounded-t-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-red-900\",\n                                                children: \"تأكيد رفض الطلبية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-700\",\n                                                children: \"هذا الإجراء لا يمكن التراجع عنه\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                            className: \"w-8 h-8 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"هل أنت متأكد من رفض هذه الطلبية؟\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: \"سيتم إشعار العميل برفض الطلبية ولن تتمكن من قبولها مرة أخرى\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: confirmRejectOrder,\n                                                disabled: processingOrderId !== null,\n                                                className: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\",\n                                                children: processingOrderId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                            size: \"sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"جاري الرفض...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"نعم، رفض الطلبية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowRejectConfirmModal(false),\n                                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200\",\n                                                children: \"إلغاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 731,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showCurrentOrdersModal,\n                onClose: ()=>setShowCurrentOrdersModal(false),\n                size: \"5xl\",\n                position: \"center\",\n                className: \"backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 border-0 rounded-2xl shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                            className: \"border-b border-gray-100 bg-white rounded-t-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-1\",\n                                                        children: \"الطلبات الحالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-600\",\n                                                        children: [\n                                                            \"الطلبات قيد التحضير (\",\n                                                            currentOrders.length,\n                                                            \" طلب)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-400 to-indigo-500 text-white text-xl font-bold px-5 py-3 rounded-full shadow-lg\",\n                                                children: currentOrders.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                            className: \"max-h-[70vh] overflow-y-auto bg-gradient-to-br from-gray-50 to-white p-6\",\n                            children: currentOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 text-blue-500\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                        children: \"لا توجد طلبات قيد التحضير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"جميع الطلبات تم تحضيرها أو لا توجد طلبات مقبولة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: currentOrders.map((order)=>{\n                                    var _order_Products;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl p-6 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 845,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 mb-1\",\n                                                                        children: [\n                                                                            \"طلب رقم #\",\n                                                                            order.OrderID\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 857,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(order.OrderDate), {\n                                                                                            addSuffix: true,\n                                                                                            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 860,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            order.CustomerPhoneNum && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 870,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 869,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: order.CustomerPhoneNum\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 872,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 868,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-green-400 to-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg mb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 881,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"قيد التحضير\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 879,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: formatPrice(order.CartTotalPrice)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"المجموع الكلي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 text-green-600\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"المنتجات المطلوبة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full\",\n                                                                children: [\n                                                                    ((_order_Products = order.Products) === null || _order_Products === void 0 ? void 0 : _order_Products.length) || 0,\n                                                                    \" منتج\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: order.Products && order.Products.length > 0 ? order.Products.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 border border-gray-100 rounded-xl p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-16 h-16 rounded-xl overflow-hidden bg-gray-100\",\n                                                                                    children: item.Product.Image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: getImageUrl(item.Product.Image),\n                                                                                        alt: item.Product.ProductName,\n                                                                                        className: \"w-full h-full object-cover\",\n                                                                                        onError: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.src = '/images/placeholder-food.jpg';\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 918,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-6 h-6 text-gray-400\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 930,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 929,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 928,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 916,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n                                                                                    children: item.Quantity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 936,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                            className: \"font-semibold text-gray-900 truncate\",\n                                                                                            children: item.Product.ProductName\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 944,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-right ml-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                                    children: formatPrice(item.Product.Price * item.Quantity)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 948,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        formatPrice(item.Product.Price),\n                                                                                                        \" \\xd7 \",\n                                                                                                        item.Quantity\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 951,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 947,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 943,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                item.IngredientUsages && item.IngredientUsages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-medium text-gray-700 mb-2\",\n                                                                                            children: \"المكونات المختارة:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 960,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex flex-wrap gap-1\",\n                                                                                            children: item.IngredientUsages.map((usage, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(usage.IsNeeded ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"w-1.5 h-1.5 rounded-full mr-1 \".concat(usage.IsNeeded ? 'bg-green-500' : 'bg-red-500')\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                            lineNumber: 971,\n                                                                                                            columnNumber: 45\n                                                                                                        }, undefined),\n                                                                                                        usage.Ingredient.IngredientName,\n                                                                                                        usage.IsNeeded ? ' ✓' : ' ✗'\n                                                                                                    ]\n                                                                                                }, idx, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 963,\n                                                                                                    columnNumber: 43\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 961,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 29\n                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8 bg-gray-50 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-3\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 988,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 font-medium\",\n                                                                    children: \"لا توجد منتجات في هذا الطلب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 986,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, order.OrderID, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 824,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Footer, {\n                            className: \"bg-white border-t border-gray-100 rounded-b-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        color: \"gray\",\n                                        onClick: fetchCurrentOrders,\n                                        disabled: isLoading,\n                                        children: [\n                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                size: \"sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 54\n                                            }, undefined),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        color: \"light\",\n                                        onClick: ()=>setShowCurrentOrdersModal(false),\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 1011,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 1001,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 795,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 788,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(OrderNotificationOverlay, \"JxB6qRQkP96uprzgosg8sJ6dKaE=\", false, function() {\n    return [\n        _app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__.useOrderNotification\n    ];\n});\n_c = OrderNotificationOverlay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderNotificationOverlay);\nvar _c;\n$RefreshReg$(_c, \"OrderNotificationOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/shared/OrderNotificationOverlay.tsx\n"));

/***/ })

});
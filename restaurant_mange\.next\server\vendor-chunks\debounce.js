/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/debounce";
exports.ids = ["vendor-chunks/debounce"];
exports.modules = {

/***/ "(ssr)/./node_modules/debounce/index.js":
/*!****************************************!*\
  !*** ./node_modules/debounce/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("function debounce(function_, wait = 100, options = {}) {\n\tif (typeof function_ !== 'function') {\n\t\tthrow new TypeError(`Expected the first parameter to be a function, got \\`${typeof function_}\\`.`);\n\t}\n\n\tif (wait < 0) {\n\t\tthrow new RangeError('`wait` must not be negative.');\n\t}\n\n\t// TODO: Deprecate the boolean parameter at some point.\n\tconst {immediate} = typeof options === 'boolean' ? {immediate: options} : options;\n\n\tlet storedContext;\n\tlet storedArguments;\n\tlet timeoutId;\n\tlet timestamp;\n\tlet result;\n\n\tfunction run() {\n\t\tconst callContext = storedContext;\n\t\tconst callArguments = storedArguments;\n\t\tstoredContext = undefined;\n\t\tstoredArguments = undefined;\n\t\tresult = function_.apply(callContext, callArguments);\n\t\treturn result;\n\t}\n\n\tfunction later() {\n\t\tconst last = Date.now() - timestamp;\n\n\t\tif (last < wait && last >= 0) {\n\t\t\ttimeoutId = setTimeout(later, wait - last);\n\t\t} else {\n\t\t\ttimeoutId = undefined;\n\n\t\t\tif (!immediate) {\n\t\t\t\tresult = run();\n\t\t\t}\n\t\t}\n\t}\n\n\tconst debounced = function (...arguments_) {\n\t\tif (storedContext && this !== storedContext) {\n\t\t\tthrow new Error('Debounced method called with different contexts.');\n\t\t}\n\n\t\tstoredContext = this; // eslint-disable-line unicorn/no-this-assignment\n\t\tstoredArguments = arguments_;\n\t\ttimestamp = Date.now();\n\n\t\tconst callNow = immediate && !timeoutId;\n\n\t\tif (!timeoutId) {\n\t\t\ttimeoutId = setTimeout(later, wait);\n\t\t}\n\n\t\tif (callNow) {\n\t\t\tresult = run();\n\t\t}\n\n\t\treturn result;\n\t};\n\n\tdebounced.clear = () => {\n\t\tif (!timeoutId) {\n\t\t\treturn;\n\t\t}\n\n\t\tclearTimeout(timeoutId);\n\t\ttimeoutId = undefined;\n\t};\n\n\tdebounced.flush = () => {\n\t\tif (!timeoutId) {\n\t\t\treturn;\n\t\t}\n\n\t\tdebounced.trigger();\n\t};\n\n\tdebounced.trigger = () => {\n\t\tresult = run();\n\n\t\tdebounced.clear();\n\t};\n\n\treturn debounced;\n}\n\n// Adds compatibility for ES modules\nmodule.exports.debounce = debounce;\n\nmodule.exports = debounce;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGVib3VuY2UvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEscURBQXFEO0FBQ3JEO0FBQ0EsOEVBQThFLGlCQUFpQjtBQUMvRjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxRQUFRLFdBQVcsa0NBQWtDLG9CQUFvQjs7QUFFekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdCQUF3QjtBQUN4QjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLHVCQUF1Qjs7QUFFdkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRGVza3RvcFxcUmVzdCBEZWxpdmVyeVxccmVzdGF1cmFudF9tYW5nZVxcbm9kZV9tb2R1bGVzXFxkZWJvdW5jZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZGVib3VuY2UoZnVuY3Rpb25fLCB3YWl0ID0gMTAwLCBvcHRpb25zID0ge30pIHtcblx0aWYgKHR5cGVvZiBmdW5jdGlvbl8gIT09ICdmdW5jdGlvbicpIHtcblx0XHR0aHJvdyBuZXcgVHlwZUVycm9yKGBFeHBlY3RlZCB0aGUgZmlyc3QgcGFyYW1ldGVyIHRvIGJlIGEgZnVuY3Rpb24sIGdvdCBcXGAke3R5cGVvZiBmdW5jdGlvbl99XFxgLmApO1xuXHR9XG5cblx0aWYgKHdhaXQgPCAwKSB7XG5cdFx0dGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2B3YWl0YCBtdXN0IG5vdCBiZSBuZWdhdGl2ZS4nKTtcblx0fVxuXG5cdC8vIFRPRE86IERlcHJlY2F0ZSB0aGUgYm9vbGVhbiBwYXJhbWV0ZXIgYXQgc29tZSBwb2ludC5cblx0Y29uc3Qge2ltbWVkaWF0ZX0gPSB0eXBlb2Ygb3B0aW9ucyA9PT0gJ2Jvb2xlYW4nID8ge2ltbWVkaWF0ZTogb3B0aW9uc30gOiBvcHRpb25zO1xuXG5cdGxldCBzdG9yZWRDb250ZXh0O1xuXHRsZXQgc3RvcmVkQXJndW1lbnRzO1xuXHRsZXQgdGltZW91dElkO1xuXHRsZXQgdGltZXN0YW1wO1xuXHRsZXQgcmVzdWx0O1xuXG5cdGZ1bmN0aW9uIHJ1bigpIHtcblx0XHRjb25zdCBjYWxsQ29udGV4dCA9IHN0b3JlZENvbnRleHQ7XG5cdFx0Y29uc3QgY2FsbEFyZ3VtZW50cyA9IHN0b3JlZEFyZ3VtZW50cztcblx0XHRzdG9yZWRDb250ZXh0ID0gdW5kZWZpbmVkO1xuXHRcdHN0b3JlZEFyZ3VtZW50cyA9IHVuZGVmaW5lZDtcblx0XHRyZXN1bHQgPSBmdW5jdGlvbl8uYXBwbHkoY2FsbENvbnRleHQsIGNhbGxBcmd1bWVudHMpO1xuXHRcdHJldHVybiByZXN1bHQ7XG5cdH1cblxuXHRmdW5jdGlvbiBsYXRlcigpIHtcblx0XHRjb25zdCBsYXN0ID0gRGF0ZS5ub3coKSAtIHRpbWVzdGFtcDtcblxuXHRcdGlmIChsYXN0IDwgd2FpdCAmJiBsYXN0ID49IDApIHtcblx0XHRcdHRpbWVvdXRJZCA9IHNldFRpbWVvdXQobGF0ZXIsIHdhaXQgLSBsYXN0KTtcblx0XHR9IGVsc2Uge1xuXHRcdFx0dGltZW91dElkID0gdW5kZWZpbmVkO1xuXG5cdFx0XHRpZiAoIWltbWVkaWF0ZSkge1xuXHRcdFx0XHRyZXN1bHQgPSBydW4oKTtcblx0XHRcdH1cblx0XHR9XG5cdH1cblxuXHRjb25zdCBkZWJvdW5jZWQgPSBmdW5jdGlvbiAoLi4uYXJndW1lbnRzXykge1xuXHRcdGlmIChzdG9yZWRDb250ZXh0ICYmIHRoaXMgIT09IHN0b3JlZENvbnRleHQpIHtcblx0XHRcdHRocm93IG5ldyBFcnJvcignRGVib3VuY2VkIG1ldGhvZCBjYWxsZWQgd2l0aCBkaWZmZXJlbnQgY29udGV4dHMuJyk7XG5cdFx0fVxuXG5cdFx0c3RvcmVkQ29udGV4dCA9IHRoaXM7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgdW5pY29ybi9uby10aGlzLWFzc2lnbm1lbnRcblx0XHRzdG9yZWRBcmd1bWVudHMgPSBhcmd1bWVudHNfO1xuXHRcdHRpbWVzdGFtcCA9IERhdGUubm93KCk7XG5cblx0XHRjb25zdCBjYWxsTm93ID0gaW1tZWRpYXRlICYmICF0aW1lb3V0SWQ7XG5cblx0XHRpZiAoIXRpbWVvdXRJZCkge1xuXHRcdFx0dGltZW91dElkID0gc2V0VGltZW91dChsYXRlciwgd2FpdCk7XG5cdFx0fVxuXG5cdFx0aWYgKGNhbGxOb3cpIHtcblx0XHRcdHJlc3VsdCA9IHJ1bigpO1xuXHRcdH1cblxuXHRcdHJldHVybiByZXN1bHQ7XG5cdH07XG5cblx0ZGVib3VuY2VkLmNsZWFyID0gKCkgPT4ge1xuXHRcdGlmICghdGltZW91dElkKSB7XG5cdFx0XHRyZXR1cm47XG5cdFx0fVxuXG5cdFx0Y2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG5cdFx0dGltZW91dElkID0gdW5kZWZpbmVkO1xuXHR9O1xuXG5cdGRlYm91bmNlZC5mbHVzaCA9ICgpID0+IHtcblx0XHRpZiAoIXRpbWVvdXRJZCkge1xuXHRcdFx0cmV0dXJuO1xuXHRcdH1cblxuXHRcdGRlYm91bmNlZC50cmlnZ2VyKCk7XG5cdH07XG5cblx0ZGVib3VuY2VkLnRyaWdnZXIgPSAoKSA9PiB7XG5cdFx0cmVzdWx0ID0gcnVuKCk7XG5cblx0XHRkZWJvdW5jZWQuY2xlYXIoKTtcblx0fTtcblxuXHRyZXR1cm4gZGVib3VuY2VkO1xufVxuXG4vLyBBZGRzIGNvbXBhdGliaWxpdHkgZm9yIEVTIG1vZHVsZXNcbm1vZHVsZS5leHBvcnRzLmRlYm91bmNlID0gZGVib3VuY2U7XG5cbm1vZHVsZS5leHBvcnRzID0gZGVib3VuY2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/debounce/index.js\n");

/***/ })

};
;
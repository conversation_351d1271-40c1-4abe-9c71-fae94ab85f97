import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:uuid/uuid.dart';

class CartStorageService {
  // Keys for SharedPreferences - now restaurant-specific
  static const String _cartItemsKeyPrefix = 'cart_items_restaurant_';

  // Singleton instance
  static CartStorageService? _instance;

  // SharedPreferences instance
  late SharedPreferences _prefs;

  // Private constructor
  CartStorageService._();

  // Factory constructor to return the same instance
  factory CartStorageService() {
    _instance ??= CartStorageService._();
    return _instance!;
  }

  // Initialize the service
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get cart key for specific restaurant
  String _getCartKey(int restaurantId) {
    return '$_cartItemsKeyPrefix$restaurantId';
  }

  // Get all cart items for all restaurants
  Future<List<CartItem>> getCartItems() async {
    List<CartItem> allCartItems = [];

    // Get all keys that start with our cart prefix
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cartItemsKeyPrefix));

    for (String key in keys) {
      final String? cartItemsJson = _prefs.getString(key);
      if (cartItemsJson != null && cartItemsJson.isNotEmpty) {
        try {
          final List<dynamic> itemsJson = jsonDecode(cartItemsJson);
          final List<CartItem> restaurantCartItems = itemsJson.map((itemJson) {
            return CartItem.fromJson(itemJson);
          }).toList();
          allCartItems.addAll(restaurantCartItems);
        } catch (e) {
          debugPrint('ERROR: Failed to parse cart items JSON for key $key: $e');
          // Clear corrupted data
          await _prefs.remove(key);
        }
      }
    }

    debugPrint('DEBUG: Successfully loaded ${allCartItems.length} cart items from all restaurants');
    return allCartItems;
  }

  // Get cart items for specific restaurant
  Future<List<CartItem>> getCartItemsForRestaurant(int restaurantId) async {
    final String cartKey = _getCartKey(restaurantId);
    final String? cartItemsJson = _prefs.getString(cartKey);

    if (cartItemsJson == null || cartItemsJson.isEmpty) {
      debugPrint('DEBUG: No cart items found for restaurant $restaurantId');
      return [];
    }

    try {
      debugPrint('DEBUG: Found cart items JSON for restaurant $restaurantId: $cartItemsJson');
      final List<dynamic> itemsJson = jsonDecode(cartItemsJson);
      debugPrint('DEBUG: Decoded ${itemsJson.length} items for restaurant $restaurantId');

      final List<CartItem> cartItems = itemsJson.map((itemJson) {
        debugPrint('DEBUG: Processing item: $itemJson');
        return CartItem.fromJson(itemJson);
      }).toList();

      debugPrint('DEBUG: Successfully loaded ${cartItems.length} cart items for restaurant $restaurantId');
      return cartItems;
    } catch (e) {
      debugPrint('ERROR: Failed to parse cart items JSON for restaurant $restaurantId: $e');
      // Clear corrupted data
      await _prefs.remove(cartKey);
      return [];
    }
  }

  // Save cart items for specific restaurant
  Future<void> saveCartItemsForRestaurant(int restaurantId, List<CartItem> cartItems) async {
    final String cartKey = _getCartKey(restaurantId);
    debugPrint('DEBUG: Saving ${cartItems.length} cart items for restaurant $restaurantId');
    final List<Map<String, dynamic>> itemsJson = cartItems.map((item) => item.toJson()).toList();
    debugPrint('DEBUG: Items JSON: $itemsJson');
    final String encodedList = jsonEncode(itemsJson);
    debugPrint('DEBUG: Encoded JSON: $encodedList');
    await _prefs.setString(cartKey, encodedList);

    // Verify the save worked
    final String? savedJson = _prefs.getString(cartKey);
    debugPrint('DEBUG: Saved JSON for restaurant $restaurantId: $savedJson');
  }

  // Save cart items (legacy method - now saves to restaurant-specific storage)
  Future<void> saveCartItems(List<CartItem> cartItems) async {
    if (cartItems.isEmpty) {
      debugPrint('DEBUG: No cart items to save');
      return;
    }

    // Group items by restaurant
    final Map<int, List<CartItem>> restaurantGroups = {};
    for (final item in cartItems) {
      restaurantGroups.putIfAbsent(item.restaurantId, () => []);
      restaurantGroups[item.restaurantId]!.add(item);
    }

    // Save each restaurant's cart separately
    for (final entry in restaurantGroups.entries) {
      await saveCartItemsForRestaurant(entry.key, entry.value);
    }
  }

  // Add item to cart for specific restaurant
  Future<void> addToCart(Product product, int quantity, List<SelectedIngredient>? selectedIngredients) async {
    debugPrint('DEBUG: Adding product to cart: ${product.id} - ${product.name}');
    debugPrint('DEBUG: Product details: price=${product.price}, image=${product.image}, restaurantId=${product.restaurantId}');

    final int restaurantId = product.restaurantId ?? 0;
    if (restaurantId == 0) {
      throw Exception('Product must have a valid restaurant ID');
    }

    // Check if there are items from different restaurants and clear them automatically
    final allCartItems = await getCartItems();
    final productRestaurantId = product.restaurantId ?? 0;

    if (allCartItems.isNotEmpty) {
      final existingRestaurantId = allCartItems.first.restaurantId;

      if (existingRestaurantId != productRestaurantId) {
        debugPrint('DEBUG: Different restaurant detected. Clearing all existing carts and adding new item from restaurant $productRestaurantId');
        // Clear all existing carts from other restaurants
        await clearCart();
      }
    }

    // Now get the current cart for this restaurant (should be empty if we cleared above)
    final List<CartItem> currentCart = await getCartItemsForRestaurant(restaurantId);
    debugPrint('DEBUG: Current cart for restaurant $restaurantId has ${currentCart.length} items');

    // Generate a unique cart item ID
    const uuid = Uuid();
    final cartItemId = int.parse(uuid.v4().substring(0, 8), radix: 16);
    debugPrint('DEBUG: Generated cart item ID: $cartItemId');

    // Create new cart item
    final cartItem = CartItem(
      id: cartItemId,
      productId: product.id,
      productName: product.name,
      productImage: product.image,
      price: product.price,
      quantity: quantity,
      restaurantId: product.restaurantId ?? 0,
      restaurantName: product.restaurantName ?? 'Restaurant',
      selectedIngredients: selectedIngredients,
    );

    debugPrint('DEBUG: Created cart item: id=${cartItem.id}, name=${cartItem.productName}, price=${cartItem.price}');

    // Add to cart
    currentCart.add(cartItem);
    debugPrint('DEBUG: Added item to cart. New cart size: ${currentCart.length}');
    await saveCartItemsForRestaurant(restaurantId, currentCart);
  }

  // Update cart item quantity
  Future<void> updateCartItemQuantity(int cartItemId, int quantity) async {
    // Find the item in all restaurant carts
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cartItemsKeyPrefix));

    for (String key in keys) {
      final String? cartItemsJson = _prefs.getString(key);
      if (cartItemsJson != null && cartItemsJson.isNotEmpty) {
        try {
          final List<dynamic> itemsJson = jsonDecode(cartItemsJson);
          final List<CartItem> currentCart = itemsJson.map((itemJson) {
            return CartItem.fromJson(itemJson);
          }).toList();

          final int index = currentCart.indexWhere((item) => item.id == cartItemId);
          if (index != -1) {
            // Found the item, update it
            final CartItem oldItem = currentCart[index];
            final CartItem updatedItem = CartItem(
              id: oldItem.id,
              productId: oldItem.productId,
              productName: oldItem.productName,
              productImage: oldItem.productImage,
              price: oldItem.price,
              quantity: quantity,
              restaurantId: oldItem.restaurantId,
              restaurantName: oldItem.restaurantName,
              selectedIngredients: oldItem.selectedIngredients,
            );

            // Replace item in cart
            currentCart[index] = updatedItem;
            await saveCartItemsForRestaurant(oldItem.restaurantId, currentCart);
            return;
          }
        } catch (e) {
          debugPrint('ERROR: Failed to parse cart items JSON for key $key: $e');
        }
      }
    }

    throw Exception('Cart item not found');
  }

  // Remove item from cart
  Future<void> removeCartItem(int cartItemId) async {
    // Find the item in all restaurant carts
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cartItemsKeyPrefix));

    for (String key in keys) {
      final String? cartItemsJson = _prefs.getString(key);
      if (cartItemsJson != null && cartItemsJson.isNotEmpty) {
        try {
          final List<dynamic> itemsJson = jsonDecode(cartItemsJson);
          final List<CartItem> currentCart = itemsJson.map((itemJson) {
            return CartItem.fromJson(itemJson);
          }).toList();

          final int index = currentCart.indexWhere((item) => item.id == cartItemId);
          if (index != -1) {
            // Found the item, remove it
            final CartItem itemToRemove = currentCart[index];
            final List<CartItem> updatedCart = currentCart.where((item) => item.id != cartItemId).toList();
            await saveCartItemsForRestaurant(itemToRemove.restaurantId, updatedCart);
            return;
          }
        } catch (e) {
          debugPrint('ERROR: Failed to parse cart items JSON for key $key: $e');
        }
      }
    }

    throw Exception('Cart item not found');
  }

  // Clear cart for specific restaurant
  Future<void> clearCartForRestaurant(int restaurantId) async {
    final String cartKey = _getCartKey(restaurantId);
    await _prefs.remove(cartKey);
    debugPrint('DEBUG: Cleared cart for restaurant $restaurantId');
  }

  // Clear all carts
  Future<void> clearCart() async {
    // Get all cart keys and remove them
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cartItemsKeyPrefix));
    for (String key in keys) {
      await _prefs.remove(key);
    }
    debugPrint('DEBUG: Cleared all restaurant carts');
  }

  // Get cart item count for specific restaurant
  Future<int> getCartItemCountForRestaurant(int restaurantId) async {
    final List<CartItem> cartItems = await getCartItemsForRestaurant(restaurantId);
    return cartItems.fold<int>(0, (total, item) => total + item.quantity);
  }

  // Check if restaurant has items in cart
  Future<bool> hasItemsForRestaurant(int restaurantId) async {
    final List<CartItem> cartItems = await getCartItemsForRestaurant(restaurantId);
    return cartItems.isNotEmpty;
  }

  // Calculate cart total for specific restaurant
  Future<double> getCartTotalForRestaurant(int restaurantId) async {
    final List<CartItem> cartItems = await getCartItemsForRestaurant(restaurantId);
    double currentTotal = 0.0;
    for (final item in cartItems) {
      // Simply multiply price by quantity
      currentTotal += item.price * item.quantity;
    }
    return currentTotal;
  }

  // Calculate cart total for all restaurants
  Future<double> getCartTotal() async {
    final List<CartItem> cartItems = await getCartItems();
    double currentTotal = 0.0;
    for (final item in cartItems) {
      // Simply multiply price by quantity
      currentTotal += item.price * item.quantity;
    }
    debugPrint('DEBUG: Cart total: $currentTotal');
    return currentTotal;
  }

  // Check if a product is already in the cart
  Future<bool> isProductInCart(int productId) async {
    final List<CartItem> cartItems = await getCartItems();
    return cartItems.any((item) => item.productId == productId);
  }

  // Check if a product is from a different restaurant than what's in the cart
  Future<bool> isFromDifferentRestaurant(int restaurantId) async {
    final List<CartItem> cartItems = await getCartItems();

    // If cart is empty, product can be added
    if (cartItems.isEmpty) {
      debugPrint('DEBUG: Cart is empty, can add product from any restaurant');
      return false;
    }

    // Check if restaurant ID matches the first item in cart
    final cartRestaurantId = cartItems.first.restaurantId;
    final cartRestaurantName = cartItems.first.restaurantName;

    debugPrint('DEBUG: Checking if product is from different restaurant');
    debugPrint('DEBUG: Cart restaurant: ID=$cartRestaurantId, Name=$cartRestaurantName');
    debugPrint('DEBUG: Product restaurant ID=$restaurantId');
    debugPrint('DEBUG: Types - cart=${cartRestaurantId.runtimeType}, product=${restaurantId.runtimeType}');

    final isDifferent = cartRestaurantId != restaurantId;
    debugPrint('DEBUG: Is from different restaurant? $isDifferent');

    return isDifferent;
  }

  // Get the current restaurant in cart (if any)
  Future<Map<String, dynamic>?> getCurrentRestaurantInCart() async {
    final List<CartItem> cartItems = await getCartItems();

    if (cartItems.isEmpty) {
      return null;
    }

    return {
      'id': cartItems.first.restaurantId,
      'name': cartItems.first.restaurantName,
    };
  }
}

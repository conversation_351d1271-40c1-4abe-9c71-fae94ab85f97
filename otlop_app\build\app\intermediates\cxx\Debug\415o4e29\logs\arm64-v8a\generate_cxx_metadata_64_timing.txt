# C/C++ build system timings
generate_cxx_metadata
  [gap of 89ms]
  create-invalidation-state 422ms
  [gap of 77ms]
  write-metadata-json-to-file 28ms
generate_cxx_metadata completed in 617ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 60ms
  [gap of 47ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 142ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 33ms]
  create-invalidation-state 97ms
  [gap of 50ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 194ms


import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/login_screen.dart';
import '../screens/delivery_screen.dart';
import '../screens/orders_screen.dart';
import '../screens/history_screen.dart';
import '../screens/wallet_screen.dart';
import '../screens/profile_screen.dart';
import '../services/api_client.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;
  String? _newsMessage;
  Map<String, dynamic>? _activeOrder;

  @override
  void initState() {
    super.initState();
    _loadNews();
    _checkActiveOrder();
  }

  Future<void> _loadNews() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/drivers/app/moderate');

      if (response['success'] && response['data']['news'] != null) {
        setState(() {
          _newsMessage = response['data']['news'];
        });
      }
    } catch (e) {
      // Error loading news - fail silently
    }
  }

  Future<void> _checkActiveOrder() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/drivers/app/active-order');

      if (response != null && response['hasActiveOrder'] == true) {
        setState(() {
          _activeOrder = response['data'];
        });
      } else {
        setState(() {
          _activeOrder = null;
        });
      }
    } catch (e) {
      // Error loading active order - fail silently
      setState(() {
        _activeOrder = null;
      });
    }
  }





  // List of screens for bottom navigation
  List<Widget> get _screens => [
    _buildDeliveryTab(),
    const OrdersScreen(),
    const HistoryScreen(),
    const WalletScreen(),
    const ProfileScreen(),
  ];

  // Build delivery tab - shows active delivery or placeholder
  Widget _buildDeliveryTab() {
    if (_activeOrder != null) {
      return DeliveryScreen(order: _activeOrder!);
    } else {
      return Scaffold(
        body: Container(
          color: Colors.grey[50],
          child: SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.purple[50],
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Icon(
                      Icons.delivery_dining,
                      size: 80,
                      color: Colors.purple[400],
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'لا توجد طلبات قيد التوصيل',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'اقبل طلباً من تبويب الطلبات لبدء التوصيل',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _currentIndex = 1; // Switch to Orders tab
                      });
                    },
                    icon: const Icon(Icons.search),
                    label: const Text('البحث عن طلبات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextButton.icon(
                    onPressed: _checkActiveOrder,
                    icon: const Icon(Icons.refresh),
                    label: const Text('تحديث'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.purple[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
  }







  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    if (!authProvider.isAuthenticated) {
      return LoginScreen();
    }

    return Scaffold(
      body: Column(
        children: [
          // News Banner
          if (_newsMessage != null && _newsMessage!.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue[600]!, Colors.blue[800]!],
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Text(
                  _newsMessage!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

          // Main Content
          Expanded(
            child: _screens[_currentIndex],
          ),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.purple.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() => _currentIndex = index);
            // Refresh active order when switching to delivery tab
            if (index == 0) {
              _checkActiveOrder();
            }
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: Colors.purple[600],
          unselectedItemColor: Colors.grey[500],
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.delivery_dining),
              activeIcon: Icon(Icons.delivery_dining),
              label: 'التوصيل',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.assignment),
              activeIcon: Icon(Icons.assignment),
              label: 'الطلبات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.history),
              activeIcon: Icon(Icons.history),
              label: 'السجل',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.account_balance_wallet),
              activeIcon: Icon(Icons.account_balance_wallet),
              label: 'المحفظة',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person),
              activeIcon: Icon(Icons.person),
              label: 'الرئيسية',
            ),
          ],
        ),
      ),
    );
  }
}
import 'package:flutter/foundation.dart';
import 'package:otlop_app/services/api_service.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/models/restaurant_model.dart';
import 'package:otlop_app/models/working_time_model.dart' as working_time;

class RestaurantService {
  final ApiService _apiService;

  // Base URL for images
  String get baseUrl => ApiService.baseUrl;

  // Helper method to format image URLs
  String getImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return ''; // Return empty string for empty images, let UI handle fallback
    }

    // If it's already a full URL, return it as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }

  // Alternative method to fix image URLs if the first method fails
  String fixImageUrl(String url) {
    // If it's already a full URL, return it as is
    if (url.startsWith('http')) {
      return url;
    }

    // For restaurant images, use the specific path format
    return '${ApiService.baseUrl}/$url';
  }

  // Constructor
  RestaurantService({ApiService? apiService}) : _apiService = apiService ?? ApiService();

  // Get all restaurants with pagination support
  Future<Map<String, dynamic>> getAllRestaurants({
    int page = 1,
    int limit = 10,
    double? userLat,
    double? userLng
  }) async {
    try {
      // Build URL with optional user location parameters
      String url = 'restaurants?page=$page&limit=$limit';
      if (userLat != null && userLng != null) {
        url += '&userLat=$userLat&userLng=$userLng';
      }

      // Use the retry mechanism in the API service (2 retries = 3 total attempts)
      final response = await _apiService.get(url, retries: 2);

      // Debug the response structure
      debugPrint('API Response: $response');

      // Ensure response['data'] is a List
      final data = response['data'];
      if (data == null) {
        throw Exception('No data received from API');
      }

      if (data is! List) {
        debugPrint('Data is not a List, it is: ${data.runtimeType}');
        throw Exception('Invalid data format from API - expected List, got ${data.runtimeType}');
      }

      // Convert each item to Map<String, dynamic> with better error handling
      final List<Map<String, dynamic>> restaurants = [];

      for (int i = 0; i < data.length; i++) {
        try {
          final item = data[i];
          Map<String, dynamic> restaurantMap;

          if (item is Map<String, dynamic>) {
            restaurantMap = item;
          } else if (item is Map) {
            // Convert dynamic keys to String keys safely
            restaurantMap = <String, dynamic>{};
            item.forEach((key, value) {
              if (key != null) {
                restaurantMap[key.toString()] = value;
              }
            });
          } else {
            debugPrint('Skipping invalid restaurant item at index $i: ${item.runtimeType}');
            continue;
          }

          // Process image URL
          if (restaurantMap['Image'] != null) {
            restaurantMap['image'] = getImageUrl(restaurantMap['Image'].toString());
          } else {
            restaurantMap['image'] = '$baseUrl/uploads/restaurants/default-restaurant.png';
          }

          restaurants.add(restaurantMap);
        } catch (itemError) {
          debugPrint('Error processing restaurant item at index $i: $itemError');
          // Continue processing other items instead of failing completely
          continue;
        }
      }

      if (restaurants.isEmpty && data.isNotEmpty) {
        throw Exception('No valid restaurant data could be processed');
      }

      debugPrint('Successfully processed ${restaurants.length} restaurants');

      // Return both data and pagination info
      return {
        'data': restaurants,
        'pagination': response['pagination'] ?? {
          'currentPage': page,
          'totalPages': 1,
          'totalItems': restaurants.length,
          'itemsPerPage': limit,
        }
      };
    } catch (e) {
      // Log the error for debugging
      debugPrint('Error in getAllRestaurants: ${e.toString()}');
      rethrow; // Re-throw the original exception for better error handling
    }
  }

  // Legacy method for backward compatibility
  Future<List<Map<String, dynamic>>> getAllRestaurantsLegacy() async {
    final result = await getAllRestaurants();
    return List<Map<String, dynamic>>.from(result['data']);
  }

  // Get restaurant by ID
  Future<Map<String, dynamic>> getRestaurantById(String restaurantId) async {
    try {
      final response = await _apiService.get('restaurants/$restaurantId');
      final data = response['data'] ?? {};

      // Ensure image URL is properly formatted
      if (data['Image'] != null) {
        // Set the image field to the properly formatted URL
        data['image'] = getImageUrl(data['Image']);
      } else {
        // If no image is provided, use a default
        data['image'] = '$baseUrl/uploads/restaurants/default-restaurant.png';
      }

      return data;
    } catch (e) {
      throw Exception('Failed to load restaurant details: ${e.toString()}');
    }
  }

  // Get restaurant menu
  Future<List<dynamic>> getRestaurantMenu(String restaurantId) async {
    try {
      final response = await _apiService.get('restaurants/$restaurantId/');
      final menuData = response['data'] ?? [];

      // Process menu items to ensure image URLs are properly formatted
      for (var item in menuData) {
        if (item['image'] != null) {
          item['image'] = getImageUrl(item['image']);
        } else if (item['Image'] != null) {
          item['image'] = getImageUrl(item['Image']);
        }

        // Also use ImageUrl if available
        if (item['ImageUrl'] != null) {
          item['image'] = item['ImageUrl'];
        }
      }

      return menuData;
    } catch (e) {
      throw Exception('Failed to load restaurant menu: ${e.toString()}');
    }
  }

  // Get restaurant products (menu items)
  Future<List<Product>> getRestaurantProducts(String restaurantId) async {
    try {
      final response = await _apiService.get('products/restaurant/$restaurantId');

      // Check if the response has a data property that contains categories
      if (response['data'] != null && response['data'] is List) {
        final List<dynamic> categories = response['data'];

        // Check if the response contains categories with products
        if (categories.isNotEmpty && categories[0]['products'] != null) {
          // Flatten all products from all categories
          final List<Product> allProducts = [];

          for (var category in categories) {
            final String categoryName = category['CategoryName'] ?? 'Uncategorized';
            final int categoryId = category['CategoryID'] ?? 0;
            final List<dynamic> categoryProducts = category['products'] ?? [];

            for (var productJson in categoryProducts) {
              // Add category name and ID to each product
              productJson['categoryName'] = categoryName;
              productJson['categoryId'] = categoryId;

              // Process image URL - use the Image field directly
              if (productJson['Image'] != null) {
                String imageUrl = productJson['Image'].toString();

                // If it's a relative path, make it absolute
                if (!imageUrl.startsWith('http')) {
                  // If it starts with a slash, append it to the base URL
                  if (imageUrl.startsWith('/')) {
                    productJson['image'] = '$baseUrl$imageUrl';
                  } else {
                    // Otherwise, append it to the base URL with a slash
                    productJson['image'] = '$baseUrl/$imageUrl';
                  }
                } else {
                  // If it's a full URL but contains localhost, replace with the actual base URL
                  if (imageUrl.contains('localhost')) {
                    final uri = Uri.parse(imageUrl);
                    productJson['image'] = '$baseUrl${uri.path}';
                  } else {
                    productJson['image'] = imageUrl;
                  }
                }
              }

              // Also use ImageUrl if available (this might be a full URL with localhost)
              if (productJson['ImageUrl'] != null) {
                String imageUrl = productJson['ImageUrl'].toString();

                // If it contains localhost, replace with the actual base URL
                if (imageUrl.contains('localhost')) {
                  final uri = Uri.parse(imageUrl);
                  productJson['image'] = '$baseUrl${uri.path}';
                } else {
                  productJson['image'] = imageUrl;
                }
              }

              // Set availability status
              productJson['available'] = productJson['AvailabilityStatus'] == 1;

              try {
                final product = Product.fromJson(productJson);
                allProducts.add(product);
              } catch (e) {
                debugPrint('Error parsing product: $e');
                // Continue to next product
              }
            }
          }

          return allProducts;
        }
      }

      // Fallback to old format if the new format is not detected
      final List<dynamic> data = response['data'] ?? [];

      // Process product items to ensure image URLs are properly formatted
      for (var item in data) {
        if (item['image'] != null) {
          item['image'] = getImageUrl(item['image']);
        } else if (item['Image'] != null) {
          item['image'] = getImageUrl(item['Image']);
        }

        // Also use ImageUrl if available
        if (item['ImageUrl'] != null) {
          item['image'] = item['ImageUrl'];
        }
      }

      final products = data.map((json) => Product.fromJson(json)).toList();
      return products;
    } catch (e) {
      debugPrint('Error fetching restaurant products: $e');
      // Return empty list on error
      return [];
    }
  }

  // Get restaurant by category
  Future<List<dynamic>> getRestaurantsByCategory(String categoryId) async {
    try {
      final response = await _apiService.get('restaurants/category/$categoryId');
      return response['data'] ?? [];
    } catch (e) {
      throw Exception('Failed to load restaurants by category: ${e.toString()}');
    }
  }

  // Search restaurants
  Future<List<dynamic>> searchRestaurants(String query) async {
    try {
      // Ensure the query is properly encoded for URL
      final encodedQuery = Uri.encodeComponent(query);
      final response = await _apiService.get('restaurants/search?name=$encodedQuery');

      final List<dynamic> restaurants = response['data'] ?? [];

      // Process restaurant items to ensure image URLs are properly formatted
      for (var restaurant in restaurants) {
        // Use the Image field from the API response
        if (restaurant['Image'] != null) {
          // Set the image field to the properly formatted URL
          restaurant['image'] = getImageUrl(restaurant['Image']);
        } else {
          // If no image is provided, use a default
          restaurant['image'] = '$baseUrl/uploads/restaurants/default-restaurant.png';
        }
      }

      return restaurants;
    } catch (e) {
      throw Exception('Failed to search restaurants: ${e.toString()}');
    }
  }

  // Get restaurant working hours
  Future<List<working_time.WorkingTime>> getRestaurantWorkingHours(String restaurantId) async {
    try {
      final response = await _apiService.get('restaurants/$restaurantId/working-times');
      debugPrint('Working hours response type: ${response.runtimeType}');

      List<dynamic> workingTimesData = [];

      // Handle different response formats
      if (response is List) {
        // If the response is directly a list
        workingTimesData = response;
      } else if (response is Map) {
        // If the response is a map, check for data property
        if (response['data'] != null && response['data'] is List) {
          workingTimesData = response['data'];
        } else if (response['isArray'] == true && response['data'] is List) {
          // Handle the wrapped array from our API service
          workingTimesData = response['data'];
        }
      }

      // Convert the data to WorkingTime objects
      return workingTimesData.map((item) {
        try {
          if (item is Map<String, dynamic>) {
            return working_time.WorkingTime.fromJson(item);
          } else {
            debugPrint('Invalid working time item format: $item');
            throw Exception('Invalid working time item format');
          }
        } catch (e) {
          debugPrint('Error parsing working time item: $e');
          throw Exception('Error parsing working time item: $e');
        }
      }).toList();
    } catch (e) {
      // Log error but don't crash the app
      debugPrint('Error fetching restaurant working hours: $e');
      return [];
    }
  }
}
globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(auth)/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/Accordion.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Accordion/Accordion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionContent.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionPanel.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionTitle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Banner/BannerCollapseButton.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Banner/BannerCollapseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Carousel/Carousel.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Carousel/Carousel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/Clipboard.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Clipboard/Clipboard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIcon.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIconText.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIconText.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/DarkThemeToggle/DarkThemeToggle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/DarkThemeToggle/DarkThemeToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Datepicker/Datepicker.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Datepicker/Datepicker.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/Drawer.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Drawer/Drawer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerHeader.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerItems.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/Dropdown.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Dropdown/Dropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownDivider.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownDivider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownHeader.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownItem.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Floating/Floating.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Floating/Floating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenu.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenu.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdown.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdownToggle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdownToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/Modal.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Modal/Modal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalBody.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalFooter.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalHeader.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/Navbar.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Navbar/Navbar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarBrand.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarBrand.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarCollapse.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarCollapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarLink.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarLink.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarToggle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Popover/Popover.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Popover/Popover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/Rating.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Rating/Rating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/RatingStar.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Rating/RatingStar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/Sidebar.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/Sidebar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCollapse.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCollapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCTA.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCTA.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItem.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItemGroup.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItemGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItems.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarLogo.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarLogo.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/Table.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/Table.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableBody.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/TableBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableCell.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/TableCell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHead.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/TableHead.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHeadCell.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/TableHeadCell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableRow.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Table/TableRow.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/TabItem.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Tabs/TabItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/Tabs.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Tabs/Tabs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/Timeline.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/Timeline.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineBody.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineContent.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineItem.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelinePoint.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelinePoint.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTime.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTime.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTitle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/Toast.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Toast/Toast.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/ToastToggle.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/components/Toast/ToastToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/hooks/use-theme-mode.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/hooks/use-theme-mode.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/client.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/theme-store/init/client.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/mode.mjs":{"*":{"id":"(ssr)/./node_modules/flowbite-react/dist/esm/theme-store/init/mode.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./public/images/backgrounds/errorimg.svg":{"*":{"id":"(ssr)/./public/images/backgrounds/errorimg.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/page.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(ssr)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/working-hours/working-hours-management.tsx":{"*":{"id":"(ssr)/./src/app/components/working-hours/working-hours-management.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/product-management/product-list.tsx":{"*":{"id":"(ssr)/./src/app/components/product-management/product-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/accounts/restaurant/restaurant-account-list.tsx":{"*":{"id":"(ssr)/./src/app/components/accounts/restaurant/restaurant-account-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/add/page.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/accounts/add/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/orders/order-list.tsx":{"*":{"id":"(ssr)/./src/app/components/orders/order-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/edit/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/(DashboardLayout)/accounts/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Accordion\\Accordion.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/Accordion.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Accordion\\AccordionContent.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionContent.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Accordion\\AccordionPanel.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionPanel.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Accordion\\AccordionTitle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionTitle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Banner\\BannerCollapseButton.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Banner/BannerCollapseButton.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Carousel\\Carousel.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Carousel/Carousel.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Clipboard\\Clipboard.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/Clipboard.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Clipboard\\ClipboardWithIcon.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIcon.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Clipboard\\ClipboardWithIconText.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIconText.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\DarkThemeToggle\\DarkThemeToggle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/DarkThemeToggle/DarkThemeToggle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Datepicker\\Datepicker.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Datepicker/Datepicker.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Drawer\\Drawer.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/Drawer.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Drawer\\DrawerHeader.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Drawer\\DrawerItems.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerItems.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Dropdown\\Dropdown.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/Dropdown.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Dropdown\\DropdownDivider.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownDivider.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Dropdown\\DropdownHeader.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownHeader.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Dropdown\\DropdownItem.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownItem.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Floating\\Floating.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Floating/Floating.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\MegaMenu\\MegaMenu.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenu.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\MegaMenu\\MegaMenuDropdown.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdown.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\MegaMenu\\MegaMenuDropdownToggle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdownToggle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Modal\\Modal.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/Modal.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Modal\\ModalBody.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalBody.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Modal\\ModalFooter.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalFooter.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Modal\\ModalHeader.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Navbar\\Navbar.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/Navbar.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Navbar\\NavbarBrand.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarBrand.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Navbar\\NavbarCollapse.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarCollapse.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Navbar\\NavbarLink.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarLink.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Navbar\\NavbarToggle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarToggle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Popover\\Popover.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Popover/Popover.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Rating\\Rating.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/Rating.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Rating\\RatingStar.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/RatingStar.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\Sidebar.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/Sidebar.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarCollapse.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCollapse.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarCTA.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCTA.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarItem.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItem.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarItemGroup.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItemGroup.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarItems.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItems.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Sidebar\\SidebarLogo.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarLogo.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\Table.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/Table.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\TableBody.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableBody.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\TableCell.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableCell.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\TableHead.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHead.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\TableHeadCell.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHeadCell.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Table\\TableRow.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableRow.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Tabs\\TabItem.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/TabItem.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Tabs\\Tabs.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/Tabs.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\Timeline.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/Timeline.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelineBody.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineBody.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelineContent.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineContent.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelineItem.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineItem.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelinePoint.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelinePoint.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelineTime.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTime.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Timeline\\TimelineTitle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTitle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Toast\\Toast.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/Toast.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\components\\Toast\\ToastToggle.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/ToastToggle.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\hooks\\use-theme-mode.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/hooks/use-theme-mode.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\theme-store\\init\\client.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/client.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\flowbite-react\\dist\\esm\\theme-store\\init\\mode.mjs":{"id":"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/mode.mjs","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\public\\images\\backgrounds\\errorimg.svg":{"id":"(app-pages-browser)/./public/images/backgrounds/errorimg.svg","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx","name":"*","chunks":["app/(DashboardLayout)/layout","static/chunks/app/(DashboardLayout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\page.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/page.tsx","name":"*","chunks":["app/(DashboardLayout)/page","static/chunks/app/(DashboardLayout)/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(auth)\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/(auth)/login/page.tsx","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\components\\working-hours\\working-hours-management.tsx":{"id":"(app-pages-browser)/./src/app/components/working-hours/working-hours-management.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\components\\product-management\\product-list.tsx":{"id":"(app-pages-browser)/./src/app/components/product-management/product-list.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\components\\accounts\\restaurant\\restaurant-account-list.tsx":{"id":"(app-pages-browser)/./src/app/components/accounts/restaurant/restaurant-account-list.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\accounts\\add\\page.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/add/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\components\\orders\\order-list.tsx":{"id":"(app-pages-browser)/./src/app/components/orders/order-list.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\accounts\\edit\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\layout":[],"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(DashboardLayout)\\page":[],"C:\\Users\\<USER>\\Desktop\\Rest Delivery\\restaurant_mange\\src\\app\\(auth)\\login\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(rsc)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/Accordion.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Accordion/Accordion.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionContent.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionPanel.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionPanel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionTitle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Accordion/AccordionTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Banner/BannerCollapseButton.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Banner/BannerCollapseButton.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Carousel/Carousel.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Carousel/Carousel.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/Clipboard.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Clipboard/Clipboard.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIcon.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIcon.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIconText.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Clipboard/ClipboardWithIconText.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/DarkThemeToggle/DarkThemeToggle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/DarkThemeToggle/DarkThemeToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Datepicker/Datepicker.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Datepicker/Datepicker.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/Drawer.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Drawer/Drawer.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerHeader.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerItems.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Drawer/DrawerItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/Dropdown.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Dropdown/Dropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownDivider.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownDivider.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownHeader.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownItem.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Dropdown/DropdownItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Floating/Floating.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Floating/Floating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenu.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenu.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdown.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdown.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdownToggle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/MegaMenu/MegaMenuDropdownToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/Modal.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Modal/Modal.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalBody.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalFooter.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalFooter.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalHeader.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Modal/ModalHeader.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/Navbar.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Navbar/Navbar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarBrand.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarBrand.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarCollapse.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarCollapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarLink.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarLink.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarToggle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Navbar/NavbarToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Popover/Popover.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Popover/Popover.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/Rating.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Rating/Rating.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Rating/RatingStar.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Rating/RatingStar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/Sidebar.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/Sidebar.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCollapse.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCollapse.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCTA.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarCTA.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItem.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItemGroup.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItemGroup.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItems.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarItems.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarLogo.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Sidebar/SidebarLogo.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/Table.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/Table.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableBody.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/TableBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableCell.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/TableCell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHead.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/TableHead.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableHeadCell.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/TableHeadCell.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Table/TableRow.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Table/TableRow.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/TabItem.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Tabs/TabItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Tabs/Tabs.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Tabs/Tabs.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/Timeline.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/Timeline.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineBody.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineBody.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineContent.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineContent.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineItem.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineItem.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelinePoint.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelinePoint.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTime.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTime.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTitle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Timeline/TimelineTitle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/Toast.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Toast/Toast.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/components/Toast/ToastToggle.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/components/Toast/ToastToggle.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/hooks/use-theme-mode.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/hooks/use-theme-mode.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/client.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/theme-store/init/client.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/theme-store/init/mode.mjs":{"*":{"id":"(rsc)/./node_modules/flowbite-react/dist/esm/theme-store/init/mode.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./public/images/backgrounds/errorimg.svg":{"*":{"id":"(rsc)/./public/images/backgrounds/errorimg.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(DashboardLayout)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/page.tsx":{"*":{"id":"(rsc)/./src/app/(DashboardLayout)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(rsc)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/working-hours/working-hours-management.tsx":{"*":{"id":"(rsc)/./src/app/components/working-hours/working-hours-management.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/product-management/product-list.tsx":{"*":{"id":"(rsc)/./src/app/components/product-management/product-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/accounts/restaurant/restaurant-account-list.tsx":{"*":{"id":"(rsc)/./src/app/components/accounts/restaurant/restaurant-account-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/add/page.tsx":{"*":{"id":"(rsc)/./src/app/(DashboardLayout)/accounts/add/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/orders/order-list.tsx":{"*":{"id":"(rsc)/./src/app/components/orders/order-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(DashboardLayout)/accounts/edit/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/(DashboardLayout)/accounts/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}
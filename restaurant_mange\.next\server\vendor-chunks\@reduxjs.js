"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reduxjs";
exports.ids = ["vendor-chunks/@reduxjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancerArray: () => (/* binding */ EnhancerArray),\n/* harmony export */   MiddlewareArray: () => (/* binding */ MiddlewareArray),\n/* harmony export */   SHOULD_AUTOBATCH: () => (/* binding */ SHOULD_AUTOBATCH),\n/* harmony export */   TaskAbortError: () => (/* binding */ TaskAbortError),\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.__DO_NOT_USE__ActionTypes),\n/* harmony export */   addListener: () => (/* binding */ addListener),\n/* harmony export */   applyMiddleware: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware),\n/* harmony export */   autoBatchEnhancer: () => (/* binding */ autoBatchEnhancer),\n/* harmony export */   bindActionCreators: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.bindActionCreators),\n/* harmony export */   clearAllListeners: () => (/* binding */ clearAllListeners),\n/* harmony export */   combineReducers: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers),\n/* harmony export */   compose: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.compose),\n/* harmony export */   configureStore: () => (/* binding */ configureStore),\n/* harmony export */   createAction: () => (/* binding */ createAction),\n/* harmony export */   createActionCreatorInvariantMiddleware: () => (/* binding */ createActionCreatorInvariantMiddleware),\n/* harmony export */   createAsyncThunk: () => (/* binding */ createAsyncThunk),\n/* harmony export */   createDraftSafeSelector: () => (/* binding */ createDraftSafeSelector),\n/* harmony export */   createEntityAdapter: () => (/* binding */ createEntityAdapter),\n/* harmony export */   createImmutableStateInvariantMiddleware: () => (/* binding */ createImmutableStateInvariantMiddleware),\n/* harmony export */   createListenerMiddleware: () => (/* binding */ createListenerMiddleware),\n/* harmony export */   createNextState: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createReducer: () => (/* binding */ createReducer),\n/* harmony export */   createSelector: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelector),\n/* harmony export */   createSerializableStateInvariantMiddleware: () => (/* binding */ createSerializableStateInvariantMiddleware),\n/* harmony export */   createSlice: () => (/* binding */ createSlice),\n/* harmony export */   createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   current: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.current),\n/* harmony export */   findNonSerializableValue: () => (/* binding */ findNonSerializableValue),\n/* harmony export */   freeze: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.freeze),\n/* harmony export */   getDefaultMiddleware: () => (/* binding */ getDefaultMiddleware),\n/* harmony export */   getType: () => (/* binding */ getType),\n/* harmony export */   isAction: () => (/* binding */ isAction),\n/* harmony export */   isActionCreator: () => (/* binding */ isActionCreator),\n/* harmony export */   isAllOf: () => (/* binding */ isAllOf),\n/* harmony export */   isAnyOf: () => (/* binding */ isAnyOf),\n/* harmony export */   isAsyncThunkAction: () => (/* binding */ isAsyncThunkAction),\n/* harmony export */   isDraft: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.isDraft),\n/* harmony export */   isFluxStandardAction: () => (/* binding */ isFSA),\n/* harmony export */   isFulfilled: () => (/* binding */ isFulfilled),\n/* harmony export */   isImmutableDefault: () => (/* binding */ isImmutableDefault),\n/* harmony export */   isPending: () => (/* binding */ isPending),\n/* harmony export */   isPlain: () => (/* binding */ isPlain),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isRejected: () => (/* binding */ isRejected),\n/* harmony export */   isRejectedWithValue: () => (/* binding */ isRejectedWithValue),\n/* harmony export */   legacy_createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.legacy_createStore),\n/* harmony export */   miniSerializeError: () => (/* binding */ miniSerializeError),\n/* harmony export */   nanoid: () => (/* binding */ nanoid),\n/* harmony export */   original: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.original),\n/* harmony export */   prepareAutoBatched: () => (/* binding */ prepareAutoBatched),\n/* harmony export */   removeListener: () => (/* binding */ removeListener),\n/* harmony export */   unwrapResult: () => (/* binding */ unwrapResult)\n/* harmony export */ });\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/immer.esm.mjs\");\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/es/redux.js\");\n/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/es/index.js\");\n/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-thunk */ \"(ssr)/./node_modules/redux-thunk/es/index.js\");\nvar __extends = (undefined && undefined.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nvar __defProp = Object.defineProperty;\r\nvar __defProps = Object.defineProperties;\r\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\r\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\r\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\r\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\r\nvar __defNormalProp = function (obj, key, value) { return key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value: value }) : obj[key] = value; };\r\nvar __spreadValues = function (a, b) {\r\n    for (var prop in b || (b = {}))\r\n        if (__hasOwnProp.call(b, prop))\r\n            __defNormalProp(a, prop, b[prop]);\r\n    if (__getOwnPropSymbols)\r\n        for (var _i = 0, _c = __getOwnPropSymbols(b); _i < _c.length; _i++) {\r\n            var prop = _c[_i];\r\n            if (__propIsEnum.call(b, prop))\r\n                __defNormalProp(a, prop, b[prop]);\r\n        }\r\n    return a;\r\n};\r\nvar __spreadProps = function (a, b) { return __defProps(a, __getOwnPropDescs(b)); };\r\nvar __async = function (__this, __arguments, generator) {\r\n    return new Promise(function (resolve, reject) {\r\n        var fulfilled = function (value) {\r\n            try {\r\n                step(generator.next(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var rejected = function (value) {\r\n            try {\r\n                step(generator.throw(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var step = function (x) { return x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected); };\r\n        step((generator = generator.apply(__this, __arguments)).next());\r\n    });\r\n};\r\n// src/index.ts\r\n\r\n\r\n\r\n\r\n// src/createDraftSafeSelector.ts\r\n\r\n\r\nvar createDraftSafeSelector = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    var selector = reselect__WEBPACK_IMPORTED_MODULE_1__.createSelector.apply(void 0, args);\r\n    var wrappedSelector = function (value) {\r\n        var rest = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            rest[_i - 1] = arguments[_i];\r\n        }\r\n        return selector.apply(void 0, __spreadArray([(0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value], rest));\r\n    };\r\n    return wrappedSelector;\r\n};\r\n// src/configureStore.ts\r\n\r\n// src/devtoolsExtension.ts\r\n\r\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\r\n    if (arguments.length === 0)\r\n        return void 0;\r\n    if (typeof arguments[0] === \"object\")\r\n        return redux__WEBPACK_IMPORTED_MODULE_0__.compose;\r\n    return redux__WEBPACK_IMPORTED_MODULE_0__.compose.apply(null, arguments);\r\n};\r\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\r\n    return function (noop2) {\r\n        return noop2;\r\n    };\r\n};\r\n// src/isPlainObject.ts\r\nfunction isPlainObject(value) {\r\n    if (typeof value !== \"object\" || value === null)\r\n        return false;\r\n    var proto = Object.getPrototypeOf(value);\r\n    if (proto === null)\r\n        return true;\r\n    var baseProto = proto;\r\n    while (Object.getPrototypeOf(baseProto) !== null) {\r\n        baseProto = Object.getPrototypeOf(baseProto);\r\n    }\r\n    return proto === baseProto;\r\n}\r\n// src/getDefaultMiddleware.ts\r\n\r\n// src/tsHelpers.ts\r\nvar hasMatchFunction = function (v) {\r\n    return v && typeof v.match === \"function\";\r\n};\r\n// src/createAction.ts\r\nfunction createAction(type, prepareAction) {\r\n    function actionCreator() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        if (prepareAction) {\r\n            var prepared = prepareAction.apply(void 0, args);\r\n            if (!prepared) {\r\n                throw new Error(\"prepareAction did not return an object\");\r\n            }\r\n            return __spreadValues(__spreadValues({\r\n                type: type,\r\n                payload: prepared.payload\r\n            }, \"meta\" in prepared && { meta: prepared.meta }), \"error\" in prepared && { error: prepared.error });\r\n        }\r\n        return { type: type, payload: args[0] };\r\n    }\r\n    actionCreator.toString = function () { return \"\" + type; };\r\n    actionCreator.type = type;\r\n    actionCreator.match = function (action) { return action.type === type; };\r\n    return actionCreator;\r\n}\r\nfunction isAction(action) {\r\n    return isPlainObject(action) && \"type\" in action;\r\n}\r\nfunction isActionCreator(action) {\r\n    return typeof action === \"function\" && \"type\" in action && hasMatchFunction(action);\r\n}\r\nfunction isFSA(action) {\r\n    return isAction(action) && typeof action.type === \"string\" && Object.keys(action).every(isValidKey);\r\n}\r\nfunction isValidKey(key) {\r\n    return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\r\n}\r\nfunction getType(actionCreator) {\r\n    return \"\" + actionCreator;\r\n}\r\n// src/actionCreatorInvariantMiddleware.ts\r\nfunction getMessage(type) {\r\n    var splitType = type ? (\"\" + type).split(\"/\") : [];\r\n    var actionName = splitType[splitType.length - 1] || \"actionCreator\";\r\n    return \"Detected an action creator with type \\\"\" + (type || \"unknown\") + \"\\\" being dispatched. \\nMake sure you're calling the action creator before dispatching, i.e. `dispatch(\" + actionName + \"())` instead of `dispatch(\" + actionName + \")`. This is necessary even if the action has no payload.\";\r\n}\r\nfunction createActionCreatorInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (false) {}\r\n    var _c = options.isActionCreator, isActionCreator2 = _c === void 0 ? isActionCreator : _c;\r\n    return function () { return function (next) { return function (action) {\r\n        if (isActionCreator2(action)) {\r\n            console.warn(getMessage(action.type));\r\n        }\r\n        return next(action);\r\n    }; }; };\r\n}\r\n// src/utils.ts\r\n\r\nfunction getTimeMeasureUtils(maxDelay, fnName) {\r\n    var elapsed = 0;\r\n    return {\r\n        measureTime: function (fn) {\r\n            var started = Date.now();\r\n            try {\r\n                return fn();\r\n            }\r\n            finally {\r\n                var finished = Date.now();\r\n                elapsed += finished - started;\r\n            }\r\n        },\r\n        warnIfExceeded: function () {\r\n            if (elapsed > maxDelay) {\r\n                console.warn(fnName + \" took \" + elapsed + \"ms, which is more than the warning threshold of \" + maxDelay + \"ms. \\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\\nIt is disabled in production builds, so you don't need to worry about that.\");\r\n            }\r\n        }\r\n    };\r\n}\r\nvar MiddlewareArray = /** @class */ (function (_super) {\r\n    __extends(MiddlewareArray, _super);\r\n    function MiddlewareArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, MiddlewareArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(MiddlewareArray, Symbol.species, {\r\n        get: function () {\r\n            return MiddlewareArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    MiddlewareArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    MiddlewareArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return MiddlewareArray;\r\n}(Array));\r\nvar EnhancerArray = /** @class */ (function (_super) {\r\n    __extends(EnhancerArray, _super);\r\n    function EnhancerArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, EnhancerArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(EnhancerArray, Symbol.species, {\r\n        get: function () {\r\n            return EnhancerArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    EnhancerArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    EnhancerArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return EnhancerArray;\r\n}(Array));\r\nfunction freezeDraftable(val) {\r\n    return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(val) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(val, function () {\r\n    }) : val;\r\n}\r\n// src/immutableStateInvariantMiddleware.ts\r\nvar isProduction = \"development\" === \"production\";\r\nvar prefix = \"Invariant failed\";\r\nfunction invariant(condition, message) {\r\n    if (condition) {\r\n        return;\r\n    }\r\n    if (isProduction) {\r\n        throw new Error(prefix);\r\n    }\r\n    throw new Error(prefix + \": \" + (message || \"\"));\r\n}\r\nfunction stringify(obj, serializer, indent, decycler) {\r\n    return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\r\n}\r\nfunction getSerialize(serializer, decycler) {\r\n    var stack = [], keys = [];\r\n    if (!decycler)\r\n        decycler = function (_, value) {\r\n            if (stack[0] === value)\r\n                return \"[Circular ~]\";\r\n            return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\r\n        };\r\n    return function (key, value) {\r\n        if (stack.length > 0) {\r\n            var thisPos = stack.indexOf(this);\r\n            ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\r\n            ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\r\n            if (~stack.indexOf(value))\r\n                value = decycler.call(this, key, value);\r\n        }\r\n        else\r\n            stack.push(value);\r\n        return serializer == null ? value : serializer.call(this, key, value);\r\n    };\r\n}\r\nfunction isImmutableDefault(value) {\r\n    return typeof value !== \"object\" || value == null || Object.isFrozen(value);\r\n}\r\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\r\n    var trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\r\n    return {\r\n        detectMutations: function () {\r\n            return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\r\n        }\r\n    };\r\n}\r\nfunction trackProperties(isImmutable, ignorePaths, obj, path, checkedObjects) {\r\n    if (ignorePaths === void 0) { ignorePaths = []; }\r\n    if (path === void 0) { path = \"\"; }\r\n    if (checkedObjects === void 0) { checkedObjects = new Set(); }\r\n    var tracked = { value: obj };\r\n    if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n        checkedObjects.add(obj);\r\n        tracked.children = {};\r\n        for (var key in obj) {\r\n            var childPath = path ? path + \".\" + key : key;\r\n            if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n                continue;\r\n            }\r\n            tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\r\n        }\r\n    }\r\n    return tracked;\r\n}\r\nfunction detectMutations(isImmutable, ignoredPaths, trackedProperty, obj, sameParentRef, path) {\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    if (sameParentRef === void 0) { sameParentRef = false; }\r\n    if (path === void 0) { path = \"\"; }\r\n    var prevObj = trackedProperty ? trackedProperty.value : void 0;\r\n    var sameRef = prevObj === obj;\r\n    if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n        return { wasMutated: true, path: path };\r\n    }\r\n    if (isImmutable(prevObj) || isImmutable(obj)) {\r\n        return { wasMutated: false };\r\n    }\r\n    var keysToDetect = {};\r\n    for (var key in trackedProperty.children) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    for (var key in obj) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_1 = function (key) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        var result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\r\n        if (result.wasMutated) {\r\n            return { value: result };\r\n        }\r\n    };\r\n    for (var key in keysToDetect) {\r\n        var state_1 = _loop_1(key);\r\n        if (typeof state_1 === \"object\")\r\n            return state_1.value;\r\n    }\r\n    return { wasMutated: false };\r\n}\r\nfunction createImmutableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (false) {}\r\n    var _c = options.isImmutable, isImmutable = _c === void 0 ? isImmutableDefault : _c, ignoredPaths = options.ignoredPaths, _d = options.warnAfter, warnAfter = _d === void 0 ? 32 : _d, ignore = options.ignore;\r\n    ignoredPaths = ignoredPaths || ignore;\r\n    var track = trackForMutations.bind(null, isImmutable, ignoredPaths);\r\n    return function (_c) {\r\n        var getState = _c.getState;\r\n        var state = getState();\r\n        var tracker = track(state);\r\n        var result;\r\n        return function (next) { return function (action) {\r\n            var measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                invariant(!result.wasMutated, \"A state mutation was detected between dispatches, in the path '\" + (result.path || \"\") + \"'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            var dispatchedAction = next(action);\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                result.wasMutated && invariant(!result.wasMutated, \"A state mutation was detected inside a dispatch, in the path: \" + (result.path || \"\") + \". Take a look at the reducer(s) handling the action \" + stringify(action) + \". (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n            return dispatchedAction;\r\n        }; };\r\n    };\r\n}\r\n// src/serializableStateInvariantMiddleware.ts\r\nfunction isPlain(val) {\r\n    var type = typeof val;\r\n    return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\r\n}\r\nfunction findNonSerializableValue(value, path, isSerializable, getEntries, ignoredPaths, cache) {\r\n    if (path === void 0) { path = \"\"; }\r\n    if (isSerializable === void 0) { isSerializable = isPlain; }\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    var foundNestedSerializable;\r\n    if (!isSerializable(value)) {\r\n        return {\r\n            keyPath: path || \"<root>\",\r\n            value: value\r\n        };\r\n    }\r\n    if (typeof value !== \"object\" || value === null) {\r\n        return false;\r\n    }\r\n    if (cache == null ? void 0 : cache.has(value))\r\n        return false;\r\n    var entries = getEntries != null ? getEntries(value) : Object.entries(value);\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_2 = function (key, nestedValue) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        if (!isSerializable(nestedValue)) {\r\n            return { value: {\r\n                    keyPath: nestedPath,\r\n                    value: nestedValue\r\n                } };\r\n        }\r\n        if (typeof nestedValue === \"object\") {\r\n            foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\r\n            if (foundNestedSerializable) {\r\n                return { value: foundNestedSerializable };\r\n            }\r\n        }\r\n    };\r\n    for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\r\n        var _c = entries_1[_i], key = _c[0], nestedValue = _c[1];\r\n        var state_2 = _loop_2(key, nestedValue);\r\n        if (typeof state_2 === \"object\")\r\n            return state_2.value;\r\n    }\r\n    if (cache && isNestedFrozen(value))\r\n        cache.add(value);\r\n    return false;\r\n}\r\nfunction isNestedFrozen(value) {\r\n    if (!Object.isFrozen(value))\r\n        return false;\r\n    for (var _i = 0, _c = Object.values(value); _i < _c.length; _i++) {\r\n        var nestedValue = _c[_i];\r\n        if (typeof nestedValue !== \"object\" || nestedValue === null)\r\n            continue;\r\n        if (!isNestedFrozen(nestedValue))\r\n            return false;\r\n    }\r\n    return true;\r\n}\r\nfunction createSerializableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (false) {}\r\n    var _c = options.isSerializable, isSerializable = _c === void 0 ? isPlain : _c, getEntries = options.getEntries, _d = options.ignoredActions, ignoredActions = _d === void 0 ? [] : _d, _e = options.ignoredActionPaths, ignoredActionPaths = _e === void 0 ? [\"meta.arg\", \"meta.baseQueryMeta\"] : _e, _f = options.ignoredPaths, ignoredPaths = _f === void 0 ? [] : _f, _g = options.warnAfter, warnAfter = _g === void 0 ? 32 : _g, _h = options.ignoreState, ignoreState = _h === void 0 ? false : _h, _j = options.ignoreActions, ignoreActions = _j === void 0 ? false : _j, _k = options.disableCache, disableCache = _k === void 0 ? false : _k;\r\n    var cache = !disableCache && WeakSet ? new WeakSet() : void 0;\r\n    return function (storeAPI) { return function (next) { return function (action) {\r\n        var result = next(action);\r\n        var measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\r\n        if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\r\n            measureUtils.measureTime(function () {\r\n                var foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\r\n                if (foundActionNonSerializableValue) {\r\n                    var keyPath = foundActionNonSerializableValue.keyPath, value = foundActionNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in an action, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\r\n                }\r\n            });\r\n        }\r\n        if (!ignoreState) {\r\n            measureUtils.measureTime(function () {\r\n                var state = storeAPI.getState();\r\n                var foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\r\n                if (foundStateNonSerializableValue) {\r\n                    var keyPath = foundStateNonSerializableValue.keyPath, value = foundStateNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in the state, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the reducer(s) handling this action type: \" + action.type + \".\\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)\");\r\n                }\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n        }\r\n        return result;\r\n    }; }; };\r\n}\r\n// src/getDefaultMiddleware.ts\r\nfunction isBoolean(x) {\r\n    return typeof x === \"boolean\";\r\n}\r\nfunction curryGetDefaultMiddleware() {\r\n    return function curriedGetDefaultMiddleware(options) {\r\n        return getDefaultMiddleware(options);\r\n    };\r\n}\r\nfunction getDefaultMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = options.thunk, thunk = _c === void 0 ? true : _c, _d = options.immutableCheck, immutableCheck = _d === void 0 ? true : _d, _e = options.serializableCheck, serializableCheck = _e === void 0 ? true : _e, _f = options.actionCreatorCheck, actionCreatorCheck = _f === void 0 ? true : _f;\r\n    var middlewareArray = new MiddlewareArray();\r\n    if (thunk) {\r\n        if (isBoolean(thunk)) {\r\n            middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\r\n        }\r\n        else {\r\n            middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__[\"default\"].withExtraArgument(thunk.extraArgument));\r\n        }\r\n    }\r\n    if (true) {\r\n        if (immutableCheck) {\r\n            var immutableOptions = {};\r\n            if (!isBoolean(immutableCheck)) {\r\n                immutableOptions = immutableCheck;\r\n            }\r\n            middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\r\n        }\r\n        if (serializableCheck) {\r\n            var serializableOptions = {};\r\n            if (!isBoolean(serializableCheck)) {\r\n                serializableOptions = serializableCheck;\r\n            }\r\n            middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\r\n        }\r\n        if (actionCreatorCheck) {\r\n            var actionCreatorOptions = {};\r\n            if (!isBoolean(actionCreatorCheck)) {\r\n                actionCreatorOptions = actionCreatorCheck;\r\n            }\r\n            middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\r\n        }\r\n    }\r\n    return middlewareArray;\r\n}\r\n// src/configureStore.ts\r\nvar IS_PRODUCTION = \"development\" === \"production\";\r\nfunction configureStore(options) {\r\n    var curriedGetDefaultMiddleware = curryGetDefaultMiddleware();\r\n    var _c = options || {}, _d = _c.reducer, reducer = _d === void 0 ? void 0 : _d, _e = _c.middleware, middleware = _e === void 0 ? curriedGetDefaultMiddleware() : _e, _f = _c.devTools, devTools = _f === void 0 ? true : _f, _g = _c.preloadedState, preloadedState = _g === void 0 ? void 0 : _g, _h = _c.enhancers, enhancers = _h === void 0 ? void 0 : _h;\r\n    var rootReducer;\r\n    if (typeof reducer === \"function\") {\r\n        rootReducer = reducer;\r\n    }\r\n    else if (isPlainObject(reducer)) {\r\n        rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducer);\r\n    }\r\n    else {\r\n        throw new Error('\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\r\n    }\r\n    var finalMiddleware = middleware;\r\n    if (typeof finalMiddleware === \"function\") {\r\n        finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware);\r\n        if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n            throw new Error(\"when using a middleware builder function, an array of middleware must be returned\");\r\n        }\r\n    }\r\n    if (!IS_PRODUCTION && finalMiddleware.some(function (item) { return typeof item !== \"function\"; })) {\r\n        throw new Error(\"each middleware provided to configureStore must be a function\");\r\n    }\r\n    var middlewareEnhancer = redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware.apply(void 0, finalMiddleware);\r\n    var finalCompose = redux__WEBPACK_IMPORTED_MODULE_0__.compose;\r\n    if (devTools) {\r\n        finalCompose = composeWithDevTools(__spreadValues({\r\n            trace: !IS_PRODUCTION\r\n        }, typeof devTools === \"object\" && devTools));\r\n    }\r\n    var defaultEnhancers = new EnhancerArray(middlewareEnhancer);\r\n    var storeEnhancers = defaultEnhancers;\r\n    if (Array.isArray(enhancers)) {\r\n        storeEnhancers = __spreadArray([middlewareEnhancer], enhancers);\r\n    }\r\n    else if (typeof enhancers === \"function\") {\r\n        storeEnhancers = enhancers(defaultEnhancers);\r\n    }\r\n    var composedEnhancer = finalCompose.apply(void 0, storeEnhancers);\r\n    return (0,redux__WEBPACK_IMPORTED_MODULE_0__.createStore)(rootReducer, preloadedState, composedEnhancer);\r\n}\r\n// src/createReducer.ts\r\n\r\n// src/mapBuilders.ts\r\nfunction executeReducerBuilderCallback(builderCallback) {\r\n    var actionsMap = {};\r\n    var actionMatchers = [];\r\n    var defaultCaseReducer;\r\n    var builder = {\r\n        addCase: function (typeOrActionCreator, reducer) {\r\n            if (true) {\r\n                if (actionMatchers.length > 0) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addMatcher`\");\r\n                }\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            var type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\r\n            if (!type) {\r\n                throw new Error(\"`builder.addCase` cannot be called with an empty action type\");\r\n            }\r\n            if (type in actionsMap) {\r\n                throw new Error(\"`builder.addCase` cannot be called with two reducers for the same action type\");\r\n            }\r\n            actionsMap[type] = reducer;\r\n            return builder;\r\n        },\r\n        addMatcher: function (matcher, reducer) {\r\n            if (true) {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            actionMatchers.push({ matcher: matcher, reducer: reducer });\r\n            return builder;\r\n        },\r\n        addDefaultCase: function (reducer) {\r\n            if (true) {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addDefaultCase` can only be called once\");\r\n                }\r\n            }\r\n            defaultCaseReducer = reducer;\r\n            return builder;\r\n        }\r\n    };\r\n    builderCallback(builder);\r\n    return [actionsMap, actionMatchers, defaultCaseReducer];\r\n}\r\n// src/createReducer.ts\r\nfunction isStateFunction(x) {\r\n    return typeof x === \"function\";\r\n}\r\nvar hasWarnedAboutObjectNotation = false;\r\nfunction createReducer(initialState, mapOrBuilderCallback, actionMatchers, defaultCaseReducer) {\r\n    if (actionMatchers === void 0) { actionMatchers = []; }\r\n    if (true) {\r\n        if (typeof mapOrBuilderCallback === \"object\") {\r\n            if (!hasWarnedAboutObjectNotation) {\r\n                hasWarnedAboutObjectNotation = true;\r\n                console.warn(\"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\r\n            }\r\n        }\r\n    }\r\n    var _c = typeof mapOrBuilderCallback === \"function\" ? executeReducerBuilderCallback(mapOrBuilderCallback) : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer], actionsMap = _c[0], finalActionMatchers = _c[1], finalDefaultCaseReducer = _c[2];\r\n    var getInitialState;\r\n    if (isStateFunction(initialState)) {\r\n        getInitialState = function () { return freezeDraftable(initialState()); };\r\n    }\r\n    else {\r\n        var frozenInitialState_1 = freezeDraftable(initialState);\r\n        getInitialState = function () { return frozenInitialState_1; };\r\n    }\r\n    function reducer(state, action) {\r\n        if (state === void 0) { state = getInitialState(); }\r\n        var caseReducers = __spreadArray([\r\n            actionsMap[action.type]\r\n        ], finalActionMatchers.filter(function (_c) {\r\n            var matcher = _c.matcher;\r\n            return matcher(action);\r\n        }).map(function (_c) {\r\n            var reducer2 = _c.reducer;\r\n            return reducer2;\r\n        }));\r\n        if (caseReducers.filter(function (cr) { return !!cr; }).length === 0) {\r\n            caseReducers = [finalDefaultCaseReducer];\r\n        }\r\n        return caseReducers.reduce(function (previousState, caseReducer) {\r\n            if (caseReducer) {\r\n                if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(previousState)) {\r\n                    var draft = previousState;\r\n                    var result = caseReducer(draft, action);\r\n                    if (result === void 0) {\r\n                        return previousState;\r\n                    }\r\n                    return result;\r\n                }\r\n                else if (!(0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(previousState)) {\r\n                    var result = caseReducer(previousState, action);\r\n                    if (result === void 0) {\r\n                        if (previousState === null) {\r\n                            return previousState;\r\n                        }\r\n                        throw Error(\"A case reducer on a non-draftable value must not return undefined\");\r\n                    }\r\n                    return result;\r\n                }\r\n                else {\r\n                    return (0,immer__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(previousState, function (draft) {\r\n                        return caseReducer(draft, action);\r\n                    });\r\n                }\r\n            }\r\n            return previousState;\r\n        }, state);\r\n    }\r\n    reducer.getInitialState = getInitialState;\r\n    return reducer;\r\n}\r\n// src/createSlice.ts\r\nvar hasWarnedAboutObjectNotation2 = false;\r\nfunction getType2(slice, actionKey) {\r\n    return slice + \"/\" + actionKey;\r\n}\r\nfunction createSlice(options) {\r\n    var name = options.name;\r\n    if (!name) {\r\n        throw new Error(\"`name` is a required option for createSlice\");\r\n    }\r\n    if (typeof process !== \"undefined\" && \"development\" === \"development\") {\r\n        if (options.initialState === void 0) {\r\n            console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\r\n        }\r\n    }\r\n    var initialState = typeof options.initialState == \"function\" ? options.initialState : freezeDraftable(options.initialState);\r\n    var reducers = options.reducers || {};\r\n    var reducerNames = Object.keys(reducers);\r\n    var sliceCaseReducersByName = {};\r\n    var sliceCaseReducersByType = {};\r\n    var actionCreators = {};\r\n    reducerNames.forEach(function (reducerName) {\r\n        var maybeReducerWithPrepare = reducers[reducerName];\r\n        var type = getType2(name, reducerName);\r\n        var caseReducer;\r\n        var prepareCallback;\r\n        if (\"reducer\" in maybeReducerWithPrepare) {\r\n            caseReducer = maybeReducerWithPrepare.reducer;\r\n            prepareCallback = maybeReducerWithPrepare.prepare;\r\n        }\r\n        else {\r\n            caseReducer = maybeReducerWithPrepare;\r\n        }\r\n        sliceCaseReducersByName[reducerName] = caseReducer;\r\n        sliceCaseReducersByType[type] = caseReducer;\r\n        actionCreators[reducerName] = prepareCallback ? createAction(type, prepareCallback) : createAction(type);\r\n    });\r\n    function buildReducer() {\r\n        if (true) {\r\n            if (typeof options.extraReducers === \"object\") {\r\n                if (!hasWarnedAboutObjectNotation2) {\r\n                    hasWarnedAboutObjectNotation2 = true;\r\n                    console.warn(\"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\r\n                }\r\n            }\r\n        }\r\n        var _c = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers], _d = _c[0], extraReducers = _d === void 0 ? {} : _d, _e = _c[1], actionMatchers = _e === void 0 ? [] : _e, _f = _c[2], defaultCaseReducer = _f === void 0 ? void 0 : _f;\r\n        var finalCaseReducers = __spreadValues(__spreadValues({}, extraReducers), sliceCaseReducersByType);\r\n        return createReducer(initialState, function (builder) {\r\n            for (var key in finalCaseReducers) {\r\n                builder.addCase(key, finalCaseReducers[key]);\r\n            }\r\n            for (var _i = 0, actionMatchers_1 = actionMatchers; _i < actionMatchers_1.length; _i++) {\r\n                var m = actionMatchers_1[_i];\r\n                builder.addMatcher(m.matcher, m.reducer);\r\n            }\r\n            if (defaultCaseReducer) {\r\n                builder.addDefaultCase(defaultCaseReducer);\r\n            }\r\n        });\r\n    }\r\n    var _reducer;\r\n    return {\r\n        name: name,\r\n        reducer: function (state, action) {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer(state, action);\r\n        },\r\n        actions: actionCreators,\r\n        caseReducers: sliceCaseReducersByName,\r\n        getInitialState: function () {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer.getInitialState();\r\n        }\r\n    };\r\n}\r\n// src/entities/entity_state.ts\r\nfunction getInitialEntityState() {\r\n    return {\r\n        ids: [],\r\n        entities: {}\r\n    };\r\n}\r\nfunction createInitialStateFactory() {\r\n    function getInitialState(additionalState) {\r\n        if (additionalState === void 0) { additionalState = {}; }\r\n        return Object.assign(getInitialEntityState(), additionalState);\r\n    }\r\n    return { getInitialState: getInitialState };\r\n}\r\n// src/entities/state_selectors.ts\r\nfunction createSelectorsFactory() {\r\n    function getSelectors(selectState) {\r\n        var selectIds = function (state) { return state.ids; };\r\n        var selectEntities = function (state) { return state.entities; };\r\n        var selectAll = createDraftSafeSelector(selectIds, selectEntities, function (ids, entities) { return ids.map(function (id) { return entities[id]; }); });\r\n        var selectId = function (_, id) { return id; };\r\n        var selectById = function (entities, id) { return entities[id]; };\r\n        var selectTotal = createDraftSafeSelector(selectIds, function (ids) { return ids.length; });\r\n        if (!selectState) {\r\n            return {\r\n                selectIds: selectIds,\r\n                selectEntities: selectEntities,\r\n                selectAll: selectAll,\r\n                selectTotal: selectTotal,\r\n                selectById: createDraftSafeSelector(selectEntities, selectId, selectById)\r\n            };\r\n        }\r\n        var selectGlobalizedEntities = createDraftSafeSelector(selectState, selectEntities);\r\n        return {\r\n            selectIds: createDraftSafeSelector(selectState, selectIds),\r\n            selectEntities: selectGlobalizedEntities,\r\n            selectAll: createDraftSafeSelector(selectState, selectAll),\r\n            selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n            selectById: createDraftSafeSelector(selectGlobalizedEntities, selectId, selectById)\r\n        };\r\n    }\r\n    return { getSelectors: getSelectors };\r\n}\r\n// src/entities/state_adapter.ts\r\n\r\nfunction createSingleArgumentStateOperator(mutator) {\r\n    var operator = createStateOperator(function (_, state) { return mutator(state); });\r\n    return function operation(state) {\r\n        return operator(state, void 0);\r\n    };\r\n}\r\nfunction createStateOperator(mutator) {\r\n    return function operation(state, arg) {\r\n        function isPayloadActionArgument(arg2) {\r\n            return isFSA(arg2);\r\n        }\r\n        var runMutator = function (draft) {\r\n            if (isPayloadActionArgument(arg)) {\r\n                mutator(arg.payload, draft);\r\n            }\r\n            else {\r\n                mutator(arg, draft);\r\n            }\r\n        };\r\n        if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(state)) {\r\n            runMutator(state);\r\n            return state;\r\n        }\r\n        else {\r\n            return (0,immer__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(state, runMutator);\r\n        }\r\n    };\r\n}\r\n// src/entities/utils.ts\r\nfunction selectIdValue(entity, selectId) {\r\n    var key = selectId(entity);\r\n    if ( true && key === void 0) {\r\n        console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\r\n    }\r\n    return key;\r\n}\r\nfunction ensureEntitiesArray(entities) {\r\n    if (!Array.isArray(entities)) {\r\n        entities = Object.values(entities);\r\n    }\r\n    return entities;\r\n}\r\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\r\n    newEntities = ensureEntitiesArray(newEntities);\r\n    var added = [];\r\n    var updated = [];\r\n    for (var _i = 0, newEntities_1 = newEntities; _i < newEntities_1.length; _i++) {\r\n        var entity = newEntities_1[_i];\r\n        var id = selectIdValue(entity, selectId);\r\n        if (id in state.entities) {\r\n            updated.push({ id: id, changes: entity });\r\n        }\r\n        else {\r\n            added.push(entity);\r\n        }\r\n    }\r\n    return [added, updated];\r\n}\r\n// src/entities/unsorted_state_adapter.ts\r\nfunction createUnsortedStateAdapter(selectId) {\r\n    function addOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (key in state.entities) {\r\n            return;\r\n        }\r\n        state.ids.push(key);\r\n        state.entities[key] = entity;\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_2 = newEntities; _i < newEntities_2.length; _i++) {\r\n            var entity = newEntities_2[_i];\r\n            addOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (!(key in state.entities)) {\r\n            state.ids.push(key);\r\n        }\r\n        state.entities[key] = entity;\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_3 = newEntities; _i < newEntities_3.length; _i++) {\r\n            var entity = newEntities_3[_i];\r\n            setOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.ids = [];\r\n        state.entities = {};\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function removeOneMutably(key, state) {\r\n        return removeManyMutably([key], state);\r\n    }\r\n    function removeManyMutably(keys, state) {\r\n        var didMutate = false;\r\n        keys.forEach(function (key) {\r\n            if (key in state.entities) {\r\n                delete state.entities[key];\r\n                didMutate = true;\r\n            }\r\n        });\r\n        if (didMutate) {\r\n            state.ids = state.ids.filter(function (id) { return id in state.entities; });\r\n        }\r\n    }\r\n    function removeAllMutably(state) {\r\n        Object.assign(state, {\r\n            ids: [],\r\n            entities: {}\r\n        });\r\n    }\r\n    function takeNewKey(keys, update, state) {\r\n        var original2 = state.entities[update.id];\r\n        var updated = Object.assign({}, original2, update.changes);\r\n        var newKey = selectIdValue(updated, selectId);\r\n        var hasNewKey = newKey !== update.id;\r\n        if (hasNewKey) {\r\n            keys[update.id] = newKey;\r\n            delete state.entities[update.id];\r\n        }\r\n        state.entities[newKey] = updated;\r\n        return hasNewKey;\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var newKeys = {};\r\n        var updatesPerEntity = {};\r\n        updates.forEach(function (update) {\r\n            if (update.id in state.entities) {\r\n                updatesPerEntity[update.id] = {\r\n                    id: update.id,\r\n                    changes: __spreadValues(__spreadValues({}, updatesPerEntity[update.id] ? updatesPerEntity[update.id].changes : null), update.changes)\r\n                };\r\n            }\r\n        });\r\n        updates = Object.values(updatesPerEntity);\r\n        var didMutateEntities = updates.length > 0;\r\n        if (didMutateEntities) {\r\n            var didMutateIds = updates.filter(function (update) { return takeNewKey(newKeys, update, state); }).length > 0;\r\n            if (didMutateIds) {\r\n                state.ids = Object.keys(state.entities);\r\n            }\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    return {\r\n        removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n        addOne: createStateOperator(addOneMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably),\r\n        removeOne: createStateOperator(removeOneMutably),\r\n        removeMany: createStateOperator(removeManyMutably)\r\n    };\r\n}\r\n// src/entities/sorted_state_adapter.ts\r\nfunction createSortedStateAdapter(selectId, sort) {\r\n    var _c = createUnsortedStateAdapter(selectId), removeOne = _c.removeOne, removeMany = _c.removeMany, removeAll = _c.removeAll;\r\n    function addOneMutably(entity, state) {\r\n        return addManyMutably([entity], state);\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        var models = newEntities.filter(function (model) { return !(selectIdValue(model, selectId) in state.entities); });\r\n        if (models.length !== 0) {\r\n            merge(models, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        return setManyMutably([entity], state);\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        if (newEntities.length !== 0) {\r\n            merge(newEntities, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.entities = {};\r\n        state.ids = [];\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var appliedUpdates = false;\r\n        for (var _i = 0, updates_1 = updates; _i < updates_1.length; _i++) {\r\n            var update = updates_1[_i];\r\n            var entity = state.entities[update.id];\r\n            if (!entity) {\r\n                continue;\r\n            }\r\n            appliedUpdates = true;\r\n            Object.assign(entity, update.changes);\r\n            var newId = selectId(entity);\r\n            if (update.id !== newId) {\r\n                delete state.entities[update.id];\r\n                state.entities[newId] = entity;\r\n            }\r\n        }\r\n        if (appliedUpdates) {\r\n            resortEntities(state);\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    function areArraysEqual(a, b) {\r\n        if (a.length !== b.length) {\r\n            return false;\r\n        }\r\n        for (var i = 0; i < a.length && i < b.length; i++) {\r\n            if (a[i] === b[i]) {\r\n                continue;\r\n            }\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n    function merge(models, state) {\r\n        models.forEach(function (model) {\r\n            state.entities[selectId(model)] = model;\r\n        });\r\n        resortEntities(state);\r\n    }\r\n    function resortEntities(state) {\r\n        var allEntities = Object.values(state.entities);\r\n        allEntities.sort(sort);\r\n        var newSortedIds = allEntities.map(selectId);\r\n        var ids = state.ids;\r\n        if (!areArraysEqual(ids, newSortedIds)) {\r\n            state.ids = newSortedIds;\r\n        }\r\n    }\r\n    return {\r\n        removeOne: removeOne,\r\n        removeMany: removeMany,\r\n        removeAll: removeAll,\r\n        addOne: createStateOperator(addOneMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably)\r\n    };\r\n}\r\n// src/entities/create_adapter.ts\r\nfunction createEntityAdapter(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = __spreadValues({\r\n        sortComparer: false,\r\n        selectId: function (instance) { return instance.id; }\r\n    }, options), selectId = _c.selectId, sortComparer = _c.sortComparer;\r\n    var stateFactory = createInitialStateFactory();\r\n    var selectorsFactory = createSelectorsFactory();\r\n    var stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\r\n    return __spreadValues(__spreadValues(__spreadValues({\r\n        selectId: selectId,\r\n        sortComparer: sortComparer\r\n    }, stateFactory), selectorsFactory), stateAdapter);\r\n}\r\n// src/nanoid.ts\r\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\r\nvar nanoid = function (size) {\r\n    if (size === void 0) { size = 21; }\r\n    var id = \"\";\r\n    var i = size;\r\n    while (i--) {\r\n        id += urlAlphabet[Math.random() * 64 | 0];\r\n    }\r\n    return id;\r\n};\r\n// src/createAsyncThunk.ts\r\nvar commonProperties = [\r\n    \"name\",\r\n    \"message\",\r\n    \"stack\",\r\n    \"code\"\r\n];\r\nvar RejectWithValue = /** @class */ (function () {\r\n    function RejectWithValue(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return RejectWithValue;\r\n}());\r\nvar FulfillWithMeta = /** @class */ (function () {\r\n    function FulfillWithMeta(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return FulfillWithMeta;\r\n}());\r\nvar miniSerializeError = function (value) {\r\n    if (typeof value === \"object\" && value !== null) {\r\n        var simpleError = {};\r\n        for (var _i = 0, commonProperties_1 = commonProperties; _i < commonProperties_1.length; _i++) {\r\n            var property = commonProperties_1[_i];\r\n            if (typeof value[property] === \"string\") {\r\n                simpleError[property] = value[property];\r\n            }\r\n        }\r\n        return simpleError;\r\n    }\r\n    return { message: String(value) };\r\n};\r\nvar createAsyncThunk = (function () {\r\n    function createAsyncThunk2(typePrefix, payloadCreator, options) {\r\n        var fulfilled = createAction(typePrefix + \"/fulfilled\", function (payload, requestId, arg, meta) { return ({\r\n            payload: payload,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"fulfilled\"\r\n            })\r\n        }); });\r\n        var pending = createAction(typePrefix + \"/pending\", function (requestId, arg, meta) { return ({\r\n            payload: void 0,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"pending\"\r\n            })\r\n        }); });\r\n        var rejected = createAction(typePrefix + \"/rejected\", function (error, requestId, arg, payload, meta) { return ({\r\n            payload: payload,\r\n            error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                rejectedWithValue: !!payload,\r\n                requestStatus: \"rejected\",\r\n                aborted: (error == null ? void 0 : error.name) === \"AbortError\",\r\n                condition: (error == null ? void 0 : error.name) === \"ConditionError\"\r\n            })\r\n        }); });\r\n        var displayedWarning = false;\r\n        var AC = typeof AbortController !== \"undefined\" ? AbortController : /** @class */ (function () {\r\n            function class_1() {\r\n                this.signal = {\r\n                    aborted: false,\r\n                    addEventListener: function () {\r\n                    },\r\n                    dispatchEvent: function () {\r\n                        return false;\r\n                    },\r\n                    onabort: function () {\r\n                    },\r\n                    removeEventListener: function () {\r\n                    },\r\n                    reason: void 0,\r\n                    throwIfAborted: function () {\r\n                    }\r\n                };\r\n            }\r\n            class_1.prototype.abort = function () {\r\n                if (true) {\r\n                    if (!displayedWarning) {\r\n                        displayedWarning = true;\r\n                        console.info(\"This platform does not implement AbortController. \\nIf you want to use the AbortController to react to `abort` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.\");\r\n                    }\r\n                }\r\n            };\r\n            return class_1;\r\n        }());\r\n        function actionCreator(arg) {\r\n            return function (dispatch, getState, extra) {\r\n                var requestId = (options == null ? void 0 : options.idGenerator) ? options.idGenerator(arg) : nanoid();\r\n                var abortController = new AC();\r\n                var abortReason;\r\n                var started = false;\r\n                function abort(reason) {\r\n                    abortReason = reason;\r\n                    abortController.abort();\r\n                }\r\n                var promise2 = function () {\r\n                    return __async(this, null, function () {\r\n                        var _a, _b, finalAction, conditionResult, abortedPromise, err_1, skipDispatch;\r\n                        return __generator(this, function (_c) {\r\n                            switch (_c.label) {\r\n                                case 0:\r\n                                    _c.trys.push([0, 4, , 5]);\r\n                                    conditionResult = (_a = options == null ? void 0 : options.condition) == null ? void 0 : _a.call(options, arg, { getState: getState, extra: extra });\r\n                                    if (!isThenable(conditionResult)) return [3 /*break*/, 2];\r\n                                    return [4 /*yield*/, conditionResult];\r\n                                case 1:\r\n                                    conditionResult = _c.sent();\r\n                                    _c.label = 2;\r\n                                case 2:\r\n                                    if (conditionResult === false || abortController.signal.aborted) {\r\n                                        throw {\r\n                                            name: \"ConditionError\",\r\n                                            message: \"Aborted due to condition callback returning false.\"\r\n                                        };\r\n                                    }\r\n                                    started = true;\r\n                                    abortedPromise = new Promise(function (_, reject) { return abortController.signal.addEventListener(\"abort\", function () { return reject({\r\n                                        name: \"AbortError\",\r\n                                        message: abortReason || \"Aborted\"\r\n                                    }); }); });\r\n                                    dispatch(pending(requestId, arg, (_b = options == null ? void 0 : options.getPendingMeta) == null ? void 0 : _b.call(options, { requestId: requestId, arg: arg }, { getState: getState, extra: extra })));\r\n                                    return [4 /*yield*/, Promise.race([\r\n                                            abortedPromise,\r\n                                            Promise.resolve(payloadCreator(arg, {\r\n                                                dispatch: dispatch,\r\n                                                getState: getState,\r\n                                                extra: extra,\r\n                                                requestId: requestId,\r\n                                                signal: abortController.signal,\r\n                                                abort: abort,\r\n                                                rejectWithValue: function (value, meta) {\r\n                                                    return new RejectWithValue(value, meta);\r\n                                                },\r\n                                                fulfillWithValue: function (value, meta) {\r\n                                                    return new FulfillWithMeta(value, meta);\r\n                                                }\r\n                                            })).then(function (result) {\r\n                                                if (result instanceof RejectWithValue) {\r\n                                                    throw result;\r\n                                                }\r\n                                                if (result instanceof FulfillWithMeta) {\r\n                                                    return fulfilled(result.payload, requestId, arg, result.meta);\r\n                                                }\r\n                                                return fulfilled(result, requestId, arg);\r\n                                            })\r\n                                        ])];\r\n                                case 3:\r\n                                    finalAction = _c.sent();\r\n                                    return [3 /*break*/, 5];\r\n                                case 4:\r\n                                    err_1 = _c.sent();\r\n                                    finalAction = err_1 instanceof RejectWithValue ? rejected(null, requestId, arg, err_1.payload, err_1.meta) : rejected(err_1, requestId, arg);\r\n                                    return [3 /*break*/, 5];\r\n                                case 5:\r\n                                    skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\r\n                                    if (!skipDispatch) {\r\n                                        dispatch(finalAction);\r\n                                    }\r\n                                    return [2 /*return*/, finalAction];\r\n                            }\r\n                        });\r\n                    });\r\n                }();\r\n                return Object.assign(promise2, {\r\n                    abort: abort,\r\n                    requestId: requestId,\r\n                    arg: arg,\r\n                    unwrap: function () {\r\n                        return promise2.then(unwrapResult);\r\n                    }\r\n                });\r\n            };\r\n        }\r\n        return Object.assign(actionCreator, {\r\n            pending: pending,\r\n            rejected: rejected,\r\n            fulfilled: fulfilled,\r\n            typePrefix: typePrefix\r\n        });\r\n    }\r\n    createAsyncThunk2.withTypes = function () { return createAsyncThunk2; };\r\n    return createAsyncThunk2;\r\n})();\r\nfunction unwrapResult(action) {\r\n    if (action.meta && action.meta.rejectedWithValue) {\r\n        throw action.payload;\r\n    }\r\n    if (action.error) {\r\n        throw action.error;\r\n    }\r\n    return action.payload;\r\n}\r\nfunction isThenable(value) {\r\n    return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\r\n}\r\n// src/matchers.ts\r\nvar matches = function (matcher, action) {\r\n    if (hasMatchFunction(matcher)) {\r\n        return matcher.match(action);\r\n    }\r\n    else {\r\n        return matcher(action);\r\n    }\r\n};\r\nfunction isAnyOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.some(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction isAllOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.every(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction hasExpectedRequestMetadata(action, validStatus) {\r\n    if (!action || !action.meta)\r\n        return false;\r\n    var hasValidRequestId = typeof action.meta.requestId === \"string\";\r\n    var hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\r\n    return hasValidRequestId && hasValidRequestStatus;\r\n}\r\nfunction isAsyncThunkArray(a) {\r\n    return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\r\n}\r\nfunction isPending() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isPending()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.pending; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejected() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejected()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.rejected; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejectedWithValue() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    var hasFlag = function (action) {\r\n        return action && action.meta && action.meta.rejectedWithValue;\r\n    };\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) {\r\n            var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n            return combinedMatcher(action);\r\n        };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejectedWithValue()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isFulfilled() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"fulfilled\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isFulfilled()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.fulfilled; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isAsyncThunkAction() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isAsyncThunkAction()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = [];\r\n        for (var _i = 0, asyncThunks_1 = asyncThunks; _i < asyncThunks_1.length; _i++) {\r\n            var asyncThunk = asyncThunks_1[_i];\r\n            matchers.push(asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled);\r\n        }\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\n// src/listenerMiddleware/utils.ts\r\nvar assertFunction = function (func, expected) {\r\n    if (typeof func !== \"function\") {\r\n        throw new TypeError(expected + \" is not a function\");\r\n    }\r\n};\r\nvar noop = function () {\r\n};\r\nvar catchRejection = function (promise2, onError) {\r\n    if (onError === void 0) { onError = noop; }\r\n    promise2.catch(onError);\r\n    return promise2;\r\n};\r\nvar addAbortSignalListener = function (abortSignal, callback) {\r\n    abortSignal.addEventListener(\"abort\", callback, { once: true });\r\n    return function () { return abortSignal.removeEventListener(\"abort\", callback); };\r\n};\r\nvar abortControllerWithReason = function (abortController, reason) {\r\n    var signal = abortController.signal;\r\n    if (signal.aborted) {\r\n        return;\r\n    }\r\n    if (!(\"reason\" in signal)) {\r\n        Object.defineProperty(signal, \"reason\", {\r\n            enumerable: true,\r\n            value: reason,\r\n            configurable: true,\r\n            writable: true\r\n        });\r\n    }\r\n    ;\r\n    abortController.abort(reason);\r\n};\r\n// src/listenerMiddleware/exceptions.ts\r\nvar task = \"task\";\r\nvar listener = \"listener\";\r\nvar completed = \"completed\";\r\nvar cancelled = \"cancelled\";\r\nvar taskCancelled = \"task-\" + cancelled;\r\nvar taskCompleted = \"task-\" + completed;\r\nvar listenerCancelled = listener + \"-\" + cancelled;\r\nvar listenerCompleted = listener + \"-\" + completed;\r\nvar TaskAbortError = /** @class */ (function () {\r\n    function TaskAbortError(code) {\r\n        this.code = code;\r\n        this.name = \"TaskAbortError\";\r\n        this.message = task + \" \" + cancelled + \" (reason: \" + code + \")\";\r\n    }\r\n    return TaskAbortError;\r\n}());\r\n// src/listenerMiddleware/task.ts\r\nvar validateActive = function (signal) {\r\n    if (signal.aborted) {\r\n        throw new TaskAbortError(signal.reason);\r\n    }\r\n};\r\nfunction raceWithSignal(signal, promise2) {\r\n    var cleanup = noop;\r\n    return new Promise(function (resolve, reject) {\r\n        var notifyRejection = function () { return reject(new TaskAbortError(signal.reason)); };\r\n        if (signal.aborted) {\r\n            notifyRejection();\r\n            return;\r\n        }\r\n        cleanup = addAbortSignalListener(signal, notifyRejection);\r\n        promise2.finally(function () { return cleanup(); }).then(resolve, reject);\r\n    }).finally(function () {\r\n        cleanup = noop;\r\n    });\r\n}\r\nvar runTask = function (task2, cleanUp) { return __async(void 0, null, function () {\r\n    var value, error_1;\r\n    return __generator(this, function (_c) {\r\n        switch (_c.label) {\r\n            case 0:\r\n                _c.trys.push([0, 3, 4, 5]);\r\n                return [4 /*yield*/, Promise.resolve()];\r\n            case 1:\r\n                _c.sent();\r\n                return [4 /*yield*/, task2()];\r\n            case 2:\r\n                value = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: \"ok\",\r\n                        value: value\r\n                    }];\r\n            case 3:\r\n                error_1 = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: error_1 instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\r\n                        error: error_1\r\n                    }];\r\n            case 4:\r\n                cleanUp == null ? void 0 : cleanUp();\r\n                return [7 /*endfinally*/];\r\n            case 5: return [2 /*return*/];\r\n        }\r\n    });\r\n}); };\r\nvar createPause = function (signal) {\r\n    return function (promise2) {\r\n        return catchRejection(raceWithSignal(signal, promise2).then(function (output) {\r\n            validateActive(signal);\r\n            return output;\r\n        }));\r\n    };\r\n};\r\nvar createDelay = function (signal) {\r\n    var pause = createPause(signal);\r\n    return function (timeoutMs) {\r\n        return pause(new Promise(function (resolve) { return setTimeout(resolve, timeoutMs); }));\r\n    };\r\n};\r\n// src/listenerMiddleware/index.ts\r\nvar assign = Object.assign;\r\nvar INTERNAL_NIL_TOKEN = {};\r\nvar alm = \"listenerMiddleware\";\r\nvar createFork = function (parentAbortSignal, parentBlockingPromises) {\r\n    var linkControllers = function (controller) { return addAbortSignalListener(parentAbortSignal, function () { return abortControllerWithReason(controller, parentAbortSignal.reason); }); };\r\n    return function (taskExecutor, opts) {\r\n        assertFunction(taskExecutor, \"taskExecutor\");\r\n        var childAbortController = new AbortController();\r\n        linkControllers(childAbortController);\r\n        var result = runTask(function () { return __async(void 0, null, function () {\r\n            var result2;\r\n            return __generator(this, function (_c) {\r\n                switch (_c.label) {\r\n                    case 0:\r\n                        validateActive(parentAbortSignal);\r\n                        validateActive(childAbortController.signal);\r\n                        return [4 /*yield*/, taskExecutor({\r\n                                pause: createPause(childAbortController.signal),\r\n                                delay: createDelay(childAbortController.signal),\r\n                                signal: childAbortController.signal\r\n                            })];\r\n                    case 1:\r\n                        result2 = _c.sent();\r\n                        validateActive(childAbortController.signal);\r\n                        return [2 /*return*/, result2];\r\n                }\r\n            });\r\n        }); }, function () { return abortControllerWithReason(childAbortController, taskCompleted); });\r\n        if (opts == null ? void 0 : opts.autoJoin) {\r\n            parentBlockingPromises.push(result);\r\n        }\r\n        return {\r\n            result: createPause(parentAbortSignal)(result),\r\n            cancel: function () {\r\n                abortControllerWithReason(childAbortController, taskCancelled);\r\n            }\r\n        };\r\n    };\r\n};\r\nvar createTakePattern = function (startListening, signal) {\r\n    var take = function (predicate, timeout) { return __async(void 0, null, function () {\r\n        var unsubscribe, tuplePromise, promises, output;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    validateActive(signal);\r\n                    unsubscribe = function () {\r\n                    };\r\n                    tuplePromise = new Promise(function (resolve, reject) {\r\n                        var stopListening = startListening({\r\n                            predicate: predicate,\r\n                            effect: function (action, listenerApi) {\r\n                                listenerApi.unsubscribe();\r\n                                resolve([\r\n                                    action,\r\n                                    listenerApi.getState(),\r\n                                    listenerApi.getOriginalState()\r\n                                ]);\r\n                            }\r\n                        });\r\n                        unsubscribe = function () {\r\n                            stopListening();\r\n                            reject();\r\n                        };\r\n                    });\r\n                    promises = [\r\n                        tuplePromise\r\n                    ];\r\n                    if (timeout != null) {\r\n                        promises.push(new Promise(function (resolve) { return setTimeout(resolve, timeout, null); }));\r\n                    }\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, , 3, 4]);\r\n                    return [4 /*yield*/, raceWithSignal(signal, Promise.race(promises))];\r\n                case 2:\r\n                    output = _c.sent();\r\n                    validateActive(signal);\r\n                    return [2 /*return*/, output];\r\n                case 3:\r\n                    unsubscribe();\r\n                    return [7 /*endfinally*/];\r\n                case 4: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    return function (predicate, timeout) { return catchRejection(take(predicate, timeout)); };\r\n};\r\nvar getListenerEntryPropsFrom = function (options) {\r\n    var type = options.type, actionCreator = options.actionCreator, matcher = options.matcher, predicate = options.predicate, effect = options.effect;\r\n    if (type) {\r\n        predicate = createAction(type).match;\r\n    }\r\n    else if (actionCreator) {\r\n        type = actionCreator.type;\r\n        predicate = actionCreator.match;\r\n    }\r\n    else if (matcher) {\r\n        predicate = matcher;\r\n    }\r\n    else if (predicate) {\r\n    }\r\n    else {\r\n        throw new Error(\"Creating or removing a listener requires one of the known fields for matching an action\");\r\n    }\r\n    assertFunction(effect, \"options.listener\");\r\n    return { predicate: predicate, type: type, effect: effect };\r\n};\r\nvar createListenerEntry = function (options) {\r\n    var _c = getListenerEntryPropsFrom(options), type = _c.type, predicate = _c.predicate, effect = _c.effect;\r\n    var id = nanoid();\r\n    var entry = {\r\n        id: id,\r\n        effect: effect,\r\n        type: type,\r\n        predicate: predicate,\r\n        pending: new Set(),\r\n        unsubscribe: function () {\r\n            throw new Error(\"Unsubscribe not initialized\");\r\n        }\r\n    };\r\n    return entry;\r\n};\r\nvar cancelActiveListeners = function (entry) {\r\n    entry.pending.forEach(function (controller) {\r\n        abortControllerWithReason(controller, listenerCancelled);\r\n    });\r\n};\r\nvar createClearListenerMiddleware = function (listenerMap) {\r\n    return function () {\r\n        listenerMap.forEach(cancelActiveListeners);\r\n        listenerMap.clear();\r\n    };\r\n};\r\nvar safelyNotifyError = function (errorHandler, errorToNotify, errorInfo) {\r\n    try {\r\n        errorHandler(errorToNotify, errorInfo);\r\n    }\r\n    catch (errorHandlerError) {\r\n        setTimeout(function () {\r\n            throw errorHandlerError;\r\n        }, 0);\r\n    }\r\n};\r\nvar addListener = createAction(alm + \"/add\");\r\nvar clearAllListeners = createAction(alm + \"/removeAll\");\r\nvar removeListener = createAction(alm + \"/remove\");\r\nvar defaultErrorHandler = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    console.error.apply(console, __spreadArray([alm + \"/error\"], args));\r\n};\r\nfunction createListenerMiddleware(middlewareOptions) {\r\n    var _this = this;\r\n    if (middlewareOptions === void 0) { middlewareOptions = {}; }\r\n    var listenerMap = new Map();\r\n    var extra = middlewareOptions.extra, _c = middlewareOptions.onError, onError = _c === void 0 ? defaultErrorHandler : _c;\r\n    assertFunction(onError, \"onError\");\r\n    var insertEntry = function (entry) {\r\n        entry.unsubscribe = function () { return listenerMap.delete(entry.id); };\r\n        listenerMap.set(entry.id, entry);\r\n        return function (cancelOptions) {\r\n            entry.unsubscribe();\r\n            if (cancelOptions == null ? void 0 : cancelOptions.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        };\r\n    };\r\n    var findListenerEntry = function (comparator) {\r\n        for (var _i = 0, _c = Array.from(listenerMap.values()); _i < _c.length; _i++) {\r\n            var entry = _c[_i];\r\n            if (comparator(entry)) {\r\n                return entry;\r\n            }\r\n        }\r\n        return void 0;\r\n    };\r\n    var startListening = function (options) {\r\n        var entry = findListenerEntry(function (existingEntry) { return existingEntry.effect === options.effect; });\r\n        if (!entry) {\r\n            entry = createListenerEntry(options);\r\n        }\r\n        return insertEntry(entry);\r\n    };\r\n    var stopListening = function (options) {\r\n        var _c = getListenerEntryPropsFrom(options), type = _c.type, effect = _c.effect, predicate = _c.predicate;\r\n        var entry = findListenerEntry(function (entry2) {\r\n            var matchPredicateOrType = typeof type === \"string\" ? entry2.type === type : entry2.predicate === predicate;\r\n            return matchPredicateOrType && entry2.effect === effect;\r\n        });\r\n        if (entry) {\r\n            entry.unsubscribe();\r\n            if (options.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        }\r\n        return !!entry;\r\n    };\r\n    var notifyListener = function (entry, action, api, getOriginalState) { return __async(_this, null, function () {\r\n        var internalTaskController, take, autoJoinPromises, listenerError_1;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    internalTaskController = new AbortController();\r\n                    take = createTakePattern(startListening, internalTaskController.signal);\r\n                    autoJoinPromises = [];\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, 3, 4, 6]);\r\n                    entry.pending.add(internalTaskController);\r\n                    return [4 /*yield*/, Promise.resolve(entry.effect(action, assign({}, api, {\r\n                            getOriginalState: getOriginalState,\r\n                            condition: function (predicate, timeout) { return take(predicate, timeout).then(Boolean); },\r\n                            take: take,\r\n                            delay: createDelay(internalTaskController.signal),\r\n                            pause: createPause(internalTaskController.signal),\r\n                            extra: extra,\r\n                            signal: internalTaskController.signal,\r\n                            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n                            unsubscribe: entry.unsubscribe,\r\n                            subscribe: function () {\r\n                                listenerMap.set(entry.id, entry);\r\n                            },\r\n                            cancelActiveListeners: function () {\r\n                                entry.pending.forEach(function (controller, _, set) {\r\n                                    if (controller !== internalTaskController) {\r\n                                        abortControllerWithReason(controller, listenerCancelled);\r\n                                        set.delete(controller);\r\n                                    }\r\n                                });\r\n                            }\r\n                        })))];\r\n                case 2:\r\n                    _c.sent();\r\n                    return [3 /*break*/, 6];\r\n                case 3:\r\n                    listenerError_1 = _c.sent();\r\n                    if (!(listenerError_1 instanceof TaskAbortError)) {\r\n                        safelyNotifyError(onError, listenerError_1, {\r\n                            raisedBy: \"effect\"\r\n                        });\r\n                    }\r\n                    return [3 /*break*/, 6];\r\n                case 4: return [4 /*yield*/, Promise.allSettled(autoJoinPromises)];\r\n                case 5:\r\n                    _c.sent();\r\n                    abortControllerWithReason(internalTaskController, listenerCompleted);\r\n                    entry.pending.delete(internalTaskController);\r\n                    return [7 /*endfinally*/];\r\n                case 6: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    var clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\r\n    var middleware = function (api) { return function (next) { return function (action) {\r\n        if (!isAction(action)) {\r\n            return next(action);\r\n        }\r\n        if (addListener.match(action)) {\r\n            return startListening(action.payload);\r\n        }\r\n        if (clearAllListeners.match(action)) {\r\n            clearListenerMiddleware();\r\n            return;\r\n        }\r\n        if (removeListener.match(action)) {\r\n            return stopListening(action.payload);\r\n        }\r\n        var originalState = api.getState();\r\n        var getOriginalState = function () {\r\n            if (originalState === INTERNAL_NIL_TOKEN) {\r\n                throw new Error(alm + \": getOriginalState can only be called synchronously\");\r\n            }\r\n            return originalState;\r\n        };\r\n        var result;\r\n        try {\r\n            result = next(action);\r\n            if (listenerMap.size > 0) {\r\n                var currentState = api.getState();\r\n                var listenerEntries = Array.from(listenerMap.values());\r\n                for (var _i = 0, listenerEntries_1 = listenerEntries; _i < listenerEntries_1.length; _i++) {\r\n                    var entry = listenerEntries_1[_i];\r\n                    var runListener = false;\r\n                    try {\r\n                        runListener = entry.predicate(action, currentState, originalState);\r\n                    }\r\n                    catch (predicateError) {\r\n                        runListener = false;\r\n                        safelyNotifyError(onError, predicateError, {\r\n                            raisedBy: \"predicate\"\r\n                        });\r\n                    }\r\n                    if (!runListener) {\r\n                        continue;\r\n                    }\r\n                    notifyListener(entry, action, api, getOriginalState);\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            originalState = INTERNAL_NIL_TOKEN;\r\n        }\r\n        return result;\r\n    }; }; };\r\n    return {\r\n        middleware: middleware,\r\n        startListening: startListening,\r\n        stopListening: stopListening,\r\n        clearListeners: clearListenerMiddleware\r\n    };\r\n}\r\n// src/autoBatchEnhancer.ts\r\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\r\nvar prepareAutoBatched = function () { return function (payload) {\r\n    var _c;\r\n    return ({\r\n        payload: payload,\r\n        meta: (_c = {}, _c[SHOULD_AUTOBATCH] = true, _c)\r\n    });\r\n}; };\r\nvar promise;\r\nvar queueMicrotaskShim = typeof queueMicrotask === \"function\" ? queueMicrotask.bind(typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : globalThis) : function (cb) { return (promise || (promise = Promise.resolve())).then(cb).catch(function (err) { return setTimeout(function () {\r\n    throw err;\r\n}, 0); }); };\r\nvar createQueueWithTimer = function (timeout) {\r\n    return function (notify) {\r\n        setTimeout(notify, timeout);\r\n    };\r\n};\r\nvar rAF = typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10);\r\nvar autoBatchEnhancer = function (options) {\r\n    if (options === void 0) { options = { type: \"raf\" }; }\r\n    return function (next) { return function () {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var store = next.apply(void 0, args);\r\n        var notifying = true;\r\n        var shouldNotifyAtEndOfTick = false;\r\n        var notificationQueued = false;\r\n        var listeners = new Set();\r\n        var queueCallback = options.type === \"tick\" ? queueMicrotaskShim : options.type === \"raf\" ? rAF : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\r\n        var notifyListeners = function () {\r\n            notificationQueued = false;\r\n            if (shouldNotifyAtEndOfTick) {\r\n                shouldNotifyAtEndOfTick = false;\r\n                listeners.forEach(function (l) { return l(); });\r\n            }\r\n        };\r\n        return Object.assign({}, store, {\r\n            subscribe: function (listener2) {\r\n                var wrappedListener = function () { return notifying && listener2(); };\r\n                var unsubscribe = store.subscribe(wrappedListener);\r\n                listeners.add(listener2);\r\n                return function () {\r\n                    unsubscribe();\r\n                    listeners.delete(listener2);\r\n                };\r\n            },\r\n            dispatch: function (action) {\r\n                var _a;\r\n                try {\r\n                    notifying = !((_a = action == null ? void 0 : action.meta) == null ? void 0 : _a[SHOULD_AUTOBATCH]);\r\n                    shouldNotifyAtEndOfTick = !notifying;\r\n                    if (shouldNotifyAtEndOfTick) {\r\n                        if (!notificationQueued) {\r\n                            notificationQueued = true;\r\n                            queueCallback(notifyListeners);\r\n                        }\r\n                    }\r\n                    return store.dispatch(action);\r\n                }\r\n                finally {\r\n                    notifying = true;\r\n                }\r\n            }\r\n        });\r\n    }; };\r\n};\r\n// src/index.ts\r\n(0,immer__WEBPACK_IMPORTED_MODULE_2__.enableES5)();\r\n\r\n//# sourceMappingURL=redux-toolkit.esm.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js\n");

/***/ })

};
;
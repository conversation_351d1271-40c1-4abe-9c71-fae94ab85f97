const sequelize = require("../config/database");

const models = require('../models/init-models')(sequelize);
const { Op , Sequelize } = require('sequelize');
const moment = require('moment')
require('moment/locale/fa')
moment.locale('fa');

// Helper function to calculate distance between two coordinates using Have<PERSON>ine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return distance;
}

// Helper function to round to nearest 0.25
function roundToQuarter(value) {
    return Math.round(value * 4) / 4;
}

// Helper function to get current Libyan time (UTC+2)
function getLibyanTime() {
    const now = new Date();
    // Convert to Libyan time (UTC+2)
    const libyanTime = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    return libyanTime;
}

// Helper function to get current day and time in Libyan timezone
function getLibyanDayAndTime() {
    const libyanTime = getLibyanTime();
    const currentDay = libyanTime.getUTCDay(); // Use UTC methods since we already adjusted for timezone
    const currentTime = libyanTime.toISOString().substring(11, 19); // HH:MM:SS format

    console.log(`Libyan time: ${libyanTime.toISOString()}, Day: ${currentDay}, Time: ${currentTime}`);

    return { currentDay, currentTime };
}

// Helper function to compare times properly
function isTimeInRange(currentTime, openTime, closeTime) {
    // Helper function to convert time to string format
    function timeToString(time) {
        if (!time) return null;
        
        // If it's already a string, return it
        if (typeof time === 'string') {
            return time;
        }
        
        // If it's a Date object, extract time portion
        if (time instanceof Date) {
            return time.toTimeString().substring(0, 8); // HH:MM:SS
        }
        
        // If it has toString method, use it
        if (time && typeof time.toString === 'function') {
            return time.toString();
        }
        
        return null;
    }
    
    // Convert all times to strings
    const currentStr = timeToString(currentTime);
    const openStr = timeToString(openTime);
    const closeStr = timeToString(closeTime);
    
    // Check if any conversion failed
    if (!currentStr || !openStr || !closeStr) {
        return false;
    }
    
    // Convert time strings to comparable format (remove seconds if present)
    const current = currentStr.substring(0, 5); // HH:MM
    const open = openStr.substring(0, 5); // HH:MM
    const close = closeStr.substring(0, 5); // HH:MM

    if (close > open) {
        // Normal case: opens and closes on same day (e.g., 09:00 - 22:00)
        return current >= open && current <= close;
    } else {
        // Special case: closes after midnight (e.g., 18:00 - 02:00)
        return current >= open || current <= close;
    }
}

const { Restaurant, Category, Product, Restaurant_Category_Product, WorkingTimes, Order, RestaurantType, DriverModerate } = models;

exports.getAllRestaurants = async(req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        // Get user location from query parameters
        const userLat = parseFloat(req.query.userLat);
        const userLng = parseFloat(req.query.userLng);

        // Use Libyan time (UTC+2) for accurate restaurant status
        const { currentDay, currentTime } = getLibyanDayAndTime();

        // Get driver moderation settings for delivery fee calculation
        const driverModerate = await DriverModerate.findOne({
            order: [['ID', 'DESC']] // Get the latest settings
        });

        const { count, rows: restaurants } = await Restaurant.findAndCountAll({
            where: {
                Status: { [Op.in]: [1] }
            },
            include: [
                {
                    model: WorkingTimes,
                    as: 'WorkingTimes',
                    where: {
                        DayOfWeek: currentDay
                    },
                    required: false
                },
                {
                    model: RestaurantType,
                    as: 'RestaurantType',
                    attributes: ['TypeName'],
                    required: false
                }
            ],
            limit,
            offset
        });

        const restaurantsWithStatus = restaurants.map(restaurant => {
            const restaurantData = restaurant.toJSON();
            const todayWorkingTime = restaurantData.WorkingTimes && restaurantData.WorkingTimes[0];

            let isClosed = true;

            if (todayWorkingTime) {
                // Check if restaurant is explicitly closed for the day
                if (todayWorkingTime.IsClosed === true) {
                    isClosed = true;
                    console.log(`Restaurant ${restaurantData.RestaurantID} is explicitly closed today`);
                }
                // Check if opening/closing times are null (closed)
                else if (todayWorkingTime.OpeningTime === null || todayWorkingTime.ClosingTime === null) {
                    isClosed = true;
                    console.log(`Restaurant ${restaurantData.RestaurantID} has null opening/closing times`);
                }
                // Check if current time is within opening hours
                else {
                    const openTime = todayWorkingTime.OpeningTime;
                    const closeTime = todayWorkingTime.ClosingTime;

                    // Use the helper function for proper time comparison
                    const isOpen = isTimeInRange(currentTime, openTime, closeTime);
                    isClosed = !isOpen;

                    console.log(`Restaurant ${restaurantData.RestaurantID}: Current time ${currentTime}, Open: ${openTime}, Close: ${closeTime}, IsClosed: ${isClosed}`);
                }
            } else {
                console.log(`Restaurant ${restaurantData.RestaurantID} has no working time for day ${currentDay}`);
            }

            delete restaurantData.WorkingTimes;

            // Calculate distance and delivery fee if user location is provided
            let distance = null;
            let deliveryFee = null;

            if (!isNaN(userLat) && !isNaN(userLng) && restaurantData.latitude && restaurantData.longitude) {
                // Calculate distance between user and restaurant
                distance = calculateDistance(userLat, userLng, restaurantData.latitude, restaurantData.longitude);
                distance = roundToQuarter(distance); // Round to nearest 0.25

                // Calculate delivery fee based on driver moderation settings
                if (driverModerate) {
                    if (driverModerate.IsConst === true) {
                        // Use constant delivery fee
                        deliveryFee = driverModerate.ConstValue || 0;
                    } else {
                        // Calculate based on distance and cost per kilometer
                        const costPerKm = driverModerate.CostPerKilometer || 0;
                        deliveryFee = distance * costPerKm;
                    }
                    // Round delivery fee to nearest 0.25
                    deliveryFee = roundToQuarter(deliveryFee);
                }
            }

            return {
                ...restaurantData,
                TypeName: restaurantData.RestaurantType?.TypeName || 'غير محدد',
                IsClosed: isClosed,
                IsOperational: restaurantData.Stats === 1 && !isClosed,
                Distance: distance,
                DeliveryFee: deliveryFee
            };
        });

        const totalPages = Math.ceil(count / limit);

        res.json({
            data: restaurantsWithStatus,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: count,
                itemsPerPage: limit
            }
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getRestaurant = async(req, res) => {
    try {
        const restaurant = await Restaurant.findOne({
            where: {
                RestaurantID: req.params.id,
                Status: 1
            },
            include: [
                {
                    model: Restaurant_Category_Product,
                    as: 'Restaurant_Category_Products',
                    include: [{
                        model: Category,
                        as: 'Category',
                        where: { Status: 1 },
                        include: [{
                            model: Product,
                            as: 'Products',
                            where: { Status: 1 }
                        }]
                    }]
                },
                {
                    model: RestaurantType,
                    as: 'RestaurantType',
                    attributes: ['TypeName']
                }
            ]
        });

        if (!restaurant) {
            return res.status(404).json({ message: 'Restaurant not found' });
        }
        res.json(restaurant);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getRestaurantData = async(req, res) => {
    try {
        // Use Libyan time (UTC+2) for accurate restaurant status
        const { currentDay, currentTime } = getLibyanDayAndTime();

        const restaurant = await Restaurant.findOne({
            where: {
                RestaurantID: req.params.id,
                Status: 1
            },
            attributes: ['RestaurantID', 'City', 'Address', 'Image', 'Name', 'longitude', 'latitude', 'RestaurantTypeID'],
            include: [
                {
                    model: RestaurantType,
                    as: 'RestaurantType',
                    attributes: ['TypeName']
                },
                {
                    model: WorkingTimes,
                    as: 'WorkingTimes',
                    where: {
                        DayOfWeek: currentDay
                    },
                    required: false
                }
            ]
        });

        if (!restaurant) {
            return res.status(404).json({ message: 'Restaurant not found' });
        }

        const restaurantData = restaurant.toJSON();
        const todayWorkingTime = restaurantData.WorkingTimes && restaurantData.WorkingTimes[0];

        let isClosed = true;

        if (todayWorkingTime) {
            // Check if restaurant is explicitly closed for the day
            if (todayWorkingTime.IsClosed === true) {
                isClosed = true;
                console.log(`Restaurant ${restaurantData.RestaurantID} is explicitly closed today`);
            }
            // Check if opening/closing times are null (closed)
            else if (todayWorkingTime.OpeningTime === null || todayWorkingTime.ClosingTime === null) {
                isClosed = true;
                console.log(`Restaurant ${restaurantData.RestaurantID} has null opening/closing times`);
            }
            // Check if current time is within opening hours
            else {
                const openTime = todayWorkingTime.OpeningTime;
                const closeTime = todayWorkingTime.ClosingTime;

                // Use the helper function for proper time comparison
                const isOpen = isTimeInRange(currentTime, openTime, closeTime);
                isClosed = !isOpen;

                console.log(`Restaurant ${restaurantData.RestaurantID}: Current time ${currentTime}, Open: ${openTime}, Close: ${closeTime}, IsClosed: ${isClosed}`);
            }
        } else {
            console.log(`Restaurant ${restaurantData.RestaurantID} has no working time for day ${currentDay}`);
        }

        // Remove WorkingTimes from response
        delete restaurantData.WorkingTimes;

        // Add the calculated status fields
        const responseData = {
            ...restaurantData,
            TypeName: restaurantData.RestaurantType?.TypeName || 'غير محدد',
            IsClosed: isClosed,
            IsOperational: !isClosed, // Add this field for compatibility
            Status: 1 // Restaurant is active since we filtered by Status: 1
        };

        res.json({ data: responseData });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.createRestaurant = async(req, res) => {
    try {
        if (!req.body.RestaurantTypeID) {
            return res.status(400).json({ message: 'Restaurant Type is required' });
        }

        const restaurant = await Restaurant.create({
            ...req.body,
            Status: 1,
            Stats: 0
        });
        res.status(201).json(restaurant);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.updateRestaurant = async(req, res) => {
    try {
        const restaurant = await Restaurant.findOne({
            where: {
                RestaurantID: req.params.id,
                Status: 1
            }
        });
        if (!restaurant) {
            return res.status(404).json({ message: 'Restaurant not found' });
        }

        if (!req.body.RestaurantTypeID) {
            return res.status(400).json({ message: 'Restaurant Type is required' });
        }

        const updateData = { 
            ...req.body, 
            UpdateDate: Sequelize.Sequelize.fn('getdate')
        };
        await restaurant.update(updateData);

        const updatedRestaurant = await Restaurant.findOne({
            where: { RestaurantID: req.params.id },
            include: [{
                model: RestaurantType,
                as: 'RestaurantType',
                attributes: ['TypeName']
            }]
        });

        res.json(updatedRestaurant);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.changeStatus = async(req, res) => {
    try {
        const restaurant = await Restaurant.findByPk(req.params.id);
        if (!restaurant) {
            return res.status(404).json({ message: 'Restaurant not found' });
        }
        await restaurant.update({ Status: req.body.status });
        res.json({ message: 'Restaurant status updated successfully' });
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.searchRestaurants = async(req, res) => {
    try {
        const { name } = req.query;

        if (!name) {
            return res.status(400).json({
                success: false,
                message: "Search name is required"
            });
        }

        const restaurants = await Restaurant.findAll({
            where: {
                Name: {
                    [Op.like]: `%${name}%`
                }
            },
            attributes: ['RestaurantID', 'Name', 'City', 'Address', 'Stats', 'Money'],
        });

        res.status(200).json({
            success: true,
            data: restaurants
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Error searching restaurants",
            error: error.message
        });
    }
};


exports.getRestaurantWorkingTimes = async(req, res) => {
    try {
        const workingTimes = await WorkingTimes.findAll({
            where: {
                RestaurantID: req.params.id
            }
        });
        res.json(workingTimes);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.updateWorkingTimes = async(req, res) => {
    try {
        const { workingTimes } = req.body;
        const restaurantId = req.params.id;

        const updatedWorkingTimes = [];

        for (const wt of workingTimes) {
            if ((wt.OpeningTime === null && wt.ClosingTime !== null) ||
                (wt.OpeningTime !== null && wt.ClosingTime === null)) {
                return res.status(400).json({
                    message: "Both OpeningTime and ClosingTime must be either null or valid times"
                });
            }

            if (wt.OpeningTime === null && wt.ClosingTime === null) {
                wt.IsClosed = true;
            }

            const [existingWorkingTime] = await WorkingTimes.findAll({
                where: {
                    RestaurantID: restaurantId,
                    DayOfWeek: wt.DayOfWeek
                }
            });

            if (existingWorkingTime) {
                await existingWorkingTime.update({
                    ...wt,
                    UpdateDate: Sequelize.fn('GETDATE'),
                    UpdateBy: req.body.UpdateBy || null
                });
                updatedWorkingTimes.push(existingWorkingTime);
            } else {
                const newWorkingTime = await WorkingTimes.create({
                    ...wt,
                    RestaurantID: restaurantId,
                    InsertBy: req.body.UpdateBy || null
                });
                updatedWorkingTimes.push(newWorkingTime);
            }
        }

        res.json(updatedWorkingTimes);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.updateRestaurantStats = async(req, res) => {
    try {
        const { Stats } = req.body;
        const restaurant = await Restaurant.findByPk(req.params.id);

        if (!restaurant) {
            return res.status(404).json({ message: 'Restaurant not found' });
        }

        await restaurant.update({
            Stats,
            UpdateDate: new Date(),
            UpdateBy: req.body.UpdateBy || null
        });

        res.json(restaurant);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.getRestaurantDashboardData = async(req, res) => {
    try {
        const { restaurantId } = req.body;

        if (!restaurantId) {
            return res.status(400).json({ 
                success: false, 
                message: 'Restaurant ID is required' 
            });
        }

        console.log(`Processing dashboard request for restaurant ID: ${restaurantId}`);

        // Use Libyan time (UTC+2) for accurate restaurant status
        const { currentDay, currentTime } = getLibyanDayAndTime();
        const now = getLibyanTime();
        const today = new Date(now.setHours(0, 0, 0, 0));
        const oneWeekAgo = new Date(today);
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const oneMonthAgo = new Date(today);
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

        // Get restaurant details with working times
        const restaurant = await Restaurant.findOne({
            where: {
                RestaurantID: restaurantId
            },
            attributes: ['RestaurantID', 'Name', 'Address', 'Stats', 'Status'],
            include: [{
                model: WorkingTimes,
                as: 'WorkingTimes',
                where: {
                    DayOfWeek: currentDay
                },
                required: false
            }]
        }).catch(err => {
            console.error('Error fetching restaurant:', err);
            return null;
        });

        if (!restaurant) {
            return res.status(404).json({ 
                success: false, 
                message: 'Restaurant not found or inactive' 
            });
        }

        // Determine if restaurant is open based on working times using Libyan time
        const restaurantData = restaurant.toJSON();
        const todayWorkingTime = restaurantData.WorkingTimes && restaurantData.WorkingTimes[0];
        let isOpen = false;

        console.log(`Dashboard: Checking restaurant ${restaurantId} status for day ${currentDay} at time ${currentTime}`);
        console.log(`Dashboard: Working time data:`, todayWorkingTime);

        if (todayWorkingTime) {
            // Check if restaurant is explicitly closed for the day
            if (todayWorkingTime.IsClosed === true) {
                isOpen = false;
                console.log(`Dashboard: Restaurant ${restaurantId} is explicitly closed today (IsClosed = true)`);
            }
            // Check if opening/closing times are null (closed)
            else if (todayWorkingTime.OpeningTime === null || todayWorkingTime.ClosingTime === null) {
                isOpen = false;
                console.log(`Dashboard: Restaurant ${restaurantId} has null opening/closing times - OpeningTime: ${todayWorkingTime.OpeningTime}, ClosingTime: ${todayWorkingTime.ClosingTime}`);
            }
            // Check if current time is within opening hours
            else {
                const openTime = todayWorkingTime.OpeningTime;
                const closeTime = todayWorkingTime.ClosingTime;

                console.log(`Dashboard: Comparing times - Current: ${currentTime}, Open: ${openTime}, Close: ${closeTime}`);

                // Use the helper function for proper time comparison
                isOpen = isTimeInRange(currentTime, openTime, closeTime);

                console.log(`Dashboard: Final result - Restaurant ${restaurantId} IsOpen: ${isOpen}`);
            }
        } else {
            console.log(`Dashboard: Restaurant ${restaurantId} has no working time for day ${currentDay}`);
        }

        // Get all orders for this restaurant - handle possible errors
        const allOrders = await Order.findAll({
            where: {
                CartID: {
                    [Op.in]: Sequelize.literal(`(SELECT CartID FROM Cart WHERE RestaurantID = ${restaurantId})`)
                }
            },
            attributes: [
                'OrderID', 
                'OrderDate',
                'TotalAmount',
                'Status',
                'OrderNumber',
                'CustomerID'
            ],
            order: [
                ['OrderDate', 'DESC']
            ]
        }).catch(err => {
            console.error('Error fetching orders:', err);
            return [];
        });

        console.log(`Found ${allOrders.length} orders for restaurant ID: ${restaurantId}`);

        // Calculate various statistics
        const totalOrders = allOrders.length;
        const todayOrders = allOrders.filter(order => {
            try {
                const orderDate = new Date(order.OrderDate);
                return orderDate >= today;
            } catch (e) {
                console.error('Error parsing order date:', e);
                return false;
            }
        }).length;
        
        const pendingOrders = allOrders.filter(order => 
            order.Status === 'pending' || 
            order.Status === 'in-progress' || 
            order.Status === 0 || 
            order.Status === 1
        ).length;
        
        const completedOrders = allOrders.filter(order => 
            order.Status === 'completed' || 
            order.Status === 'delivered' || 
            order.Status === 2 || 
            order.Status === 3
        ).length;
        
        // Calculate revenue statistics safely
        const totalRevenue = allOrders.reduce((sum, order) => {
            const amount = order.TotalAmount ? parseFloat(order.TotalAmount) : 0;
            return sum + (isNaN(amount) ? 0 : amount);
        }, 0);
        
        const todayRevenue = allOrders
            .filter(order => {
                try {
                    const orderDate = new Date(order.OrderDate);
                    return orderDate >= today;
                } catch (e) {
                    return false;
                }
            })
            .reduce((sum, order) => {
                const amount = order.TotalAmount ? parseFloat(order.TotalAmount) : 0;
                return sum + (isNaN(amount) ? 0 : amount);
            }, 0);
        
        // Calculate average order value
        const averageOrderValue = totalOrders > 0 ? (totalRevenue / totalOrders) : 0;
        
        // Estimate profit (assuming 30% profit margin)
        const profitMargin = 0.3;
        const totalProfit = totalRevenue * profitMargin;

        // Generate data for daily sales chart (last 7 days)
        const last7Days = Array.from({ length: 7 }, (_, i) => {
            const date = new Date();
            date.setHours(0, 0, 0, 0);
            date.setDate(date.getDate() - i);
            return date;
        }).reverse();

        const dailySales = last7Days.map(date => {
            const dateString = date.toISOString().split('T')[0];
            const nextDay = new Date(date);
            nextDay.setDate(nextDay.getDate() + 1);
            
            const ordersOnDay = allOrders.filter(order => {
                try {
                    const orderDate = new Date(order.OrderDate);
                    return orderDate >= date && orderDate < nextDay;
                } catch (e) {
                    return false;
                }
            });
            
            const amount = ordersOnDay.reduce((sum, order) => {
                const orderAmount = order.TotalAmount ? parseFloat(order.TotalAmount) : 0;
                return sum + (isNaN(orderAmount) ? 0 : orderAmount);
            }, 0);
            
            return {
                date: dateString,
                amount: amount,
                count: ordersOnDay.length
            };
        });

        // Get top products safely or provide fallback data
        let topProducts = [];
        try {
            // First check if needed tables and relations exist
            const hasRequiredTables = await sequelize.query(`
                SELECT 1 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME IN ('OrderItem', 'Order', 'Cart', 'Product', 'Category')
            `, { 
                type: Sequelize.QueryTypes.SELECT 
            });
            
            if (hasRequiredTables && hasRequiredTables.length >= 5) {
                // All required tables exist, try the query
                topProducts = await sequelize.query(`
                    SELECT 
                        p.ProductID as productId,
                        p.Name as productName,
                        c.CategoryName as category,
                        SUM(oi.Quantity) as quantity,
                        SUM(oi.Price * oi.Quantity) as revenue
                    FROM 
                        OrderItem oi
                        JOIN Order o ON oi.OrderID = o.OrderID
                        JOIN Cart ca ON o.CartID = ca.CartID
                        JOIN Product p ON oi.ProductID = p.ProductID
                        JOIN Category c ON p.CategoryID = c.CategoryID
                    WHERE 
                        ca.RestaurantID = :restaurantId
                    GROUP BY 
                        p.ProductID, p.Name, c.CategoryName
                    ORDER BY 
                        quantity DESC
                    LIMIT 5
                `, {
                    replacements: { 
                        restaurantId: restaurantId
                    },
                    type: Sequelize.QueryTypes.SELECT
                });
            } else {
                console.log('Some required tables are missing. Using empty top products array.');
            }
        } catch (err) {
            console.error('Error fetching top products:', err);
            // Return empty array if the query fails
        }

        // Format recent orders for the dashboard
        const recentOrders = allOrders.slice(0, 5).map(order => ({
            orderId: order.OrderID,
            orderNumber: order.OrderNumber || `#${order.OrderID}`,
            customerName: `Customer ${order.CustomerID || 'Unknown'}`,
            orderDate: order.OrderDate,
            totalAmount: parseFloat(order.TotalAmount || 0),
            status: order.Status
        }));

        // Build the complete response
        const response = {
            success: true,
            data: {
                restaurant: {
                    id: restaurantData.RestaurantID,
                    name: restaurantData.Name,
                    address: restaurantData.Address || '',
                    isOpen: isOpen
                },
                statistics: {
                    totalOrders,
                    todayOrders,
                    pendingOrders,
                    completedOrders,
                    totalRevenue,
                    todayRevenue,
                    averageOrderValue,
                    totalProfit,
                    profitMargin
                },
                dailySales,
                recentOrders,
                topProducts
            }
        };

        console.log('Dashboard data prepared successfully');
        res.json(response);
    } catch (error) {
        console.error('Error in getRestaurantDashboardData:', error);
        // Send a more detailed error message in development
        const message = process.env.NODE_ENV === 'development' 
            ? `Error: ${error.message}\nStack: ${error.stack}` 
            : 'An error occurred while fetching dashboard data';
            
        res.status(500).json({ 
            success: false, 
            message: message
        });
    }
};

exports.updateContactInfo = async(req, res) => {
    try {
        const restaurantId = req.params.id;
        const { Owner, firstNumber, secondNumber, UpdateBy } = req.body;

        const restaurant = await Restaurant.findByPk(restaurantId);
        
        if (!restaurant) {
            return res.status(404).json({ 
                success: false, 
                message: 'المطعم غير موجود' 
            });
        }

        await restaurant.update({
            Owner,
            firstNumber,
            secondNumber,
            UpdateDate: Sequelize.Sequelize.fn('getdate'),
            UpdateBy: UpdateBy || null
        });

        return res.json({
            success: true,
            message: 'تم تحديث بيانات الاتصال بنجاح',
            data: {
                RestaurantID: restaurant.RestaurantID,
                Name: restaurant.Name,
                Owner: restaurant.Owner,
                firstNumber: restaurant.firstNumber,
                secondNumber: restaurant.secondNumber
            }
        });
    } catch (error) {
        console.error('Error updating contact info:', error);
        return res.status(500).json({ 
            success: false, 
            message: 'حدث خطأ أثناء تحديث بيانات الاتصال',
            error: error.message 
        });
    }
};

module.exports = {
    getAllRestaurants: exports.getAllRestaurants,
    getRestaurant: exports.getRestaurant,
    getRestaurantData: exports.getRestaurantData,
    createRestaurant: exports.createRestaurant,
    updateRestaurant: exports.updateRestaurant,
    changeStatus: exports.changeStatus,
    searchRestaurants: exports.searchRestaurants,
    getRestaurantWorkingTimes: exports.getRestaurantWorkingTimes,
    updateWorkingTimes: exports.updateWorkingTimes,
    updateRestaurantStats: exports.updateRestaurantStats,
    getRestaurantDashboardData: exports.getRestaurantDashboardData,
    updateContactInfo: exports.updateContactInfo,
    deleteRestaurant: exports.deleteRestaurant
};
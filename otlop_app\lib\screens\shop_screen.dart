import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/customWidgets.dart';
import 'package:otlop_app/ld_themes.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/providers/location_provider.dart';
import 'package:otlop_app/screens/restaurant_detail_screen.dart';
import 'package:otlop_app/models/location_model.dart';
import 'package:otlop_app/widgets/location_dialog.dart';

class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen> with WidgetsBindingObserver {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  OverlayEntry? _overlayEntry;
  final FocusNode _searchFocusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  bool _hasInitialized = false;

  // Static variable to track if location modal has been shown in this app session
  static bool _hasShownLocationModalThisSession = false;

  @override
  void initState() {
    super.initState();
    // Register as an observer to detect app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Add listener to search focus node
    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus) {
        _showSearchOverlay();
      } else {
        _removeSearchOverlay();
      }
    });

    // Initialize data loading
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (_hasInitialized) return;

    // Load user locations first
    await _loadUserLocations();

    // Check location status and show appropriate modal
    await _checkLocationStatus();

    // Only load restaurants if user has a valid location
    if (mounted) {
      final locationProvider =
          Provider.of<LocationProvider>(context, listen: false);
      if (locationProvider.selectedLocation != null) {
        await _loadRestaurants();
      }
    }

    _hasInitialized = true;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _hasInitialized) {
      // Refresh data when app resumes
      _loadRestaurants();
    }
  }

  @override
  void dispose() {
    // Unregister the observer
    WidgetsBinding.instance.removeObserver(this);

    _searchController.dispose();
    _searchFocusNode.dispose();
    _removeSearchOverlay();
    super.dispose();
  }

  Future<void> _loadRestaurants() async {
    if (!mounted) return;

    try {
      final restaurantProvider =
          Provider.of<RestaurantProvider>(context, listen: false);
      final locationProvider =
          Provider.of<LocationProvider>(context, listen: false);

      // Clear any existing error before fetching
      restaurantProvider.clearError();

      // Check if user has a valid location selected
      if (locationProvider.selectedLocation == null) {
        debugPrint('No location selected - not fetching restaurants');
        return;
      }

      // Get user location for distance and delivery fee calculations
      final userLat = locationProvider.selectedLocation!.latitude;
      final userLng = locationProvider.selectedLocation!.longitude;

      // Validate that coordinates are not null or zero
      if (userLat == 0.0 || userLng == 0.0) {
        debugPrint('Invalid location coordinates - not fetching restaurants');
        return;
      }

      debugPrint('Fetching restaurants with user location: $userLat, $userLng');

      // Fetch restaurants with user location for distance/delivery fee calculations
      await restaurantProvider.fetchRestaurants(
        userLat: userLat,
        userLng: userLng,
      );

      debugPrint(
          'Restaurants loaded successfully: ${restaurantProvider.restaurants.length}');
    } catch (e) {
      debugPrint('Error in _loadRestaurants: $e');
      // Error is handled in the provider, just log here
    }
  }

  // Load user saved locations
  Future<void> _loadUserLocations() async {
    if (!mounted) return;

    try {
      final locationProvider =
          Provider.of<LocationProvider>(context, listen: false);
      await locationProvider.loadLocations();
    } catch (e) {
      // Error handling is done in the provider
    }
  }

  // Check location status and show appropriate modal
  Future<void> _checkLocationStatus() async {
    if (!mounted) return;

    // Skip if modal has already been shown this session
    if (_hasShownLocationModalThisSession) return;

    final locationProvider =
        Provider.of<LocationProvider>(context, listen: false);

    // Wait a bit for the UI to settle
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    // Mark that we've shown the modal this session
    _hasShownLocationModalThisSession = true;

    if (locationProvider.selectedLocation == null) {
      // No location selected - show "please select location" message
      _showNoLocationMessage();
    } else {
      // Location exists - ask if user is still in this location
      _showLocationConfirmationDialog();
    }
  }

  // Show message when no location is selected
  void _showNoLocationMessage() {
    if (!mounted) return;

    showModalBottomSheet(
        context: context,
        isDismissible: false,
        enableDrag: false,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(28)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 24,
                    offset: const Offset(0, -8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Animated icon with gradient
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFFF9A9E), Color(0xFFFAD0C4)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFF9A9E).withOpacity(0.3),
                          blurRadius: 12,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.location_searching_rounded,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Title with better typography
                  const Text(
                    'حدد موقعك لاكتشاف المطاعم',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w800,
                      height: 1.4,
                      color: Color(0xFF2D3748),
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // Description with improved readability
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      'سنساعدك في العثور على أفضل المطاعم القريبة منك بمجرد تحديد موقعك',
                      style: TextStyle(
                        fontSize: 15,
                        height: 1.5,
                        color: Color(0xFF718096),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Modern button with splash effect
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _openLocationPicker();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF7C3AED),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                        animationDuration: const Duration(milliseconds: 200),
                      ),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: const Text(
                          'تحديد الموقع',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }

  // Show confirmation dialog for existing location
  void _showLocationConfirmationDialog() {
    if (!mounted) return;

    final locationProvider =
        Provider.of<LocationProvider>(context, listen: false);
    final locationName =
        locationProvider.selectedLocation?.name ?? 'الموقع المحفوظ';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تأكيد الموقع',
                textAlign: TextAlign.right,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w800,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'هل أنت لا تزال في $locationName؟',
                textAlign: TextAlign.right,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.6,
                  color: Colors.black54,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _openLocationPicker();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: const BorderSide(color: Colors.red),
                        ),
                        textStyle: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      child: const Text('لا'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _loadRestaurants();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6B46C1),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        textStyle: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      child: const Text('نعم'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Open location picker dialog
  void _openLocationPicker() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const LocationDialog(),
    );

    // After location dialog closes, check if location was selected and load restaurants
    if (mounted) {
      final locationProvider =
          Provider.of<LocationProvider>(context, listen: false);
      if (locationProvider.selectedLocation != null) {
        await _loadRestaurants();
      }
    }
  }

  // Build the app bar with search and location dropdown
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      title: Row(
        children: [
          // Location dropdown
          Consumer<LocationProvider>(
            builder: (context, locationProvider, child) {
              final selectedLocation = locationProvider.selectedLocation;

              return PopupMenuButton<UserLocation>(
                offset: const Offset(0, 40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical:
                          10), // Increased vertical padding for more height
                  decoration: BoxDecoration(
                    color: primaryPurple.withOpacity(0.06),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: primaryPurple.withOpacity(0.15)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: primaryPurple,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          selectedLocation?.name ?? 'اختر موقعك',
                          style: const TextStyle(
                            color: primaryPurple,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 2),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: primaryPurple,
                        size: 16,
                      ),
                    ],
                  ),
                ),
                onSelected: (UserLocation location) {
                  locationProvider.selectLocation(location);
                },
                itemBuilder: (BuildContext context) {
                  final locations = locationProvider.locations;

                  if (locations.isEmpty) {
                    return [
                      PopupMenuItem<UserLocation>(
                        enabled: false,
                        child: Text(
                          'لا توجد مواقع محفوظة',
                          style: TextStyle(
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      PopupMenuItem<UserLocation>(
                        child: const Row(
                          children: [
                            Icon(
                              Icons.add_location,
                              color: primaryPurple,
                            ),
                            SizedBox(width: 8),
                            Text('إضافة موقع جديد'),
                          ],
                        ),
                        onTap: () {
                          // Add a small delay to allow the popup to close
                          Future.delayed(const Duration(milliseconds: 100), () {
                            _openLocationPicker();
                          });
                        },
                      ),
                    ];
                  }

                  final items = locations.map((location) {
                    return PopupMenuItem<UserLocation>(
                      value: location,
                      child: Row(
                        children: [
                          Icon(
                            location.isDefault ? Icons.home : Icons.location_on,
                            color: location.id == selectedLocation?.id
                                ? primaryPurple
                                : Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              location.name,
                              style: TextStyle(
                                fontWeight: location.id == selectedLocation?.id
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: location.id == selectedLocation?.id
                                    ? primaryPurple
                                    : Colors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList();

                  // Add option to add new location
                  items.add(
                    PopupMenuItem<UserLocation>(
                      child: const Row(
                        children: [
                          Icon(
                            Icons.add_location,
                            color: primaryPurple,
                          ),
                          SizedBox(width: 8),
                          Text('إضافة موقع جديد'),
                        ],
                      ),
                      onTap: () {
                        // Add a small delay to allow the popup to close
                        Future.delayed(const Duration(milliseconds: 100), () {
                          _openLocationPicker();
                        });
                      },
                    ),
                  );

                  return items;
                },
              );
            },
          ),

          const SizedBox(width: 8),

          // Search field
          Expanded(
            child: CompositedTransformTarget(
              link: _layerLink,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  textAlignVertical: TextAlignVertical.center,
                  textInputAction: TextInputAction.search,
                  onSubmitted: _performSearch,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن مطعم...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: primaryPurple,
                      size: 20,
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, size: 16),
                            onPressed: () {
                              _searchController.clear();
                              _performSearch('');
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      actions: [
        // Refresh button
        IconButton(
          icon: const Icon(Icons.refresh),
          color: primaryPurple,
          onPressed: () {
            _loadRestaurants();
          },
          tooltip: 'Refresh restaurants',
        ),
      ],
    );
  }

  // Show search overlay
  void _showSearchOverlay() {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _removeSearchOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width - 100, // Adjust width as needed
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 40), // Adjust offset as needed
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              constraints: BoxConstraints(
                maxHeight: 200,
                minWidth: size.width - 100,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Text(
                      'اضغط Enter للبحث',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.history,
                          color: Colors.grey[600],
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'بحث سابق',
                          style: TextStyle(
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  // Remove search overlay
  void _removeSearchOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // Perform search
  void _performSearch(String query) {
    _removeSearchOverlay();

    if (query.isEmpty) {
      // If query is empty, reload all restaurants
      _loadRestaurants();
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Get restaurant provider
    final restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);

    // Execute search
    restaurantProvider.searchRestaurants(query).then((_) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    }).catchError((error) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching restaurants: $error')),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Don't automatically load restaurants on build
    // Only load when user explicitly clicks retry

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(context),
      body: Column(children: [
        // Pagination info at the top
        Consumer<RestaurantProvider>(
          builder: (context, restaurantProvider, child) {
            if (restaurantProvider.totalItems > 0) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'صفحة ${restaurantProvider.currentPage} من ${restaurantProvider.totalPages}',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                    Text(
                      '${restaurantProvider.totalItems} مطعم',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: restaurantProvider.currentPage > 1
                              ? () => restaurantProvider
                                  .loadPage(restaurantProvider.currentPage - 1)
                              : null,
                          icon: const Icon(Icons.chevron_left),
                          iconSize: 20,
                          color: Colors.black87,
                          visualDensity: VisualDensity.compact,
                          padding: EdgeInsets.zero,
                          constraints:
                              const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                        IconButton(
                          onPressed: restaurantProvider.currentPage <
                                  restaurantProvider.totalPages
                              ? () => restaurantProvider
                                  .loadPage(restaurantProvider.currentPage + 1)
                              : null,
                          icon: const Icon(Icons.chevron_right),
                          iconSize: 20,
                          color: Colors.black87,
                          visualDensity: VisualDensity.compact,
                          padding: EdgeInsets.zero,
                          constraints:
                              const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),

        // Main Content
        Expanded(
          child: Consumer<RestaurantProvider>(
            builder: (context, restaurantProvider, child) {
              // Check if user has selected a location first
              final locationProvider =
                  Provider.of<LocationProvider>(context, listen: false);
              if (locationProvider.selectedLocation == null) {
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Simple Modern Icon
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.deepPurple.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.location_on, // Your original location icon
                            size: 60,
                            color: Colors.deepPurple,
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Clean Typography
                        Column(
                          children: [
                            Text(
                              'ابدأ رحلة الطعم',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.w800,
                                color: Colors.grey[900],
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'حدد موقعك لاكتشاف أفضل المطاعم من حولك',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Modern Button with Subtle Animation
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _openLocationPicker,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.deepPurple,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 18),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 200),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.my_location, size: 20),
                                  SizedBox(width: 8),
                                  Text(
                                    'تحديد الموقع',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Handle loading state
              if (restaurantProvider.isLoading &&
                  restaurantProvider.restaurants.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل المطاعم...'),
                    ],
                  ),
                );
              }
              // Handle error state
              if (restaurantProvider.error != null) {
                // Check if it's an unauthorized error
                final errorMessage = restaurantProvider.error!;
                if (errorMessage.contains('Unauthorized')) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 60,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Session Expired',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Your session has expired. Please log in again.',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () async {
                            // Get the auth provider
                            final authProvider = Provider.of<AuthProvider>(
                                context,
                                listen: false);
                            // Store navigator before async operation
                            final navigator = Navigator.of(context);
                            // Logout
                            await authProvider.logout();
                            // Check if still mounted before navigating
                            if (mounted) {
                              navigator.pushReplacementNamed('/login');
                            }
                          },
                          icon: const Icon(Icons.login),
                          label: const Text('Log In'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                // General error

                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'خطأ في تحميل المطاعم',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          _loadRestaurants();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }

              final restaurants = restaurantProvider.restaurants;

              // Handle empty state - show load restaurants button if no data has been loaded yet
              if (restaurants.isEmpty) {
                // Check if this is the initial state (no error and no loading)
                final isInitialState = !restaurantProvider.isLoading &&
                    restaurantProvider.error == null;

                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        isInitialState
                            ? Icons.restaurant_menu
                            : Icons.restaurant,
                        color: Colors.grey,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isInitialState
                            ? 'مرحباً بك في توصيلة'
                            : 'لا توجد مطاعم',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isInitialState
                            ? 'اضغط على الزر أدناه لتحميل المطاعم المتاحة'
                            : 'جرب البحث عن مطعم مختلف أو تحقق مرة أخرى لاحقاً',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _loadRestaurants,
                        icon: Icon(
                            isInitialState ? Icons.restaurant : Icons.refresh),
                        label: Text(isInitialState
                            ? 'تحميل المطاعم'
                            : 'إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }

              // Show restaurant list with pull-to-refresh
              return RefreshIndicator(
                onRefresh: _loadRestaurants,
                color: Theme.of(context).colorScheme.primary,
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: [
                    // Restaurant List
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: restaurants.length,
                      itemBuilder: (context, index) {
                        final restaurant = restaurants[index];
                        return GestureDetector(
                          onTap: () async {
                            // Select the restaurant and navigate to its page
                            restaurantProvider.selectRestaurant(restaurant);
                            restaurantProvider
                                .fetchRestaurantMenu(restaurant.id.toString());

                            await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RestaurantDetailScreen(
                                  restaurantId: restaurant.id.toString(),
                                ),
                              ),
                            );

                            // Refresh restaurants when returning from detail screen
                            if (mounted) {
                              _loadRestaurants();
                            }
                          },
                          child: ShopCard(
                            shopName: restaurant.name,
                            openedStatus: restaurant.isOpen,
                            imageUrl: restaurant.image != null &&
                                    restaurant.image!.isNotEmpty
                                ? restaurant.image!
                                : 'assets/images/rest.jpg',
                            cuisineType:
                                restaurant.restaurantTypeName ?? 'Restaurant',
                            city: restaurant.city,
                            address: restaurant.address,
                            distance: restaurant.distance,
                            deliveryFee: restaurant.deliveryFee,
                            restaurantType: restaurant.restaurantTypeName,
                          ),
                        );
                      },
                    ),

                    // Loading indicator at the bottom when refreshing
                    if (restaurantProvider.isLoading && restaurants.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ]),
    );
  }
}

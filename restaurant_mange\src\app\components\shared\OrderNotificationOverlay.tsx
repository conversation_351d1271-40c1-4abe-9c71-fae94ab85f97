'use client';

import React, { useState } from 'react';
import { useOrderNotification } from '@/app/context/OrderNotificationContext';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert } from 'flowbite-react';
import { HiExclamationCircle, HiCheckCircle, HiXCircle, HiRefresh } from 'react-icons/hi';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { HiExclamationTriangle } from 'react-icons/hi2';

interface OrderNotificationOverlayProps {
  restaurantId: string;
}

const OrderNotificationOverlay: React.FC<OrderNotificationOverlayProps> = ({ restaurantId }) => {
  const {
    pendingOrders,
    isConnected,
    isLoading,
    error,
    acceptOrder,
    rejectOrder,
    refreshOrders
  } = useOrderNotification();

  const [showModal, setShowModal] = useState(false);
  const [showCurrentOrdersModal, setShowCurrentOrdersModal] = useState(false);
  const [showRejectConfirmModal, setShowRejectConfirmModal] = useState(false);
  const [orderToReject, setOrderToReject] = useState<number | null>(null);
  const [processingOrderId, setProcessingOrderId] = useState<number | null>(null);
  const [currentOrders, setCurrentOrders] = useState<any[]>([]);
  const [preparingOrders, setPreparingOrders] = useState<any[]>([]);

  // Show modal when there are pending orders
  React.useEffect(() => {
    console.log(`OrderOverlay: Pending orders count changed to ${pendingOrders.length}`);
    if (pendingOrders.length > 0) {
      console.log('OrderOverlay: Showing modal for pending orders');
      setShowModal(true);
    } else {
      console.log('OrderOverlay: Hiding modal - no pending orders');
      setShowModal(false);
    }
  }, [pendingOrders.length]);

  // Fetch preparing orders (status = 1) - same pattern as pending orders
  const fetchPreparingOrders = React.useCallback(async () => {
    if (!restaurantId) {
      console.log('OrderOverlay: No restaurant ID provided for preparing orders');
      return;
    }

    try {
      console.log(`OrderOverlay: Fetching preparing orders for restaurant ${restaurantId}`);

      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        throw new Error('No access token found');
      }

      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL}/orders/restaurant/${restaurantId}?page=1&size=50`;
      console.log(`OrderOverlay: Making request to ${apiUrl}`);

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`OrderOverlay: Response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log(`OrderOverlay: Received response for preparing orders:`, responseData);

      // Handle paginated response format - same as pending orders
      const orders: any[] = responseData.items || responseData.data || [];
      console.log(`OrderOverlay: Received ${orders.length} total orders`);

      // Filter only preparing orders (Status = 1)
      const preparing = orders.filter(order => order.Status === 1);
      console.log(`OrderOverlay: Found ${preparing.length} preparing orders`);

      setPreparingOrders(preparing);

      // Log preparing orders for debugging
      if (preparing.length > 0) {
        console.log('OrderOverlay: Preparing orders:', preparing.map(o => ({
          id: o.OrderID,
          status: o.Status,
          total: o.CartTotalPrice
        })));
      }
    } catch (err) {
      console.error('OrderOverlay: Error fetching preparing orders:', err);
    }
  }, [restaurantId]);

  // Fetch preparing orders periodically - same pattern as pending orders
  React.useEffect(() => {
    fetchPreparingOrders(); // Initial fetch
    const interval = setInterval(fetchPreparingOrders, 15000); // Update every 15 seconds

    return () => clearInterval(interval);
  }, [fetchPreparingOrders]);

  const handleAcceptOrder = async (orderId: number) => {
    try {
      setProcessingOrderId(orderId);
      await acceptOrder(orderId);
    } catch (err) {
      console.error('Failed to accept order:', err);
    } finally {
      setProcessingOrderId(null);
    }
  };

  const handleRejectOrder = async (orderId: number) => {
    setOrderToReject(orderId);
    setShowRejectConfirmModal(true);
  };

  const confirmRejectOrder = async () => {
    if (!orderToReject) return;

    try {
      setProcessingOrderId(orderToReject);
      await rejectOrder(orderToReject);
      setShowRejectConfirmModal(false);
      setOrderToReject(null);
    } catch (err) {
      console.error('Failed to reject order:', err);
    } finally {
      setProcessingOrderId(null);
    }
  };

  const fetchCurrentOrders = async () => {
    setShowCurrentOrdersModal(true);
    // Use the already fetched preparing orders
    setCurrentOrders(preparingOrders);
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} د.ل`;
  };

  const getImageUrl = (imageUrl?: string) => {
    if (!imageUrl) return '/images/placeholder-food.jpg';
    if (imageUrl.startsWith('http')) return imageUrl;
    return `${process.env.NEXT_PUBLIC_API_URL}/${imageUrl.replace(/^\/+/, '')}`;
  };

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="fixed top-4 left-4 z-50">
      {!isConnected && (
        <Alert color="failure" icon={HiExclamationTriangle} className="mb-4">
          <div className="flex items-center justify-between">
            <span>فقدان الاتصال بالخادم</span>
            <Button
              size="xs"
              color="failure"
              onClick={refreshOrders}
              disabled={isLoading}
            >
              {isLoading ? <Spinner size="sm" /> : <HiRefresh className="w-4 h-4" />}
            </Button>
          </div>
        </Alert>
      )}
    </div>
  );

  // Order notification badge - Mind-blowing design
  const NotificationBadge = () => {
    if (pendingOrders.length === 0) return null;

    return (
      <div className="fixed top-6 right-6 z-50">
        <div
          className="relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 hover:from-orange-600 hover:via-red-600 hover:to-pink-700 text-white px-8 py-6 rounded-3xl shadow-2xl cursor-pointer transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm"
          onClick={() => setShowModal(true)}
        >
          {/* Animated background glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-400 via-red-400 to-pink-500 rounded-3xl blur-xl opacity-75 animate-pulse"></div>

          {/* Content */}
          <div className="relative flex items-center gap-4">
            {/* Icon with animated ring */}
            <div className="relative">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30">
                <HiExclamationTriangle className="w-7 h-7 text-white animate-bounce" />
              </div>
              {/* Animated ring */}
              <div className="absolute inset-0 w-12 h-12 border-2 border-white/50 rounded-full animate-ping"></div>
            </div>

            {/* Text content */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-black text-xl tracking-wide">
                  طلبات جديدة عاجلة
                </span>
                <div className="w-2 h-2 bg-yellow-300 rounded-full animate-pulse shadow-lg"></div>
              </div>
              <span className="text-white/90 text-sm font-semibold">
                {pendingOrders.length} طلب يحتاج موافقة فورية
              </span>
            </div>

            {/* Count badge */}
            <div className="relative">
              <div className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40">
                <span className="text-2xl font-black text-white">
                  {pendingOrders.length}
                </span>
              </div>
              {/* Pulsing effect */}
              <div className="absolute inset-0 w-14 h-14 bg-white/10 rounded-full animate-ping"></div>
            </div>
          </div>

          {/* Floating particles effect */}
          <div className="absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60"></div>
          <div className="absolute bottom-3 left-3 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-80"></div>
          <div className="absolute top-1/2 left-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70"></div>
        </div>
      </div>
    );
  };

  // Current Orders Button - Mind-blowing design
  const CurrentOrdersButton = () => {
    if (preparingOrders.length === 0) return null;

    return (
      <div className="fixed top-6 left-6 z-50">
        <button
          onClick={fetchCurrentOrders}
          className="relative bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-700 text-white px-8 py-6 rounded-3xl shadow-2xl transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm group"
        >
          {/* Animated background glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-500 rounded-3xl blur-xl opacity-75 group-hover:opacity-90 transition-opacity duration-300"></div>

          {/* Content */}
          <div className="relative flex items-center gap-4">
            {/* Icon with animated rotation */}
            <div className="relative">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 group-hover:rotate-12 transition-transform duration-300">
                <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                </svg>
              </div>
              {/* Rotating ring */}
              <div className="absolute inset-0 w-12 h-12 border-2 border-white/30 rounded-full animate-spin opacity-50"></div>
            </div>

            {/* Text content */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-black text-xl tracking-wide">
                  الطلبات الحالية
                </span>
                <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse shadow-lg"></div>
              </div>
              <span className="text-white/90 text-sm font-semibold">
                {preparingOrders.length} طلب قيد التحضير
              </span>
            </div>

            {/* Count badge with cooking animation */}
            <div className="relative">
              <div className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40 group-hover:bg-white/30 transition-colors duration-300">
                <span className="text-2xl font-black text-white">
                  {preparingOrders.length}
                </span>
              </div>
              {/* Steam effect */}
              <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                <div className="w-1 h-3 bg-white/40 rounded-full animate-bounce opacity-60"></div>
              </div>
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 translate-x-1">
                <div className="w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40" style={{animationDelay: '0.2s'}}></div>
              </div>
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 -translate-x-1">
                <div className="w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40" style={{animationDelay: '0.4s'}}></div>
              </div>
            </div>
          </div>

          {/* Floating sparkles */}
          <div className="absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60"></div>
          <div className="absolute bottom-3 left-3 w-1 h-1 bg-green-300 rounded-full animate-pulse opacity-80"></div>
          <div className="absolute top-1/2 right-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70"></div>
        </button>
      </div>
    );
  };

  return (
    <>
      <ConnectionStatus />
      <NotificationBadge />
      <CurrentOrdersButton />

      {/* New Orders Modal - Apple-style Clean Design */}
      <Modal
        show={showModal}
        onClose={() => setShowModal(false)}
        size="4xl"
        position="center"
        dismissible={false}
        className="backdrop-blur-sm"
      >
        <div className="bg-white border-0 rounded-2xl shadow-2xl overflow-hidden">
          {/* Clean Header */}
          <Modal.Header className="border-b border-gray-100 bg-white rounded-t-2xl px-8 py-6">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-4">
                {/* Simple, clean icon */}
                <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center shadow-sm">
                  <HiExclamationTriangle className="w-6 h-6 text-white" />
                </div>

                {/* Clean typography */}
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 mb-1">
                    طلبات جديدة
                  </h2>
                  <p className="text-sm text-gray-600">
                    {pendingOrders.length} طلب يحتاج إلى موافقة
                  </p>
                </div>
              </div>

              {/* Simple count badge */}
              <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center shadow-sm">
                <span className="text-lg font-semibold text-white">
                  {pendingOrders.length}
                </span>
              </div>
            </div>
          </Modal.Header>

          {/* Clean Modal Body */}
          <Modal.Body className="max-h-[75vh] overflow-y-auto bg-gray-50 p-6">
          {pendingOrders.length === 0 ? (
            <div className="text-center py-12">
              <HiCheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <p className="text-lg text-gray-600">لا توجد طلبات في الانتظار</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingOrders.map((order) => (
                <div
                  key={order.OrderID}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
                >
                  {/* Clean Order Header */}
                  <div className="flex justify-between items-start mb-6">
                    <div className="flex items-start gap-4">
                      {/* Simple icon */}
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                          <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                        </svg>
                      </div>

                      {/* Clean text content */}
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          طلب رقم #{order.OrderID}
                        </h3>

                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-2 text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <span>
                              {formatDistanceToNow(new Date(order.OrderDate), {
                                addSuffix: true,
                                locale: ar
                              })}
                            </span>
                          </div>

                          {order.CustomerPhoneNum && (
                            <div className="flex items-center gap-2 text-gray-600">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                              </svg>
                              <span>{order.CustomerPhoneNum}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Clean status and price section */}
                    <div className="flex flex-col items-end gap-3">
                      <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-lg text-sm font-medium">
                        في الانتظار
                      </div>

                      <div className="text-right">
                        <p className="text-2xl font-semibold text-gray-900">
                          {formatPrice(order.CartTotalPrice)}
                        </p>
                        <p className="text-sm text-gray-600">المجموع الكلي</p>
                      </div>
                    </div>
                  </div>



                  {order.Note && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700">ملاحظات:</p>
                      <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        {order.Note}
                      </p>
                    </div>
                  )}

                  {/* Order Items */}
                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                        </svg>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">المنتجات المطلوبة</h4>
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {order.Products?.length || 0} منتج
                      </span>
                    </div>

                    <div className="space-y-4">
                      {order.Products && order.Products.length > 0 ? (
                        order.Products.map((item, index) => (
                          <div
                            key={index}
                            className="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow"
                          >
                            <div className="flex gap-4">
                              {/* Product Image */}
                              <div className="relative">
                                <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-100">
                                  {item.Product.Image ? (
                                    <img
                                      src={getImageUrl(item.Product.Image)}
                                      alt={item.Product.ProductName}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = '/images/placeholder-food.jpg';
                                      }}
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                                {/* Quantity Badge */}
                                <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                  {item.Quantity}
                                </div>
                              </div>

                              {/* Product Details */}
                              <div className="flex-1 min-w-0">
                                <div className="flex justify-between items-start mb-2">
                                  <h5 className="font-semibold text-gray-900 truncate">
                                    {item.Product.ProductName}
                                  </h5>
                                  <div className="text-right ml-2">
                                    <p className="text-lg font-bold text-green-600">
                                      {formatPrice(item.Product.Price * item.Quantity)}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {formatPrice(item.Product.Price)} × {item.Quantity}
                                    </p>
                                  </div>
                                </div>

                                {/* Ingredients */}
                                {item.IngredientUsages && item.IngredientUsages.length > 0 && (
                                  <div className="mt-3">
                                    <p className="text-xs font-medium text-gray-700 mb-2">المكونات المختارة:</p>
                                    <div className="flex flex-wrap gap-1">
                                      {item.IngredientUsages.map((usage, idx) => (
                                        <span
                                          key={idx}
                                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                            usage.IsNeeded
                                              ? 'bg-green-100 text-green-800 border border-green-200'
                                              : 'bg-red-100 text-red-800 border border-red-200'
                                          }`}
                                        >
                                          <span className={`w-1.5 h-1.5 rounded-full mr-1 ${
                                            usage.IsNeeded ? 'bg-green-500' : 'bg-red-500'
                                          }`}></span>
                                          {usage.Ingredient.IngredientName}
                                          {usage.IsNeeded ? ' ✓' : ' ✗'}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 bg-gray-50 rounded-xl">
                          <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
                          </svg>
                          <p className="text-gray-500 font-medium">لا توجد منتجات في هذا الطلب</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional Order Info */}
                  {(order.RestaurantName || order.DriverFirstName || order.DriverLastName || order.DistanceInMeters) && (
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-900">معلومات إضافية</h4>
                      </div>
                      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 space-y-3">
                        {order.RestaurantName && (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                              </svg>
                              <span className="text-sm text-gray-600">المطعم:</span>
                            </div>
                            <span className="text-sm font-semibold text-gray-900">
                              {order.RestaurantName}
                            </span>
                          </div>
                        )}
                        {(order.DriverFirstName || order.DriverLastName) && (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                              </svg>
                              <span className="text-sm text-gray-600">السائق:</span>
                            </div>
                            <span className="text-sm font-semibold text-gray-900">
                              {`${order.DriverFirstName || ''} ${order.DriverLastName || ''}`.trim()}
                            </span>
                          </div>
                        )}
                        {order.DistanceInMeters && (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                              </svg>
                              <span className="text-sm text-gray-600">المسافة:</span>
                            </div>
                            <span className="text-sm font-semibold text-gray-900">
                              {(order.DistanceInMeters / 1000).toFixed(2)} كم
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Clean Action Buttons */}
                  <div className="flex gap-3 pt-6 border-t border-gray-200">
                    {/* Accept Button */}
                    <button
                      onClick={() => handleAcceptOrder(order.OrderID)}
                      disabled={processingOrderId === order.OrderID}
                      className="flex-1 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {processingOrderId === order.OrderID ? (
                        <div className="flex items-center justify-center gap-2">
                          <Spinner size="sm" />
                          <span>جاري المعالجة...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <HiCheckCircle className="w-5 h-5" />
                          <span>قبول وبدء التحضير</span>
                        </div>
                      )}
                    </button>

                    {/* Reject Button */}
                    <button
                      onClick={() => handleRejectOrder(order.OrderID)}
                      disabled={processingOrderId === order.OrderID}
                      className="flex-1 bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {processingOrderId === order.OrderID ? (
                        <div className="flex items-center justify-center gap-2">
                          <Spinner size="sm" />
                          <span>جاري المعالجة...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <HiXCircle className="w-5 h-5" />
                          <span>رفض الطلبية</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Modal.Body>
        </div>

        {/* Clean Modal Footer */}
        <Modal.Footer className="bg-white border-t border-gray-200 rounded-b-2xl px-6 py-4">
          <div className="flex justify-between items-center w-full">
            <button
              onClick={refreshOrders}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isLoading ? <Spinner size="sm" /> : <HiRefresh className="w-4 h-4" />}
              تحديث
            </button>
            <button
              onClick={() => setShowModal(false)}
              className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200"
            >
              إغلاق
            </button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Rejection Confirmation Modal */}
      <Modal
        show={showRejectConfirmModal}
        onClose={() => setShowRejectConfirmModal(false)}
        size="md"
        position="center"
        className="backdrop-blur-sm"
      >
        <div className="bg-white rounded-2xl shadow-2xl">
          <Modal.Header className="border-b border-gray-100 bg-gradient-to-r from-red-50 to-red-100 rounded-t-2xl">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center">
                <HiXCircle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-red-900">تأكيد رفض الطلبية</h3>
                <p className="text-sm text-red-700">هذا الإجراء لا يمكن التراجع عنه</p>
              </div>
            </div>
          </Modal.Header>

          <Modal.Body className="p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <HiExclamationTriangle className="w-8 h-8 text-red-500" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                هل أنت متأكد من رفض هذه الطلبية؟
              </h4>
              <p className="text-gray-600 mb-6">
                سيتم إشعار العميل برفض الطلبية ولن تتمكن من قبولها مرة أخرى
              </p>

              <div className="flex gap-3 justify-center">
                <button
                  onClick={confirmRejectOrder}
                  disabled={processingOrderId !== null}
                  className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {processingOrderId ? (
                    <div className="flex items-center gap-2">
                      <Spinner size="sm" />
                      <span>جاري الرفض...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <HiXCircle className="w-5 h-5" />
                      <span>نعم، رفض الطلبية</span>
                    </div>
                  )}
                </button>

                <button
                  onClick={() => setShowRejectConfirmModal(false)}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>

      {/* Current Orders Modal */}
      <Modal
        show={showCurrentOrdersModal}
        onClose={() => setShowCurrentOrdersModal(false)}
        size="5xl"
        position="center"
        className="backdrop-blur-sm"
      >
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border-0 rounded-2xl shadow-2xl">
          <Modal.Header className="border-b border-gray-100 bg-white rounded-t-2xl">
            <div className="flex items-center gap-4 w-full">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                    <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-1">
                    الطلبات الحالية
                  </h2>
                  <p className="text-lg text-gray-600">
                    الطلبات قيد التحضير ({currentOrders.length} طلب)
                  </p>
                </div>
              </div>
              <div className="flex-1"></div>
              <div className="flex items-center gap-3">
                <span className="bg-gradient-to-r from-blue-400 to-indigo-500 text-white text-xl font-bold px-5 py-3 rounded-full shadow-lg">
                  {currentOrders.length}
                </span>
                <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse shadow-lg"></div>
              </div>
            </div>
          </Modal.Header>

          <Modal.Body className="max-h-[70vh] overflow-y-auto bg-gradient-to-br from-gray-50 to-white p-6">
            {currentOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد طلبات قيد التحضير</h3>
                <p className="text-gray-600">جميع الطلبات تم تحضيرها أو لا توجد طلبات مقبولة حالياً</p>
              </div>
            ) : (
              <div className="space-y-6">
                {currentOrders.map((order) => (
                  <div
                    key={order.OrderID}
                    className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300"
                  >
                    {/* Order Header */}
                    <div className="flex justify-between items-start mb-6">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                          <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                            <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 mb-1">
                            طلب رقم #{order.OrderID}
                          </h3>
                          <div className="flex items-center gap-3 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                              </svg>
                              <span>
                                {formatDistanceToNow(new Date(order.OrderDate), {
                                  addSuffix: true,
                                  locale: ar
                                })}
                              </span>
                            </div>
                            {order.CustomerPhoneNum && (
                              <div className="flex items-center gap-1">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                </svg>
                                <span>{order.CustomerPhoneNum}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="bg-gradient-to-r from-green-400 to-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                            قيد التحضير
                          </div>
                        </div>
                        <p className="text-2xl font-bold text-green-600">
                          {formatPrice(order.CartTotalPrice)}
                        </p>
                        <p className="text-xs text-gray-500">المجموع الكلي</p>
                      </div>
                    </div>

                    {/* Products */}
                    <div className="mb-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                          </svg>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">المنتجات المطلوبة</h4>
                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                          {order.Products?.length || 0} منتج
                        </span>
                      </div>

                      <div className="space-y-3">
                        {order.Products && order.Products.length > 0 ? (
                          order.Products.map((item: any, index: number) => (
                            <div
                              key={index}
                              className="bg-gray-50 border border-gray-100 rounded-xl p-4"
                            >
                              <div className="flex gap-4">
                                {/* Product Image */}
                                <div className="relative">
                                  <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-100">
                                    {item.Product.Image ? (
                                      <img
                                        src={getImageUrl(item.Product.Image)}
                                        alt={item.Product.ProductName}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          const target = e.target as HTMLImageElement;
                                          target.src = '/images/placeholder-food.jpg';
                                        }}
                                      />
                                    ) : (
                                      <div className="w-full h-full flex items-center justify-center">
                                        <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                    )}
                                  </div>
                                  {/* Quantity Badge */}
                                  <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                    {item.Quantity}
                                  </div>
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-start mb-2">
                                    <h5 className="font-semibold text-gray-900 truncate">
                                      {item.Product.ProductName}
                                    </h5>
                                    <div className="text-right ml-2">
                                      <p className="text-lg font-bold text-green-600">
                                        {formatPrice(item.Product.Price * item.Quantity)}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {formatPrice(item.Product.Price)} × {item.Quantity}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Ingredients */}
                                  {item.IngredientUsages && item.IngredientUsages.length > 0 && (
                                    <div className="mt-3">
                                      <p className="text-xs font-medium text-gray-700 mb-2">المكونات المختارة:</p>
                                      <div className="flex flex-wrap gap-1">
                                        {item.IngredientUsages.map((usage: any, idx: number) => (
                                          <span
                                            key={idx}
                                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                              usage.IsNeeded
                                                ? 'bg-green-100 text-green-800 border border-green-200'
                                                : 'bg-red-100 text-red-800 border border-red-200'
                                            }`}
                                          >
                                            <span className={`w-1.5 h-1.5 rounded-full mr-1 ${
                                              usage.IsNeeded ? 'bg-green-500' : 'bg-red-500'
                                            }`}></span>
                                            {usage.Ingredient.IngredientName}
                                            {usage.IsNeeded ? ' ✓' : ' ✗'}
                                          </span>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 bg-gray-50 rounded-xl">
                            <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
                            </svg>
                            <p className="text-gray-500 font-medium">لا توجد منتجات في هذا الطلب</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Modal.Body>

          <Modal.Footer className="bg-white border-t border-gray-100 rounded-b-2xl">
            <div className="flex justify-between items-center w-full">
              <Button
                color="gray"
                onClick={fetchCurrentOrders}
                disabled={isLoading}
              >
                {isLoading ? <Spinner size="sm" /> : <HiRefresh className="w-4 h-4 mr-2" />}
                تحديث
              </Button>
              <Button
                color="light"
                onClick={() => setShowCurrentOrdersModal(false)}
              >
                إغلاق
              </Button>
            </div>
          </Modal.Footer>
        </div>
      </Modal>
    </>
  );
};

export default OrderNotificationOverlay;

import 'package:flutter/material.dart';
import 'package:otlop_app/models/restaurant_model.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/providers/restaurant_cart_provider.dart';
import 'package:otlop_app/screens/product/product_detail_screen.dart';
import 'package:otlop_app/screens/cart/cart_screen.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class RestaurantDetailScreen extends StatefulWidget {
  final int restaurantId;

  const RestaurantDetailScreen({
    Key? key,
    required this.restaurantId,
  }) : super(key: key);

  @override
  State<RestaurantDetailScreen> createState() => _RestaurantDetailScreenState();
}

class _RestaurantDetailScreenState extends State<RestaurantDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRestaurantData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRestaurantData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);
      await restaurantProvider.fetchRestaurantById(widget.restaurantId.toString());
      await restaurantProvider.fetchRestaurantMenu(widget.restaurantId.toString());
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer2<RestaurantProvider, RestaurantCartProvider>(
        builder: (context, restaurantProvider, cartProvider, child) {
          if (_isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (_error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error loading restaurant: $_error',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _loadRestaurantData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final restaurant = restaurantProvider.selectedRestaurant;
          if (restaurant == null) {
            return const Center(
              child: Text('Restaurant not found'),
            );
          }

          return CustomScrollView(
            slivers: [
              // App bar with restaurant image
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Pop back to previous screen
                    Navigator.of(context).pop();

                    // The ShopScreen will automatically refresh due to our implementation
                  },
                ),
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Restaurant cover image
                      restaurant.image != null && restaurant.image!.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: restaurant.image!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Image.asset(
                                'assets/images/rest.jpg',
                                fit: BoxFit.cover,
                              ),
                              errorWidget: (context, url, error) => Image.asset(
                                'assets/images/rest.jpg',
                                fit: BoxFit.cover,
                              ),
                            )
                          : Image.asset(
                              'assets/images/rest.jpg',
                              fit: BoxFit.cover,
                            ),

                      // Gradient overlay for better text visibility
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withAlpha(179),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  title: Text(
                    restaurant.name,
                    style: const TextStyle(
                      fontFamily: 'Alx',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      // Share restaurant (to be implemented)
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.favorite_border),
                    onPressed: () {
                      // Add to favorites (to be implemented)
                    },
                  ),
                ],
              ),

              // Restaurant info
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Restaurant name and status
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              restaurant.name,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Alx',
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: restaurant.isOpen ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              restaurant.isOpen ? 'Open' : 'Closed',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Rating and distance
                      Row(
                        children: [
                          if (restaurant.rating != null) ...[
                            RatingBar.builder(
                              initialRating: restaurant.rating!,
                              minRating: 1,
                              direction: Axis.horizontal,
                              allowHalfRating: true,
                              itemCount: 5,
                              itemSize: 20,
                              ignoreGestures: true,
                              itemBuilder: (context, _) => const Icon(
                                Icons.star,
                                color: Colors.amber,
                              ),
                              onRatingUpdate: (rating) {},
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${restaurant.rating!.toStringAsFixed(1)} (${restaurant.reviewCount ?? 0} reviews)',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                          const Spacer(),
                          Icon(
                            Icons.location_on,
                            color: Theme.of(context).colorScheme.primary,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${restaurant.distance != null ? restaurant.distance!.toStringAsFixed(1) : "N/A"} km',
                            style: TextStyle(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Restaurant description
                      if (restaurant.description != null && restaurant.description!.isNotEmpty)
                        Text(
                          restaurant.description!,
                          style: TextStyle(
                            color: Colors.grey[700],
                          ),
                        ),

                      // Restaurant address and contact info
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(Icons.location_city, size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${restaurant.address}, ${restaurant.city}',
                              style: TextStyle(color: Colors.grey[700]),
                            ),
                          ),
                        ],
                      ),

                      if (restaurant.firstNumber != null) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                            const SizedBox(width: 8),
                            Text(
                              restaurant.firstNumber!,
                              style: TextStyle(color: Colors.grey[700]),
                            ),
                          ],
                        ),
                      ],

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),

              // Tab Bar
              SliverPersistentHeader(
                delegate: _SliverAppBarDelegate(
                  TabBar(
                    controller: _tabController,
                    labelColor: Theme.of(context).colorScheme.primary,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Theme.of(context).colorScheme.primary,
                    tabs: const [
                      Tab(text: 'Menu'),
                      Tab(text: 'Reviews'),
                      Tab(text: 'Info'),
                    ],
                  ),
                ),
                pinned: true,
              ),

              // Tab Bar View
              SliverFillRemaining(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Menu Tab
                    _buildMenuTab(restaurantProvider.restaurantMenu),

                    // Reviews Tab
                    _buildReviewsTab(),

                    // Info Tab
                    _buildInfoTab(restaurant),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: Consumer<RestaurantCartProvider>(
        builder: (context, cartProvider, child) {
          final cartItemCount = cartProvider.getCartItemCountForRestaurant(widget.restaurantId);
          final cartTotal = cartProvider.getCartTotalForRestaurant(widget.restaurantId);
          final hasItems = cartProvider.hasItemsForRestaurant(widget.restaurantId);

          if (!hasItems) {
            return const SizedBox.shrink();
          }

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: SafeArea(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CartScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6B46C1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$cartItemCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'View Cart',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '\$${cartTotal.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMenuTab(List<Product> menu) {
    if (menu.isEmpty) {
      return const Center(
        child: Text('No menu items available'),
      );
    }

    // Group menu items by category
    final Map<String, List<Product>> categorizedMenu = {};
    for (final product in menu) {
      final category = product.categoryName ?? 'Uncategorized';
      categorizedMenu.putIfAbsent(category, () => []);
      categorizedMenu[category]!.add(product);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categorizedMenu.length,
      itemBuilder: (context, index) {
        final category = categorizedMenu.keys.elementAt(index);
        final categoryItems = categorizedMenu[category]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                category,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Alx',
                ),
              ),
            ),

            // Category items
            ...categoryItems.map((product) => _buildMenuItem(product)),

            const Divider(height: 32),
          ],
        );
      },
    );
  }

  Widget _buildMenuItem(Product product) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: () {
          // Get the restaurant ID from the provider
          final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);
          final restaurant = restaurantProvider.selectedRestaurant;

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailScreen(
                product: product,
                restaurantId: restaurant?.id.toString(),
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              if (product.image != null && product.image!.isNotEmpty)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CachedNetworkImage(
                    imageUrl: product.image!,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey[300],
                      child: const Icon(Icons.fastfood),
                    ),
                  ),
                )
              else
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.fastfood),
                ),

              const SizedBox(width: 16),

              // Product info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (product.description != null && product.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        product.description!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (product.discountPrice != null) ...[
                          // Discounted price
                          Text(
                            '\$${product.discountPrice!.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          // Original price (strikethrough)
                          Text(
                            '\$${product.price.toStringAsFixed(2)}',
                            style: const TextStyle(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey,
                            ),
                          ),
                        ] else ...[
                          // Regular price
                          Text(
                            '\$${product.price.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],

                        // Add to cart button
                        Consumer<RestaurantCartProvider>(
                          builder: (context, cartProvider, child) {
                            return IconButton(
                              icon: const Icon(Icons.add_shopping_cart),
                              color: Theme.of(context).colorScheme.primary,
                              onPressed: () {
                                // Show add to cart bottom sheet (quick add)
                                _showAddToCartSheet(product);
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReviewsTab() {
    // TODO: Implement reviews tab
    return const Center(
      child: Text('Reviews coming soon'),
    );
  }

  Widget _buildInfoTab(Restaurant restaurant) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Address
        if (restaurant.address.isNotEmpty) ...[
          const ListTile(
            leading: Icon(Icons.location_on),
            title: Text('Address'),
            contentPadding: EdgeInsets.zero,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 40, bottom: 16),
            child: Text('${restaurant.address}, ${restaurant.city}'),
          ),
        ],

        // Phone number
        if (restaurant.firstNumber != null && restaurant.firstNumber!.isNotEmpty) ...[
          ListTile(
            leading: const Icon(Icons.phone),
            title: const Text('Phone'),
            subtitle: Text(restaurant.firstNumber!),
            contentPadding: EdgeInsets.zero,
            trailing: IconButton(
              icon: const Icon(Icons.call),
              onPressed: () {
                // Make a call (to be implemented)
              },
            ),
          ),
          const Divider(),
        ],

        // Second phone number if available
        if (restaurant.secondNumber != null && restaurant.secondNumber!.isNotEmpty) ...[
          ListTile(
            leading: const Icon(Icons.phone_android),
            title: const Text('Alternative Phone'),
            subtitle: Text(restaurant.secondNumber!),
            contentPadding: EdgeInsets.zero,
            trailing: IconButton(
              icon: const Icon(Icons.call),
              onPressed: () {
                // Make a call (to be implemented)
              },
            ),
          ),
          const Divider(),
        ],

        // Restaurant type
        if (restaurant.restaurantTypeName != null && restaurant.restaurantTypeName!.isNotEmpty) ...[
          ListTile(
            leading: const Icon(Icons.category),
            title: const Text('Category'),
            subtitle: Text(restaurant.restaurantTypeName!),
            contentPadding: EdgeInsets.zero,
          ),
          const Divider(),
        ],

        // Delivery info
        if (restaurant.deliveryTime != null || restaurant.deliveryFee != null || restaurant.minOrderAmount != null) ...[
          const ListTile(
            leading: Icon(Icons.delivery_dining),
            title: Text('Delivery Information'),
            contentPadding: EdgeInsets.zero,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 40, bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (restaurant.deliveryTime != null)
                  Text('Delivery Time: ${restaurant.deliveryTime}'),
                if (restaurant.deliveryFee != null) ...[
                  const SizedBox(height: 4),
                  Text('Delivery Fee: \$${restaurant.deliveryFee!.toStringAsFixed(2)}'),
                ],
                if (restaurant.minOrderAmount != null) ...[
                  const SizedBox(height: 4),
                  Text('Minimum Order: \$${restaurant.minOrderAmount!.toStringAsFixed(2)}'),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }



  void _showAddToCartSheet(Product product) {
    int quantity = 1;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Product info
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product image
                      if (product.image != null && product.image!.isNotEmpty)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: CachedNetworkImage(
                            imageUrl: product.image!,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        )
                      else
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.fastfood),
                        ),

                      const SizedBox(width: 16),

                      // Product details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              product.discountPrice != null
                                  ? '\$${product.discountPrice!.toStringAsFixed(2)}'
                                  : '\$${product.price.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Quantity selector
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle_outline),
                        onPressed: quantity > 1
                            ? () => setState(() => quantity--)
                            : null,
                      ),
                      const SizedBox(width: 16),
                      Text(
                        quantity.toString(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 16),
                      IconButton(
                        icon: const Icon(Icons.add_circle_outline),
                        onPressed: () => setState(() => quantity++),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Add to cart button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () async {
                        final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);
                        final restaurant = restaurantProvider.selectedRestaurant;

                        if (restaurant == null) {
                          if (mounted) Navigator.pop(context);
                          return;
                        }

                        try {
                          // Add to cart using RestaurantCartProvider
                          final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
                          await cartProvider.addItemToRestaurantCart(product, quantity, []);

                          if (mounted) {
                            Navigator.pop(context);

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('${product.name} added to cart'),
                                backgroundColor: Colors.green,
                                action: SnackBarAction(
                                  label: 'VIEW CART',
                                  textColor: Colors.white,
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const CartScreen(),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            Navigator.pop(context);

                            // Show error message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(e.toString()),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      child: Text(
                        'Add to Cart - \$${(product.discountPrice ?? product.price * quantity).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
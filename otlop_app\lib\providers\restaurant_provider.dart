import 'package:flutter/material.dart';
import 'package:otlop_app/models/restaurant_model.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/services/restaurant_service.dart';
import 'package:otlop_app/models/working_time_model.dart' as working_time;

class RestaurantProvider extends ChangeNotifier {
  final RestaurantService _restaurantService;

  List<Restaurant> _restaurants = [];
  Restaurant? _selectedRestaurant;
  List<Product> _restaurantMenu = [];
  List<working_time.WorkingTime> _workingHours = [];
  bool _isLoading = false;
  bool _isLoadingWorkingHours = false;
  String? _error;

  // Pagination variables
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalItems = 0;
  int _itemsPerPage = 10;
  double? _userLat;
  double? _userLng;

  RestaurantProvider({RestaurantService? restaurantService})
      : _restaurantService = restaurantService ?? RestaurantService();

  // Getters
  List<Restaurant> get restaurants => _restaurants;
  Restaurant? get selectedRestaurant => _selectedRestaurant;
  List<Product> get restaurantMenu => _restaurantMenu;
  List<working_time.WorkingTime> get workingHours => _workingHours;
  bool get isLoading => _isLoading;
  bool get isLoadingWorkingHours => _isLoadingWorkingHours;
  String? get error => _error;

  // Pagination getters
  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  int get totalItems => _totalItems;
  int get itemsPerPage => _itemsPerPage;

  // Fetch all restaurants with pagination
  Future<void> fetchRestaurants({int page = 1, double? userLat, double? userLng}) async {
    // Store user location for pagination
    _userLat = userLat;
    _userLng = userLng;

    bool shouldNotify = false;

    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      shouldNotify = true;
    }

    if (shouldNotify) {
      Future.microtask(() => notifyListeners());
    }

    try {
      debugPrint('Starting to fetch restaurants for page $page...');
      final response = await _restaurantService.getAllRestaurants(
        page: page,
        limit: _itemsPerPage,
        userLat: userLat,
        userLng: userLng,
      );
      final restaurantsData = List<Map<String, dynamic>>.from(response['data']);
      final pagination = response['pagination'];

      debugPrint('Received ${restaurantsData.length} restaurants from service');

      // Update pagination info
      _currentPage = pagination['currentPage'] ?? page;
      _totalPages = pagination['totalPages'] ?? 1;
      _totalItems = pagination['totalItems'] ?? 0;
      _itemsPerPage = pagination['itemsPerPage'] ?? 10;

      // Clear existing restaurants if this is page 1, otherwise append
      if (page == 1) {
        _restaurants = [];
      }

      // Process each restaurant item safely
      for (int i = 0; i < restaurantsData.length; i++) {
        try {
          final item = restaurantsData[i];

          // Create Restaurant object and add to list
          final restaurant = Restaurant.fromJson(item);
          _restaurants.add(restaurant);
          debugPrint('Successfully processed restaurant: ${restaurant.name}');
        } catch (itemError) {
          // Log error but continue processing other items
          debugPrint('Error processing restaurant item at index $i: $itemError');
          debugPrint('Item data: ${restaurantsData[i]}');
        }
      }

      debugPrint('Successfully processed ${_restaurants.length} restaurants');

      // Clear any previous errors if we successfully got some data
      if (_restaurants.isNotEmpty) {
        _error = null;
      }

    } catch (e) {
      _error = e.toString();
      debugPrint('Error in fetchRestaurants: $e');

      // If this is a type error, provide a more user-friendly message
      if (e.toString().contains('type') && e.toString().contains('Map')) {
        _error = 'خطأ في تحميل بيانات المطاعم. يرجى المحاولة مرة أخرى.';
      }
    } finally {
      _isLoading = false;
      Future.microtask(() => notifyListeners());
    }
  }

  // Load next page
  Future<void> loadNextPage() async {
    if (_currentPage < _totalPages && !_isLoading) {
      await fetchRestaurants(page: _currentPage + 1, userLat: _userLat, userLng: _userLng);
    }
  }

  // Load specific page
  Future<void> loadPage(int page) async {
    if (page >= 1 && page <= _totalPages && !_isLoading) {
      await fetchRestaurants(page: page, userLat: _userLat, userLng: _userLng);
    }
  }

  // Fetch restaurant by ID
  Future<void> fetchRestaurantById(String restaurantId) async {
    // Use a local variable to track if we need to notify
    bool shouldNotify = false;

    // Only notify if we're not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      shouldNotify = true;
    }

    // Notify outside of the build phase using microtask
    if (shouldNotify) {
      Future.microtask(() => notifyListeners());
    }

    try {
      final restaurantData = await _restaurantService.getRestaurantById(restaurantId);

      // Convert from Map (which could be Map<dynamic, dynamic>) to Map<String, dynamic>
      Map<String, dynamic> restaurantJson = {};

      try {
        restaurantData.forEach((key, value) {
          // Only include string keys
          restaurantJson[key.toString()] = value;
        });

        // Create Restaurant object
        _selectedRestaurant = Restaurant.fromJson(restaurantJson);
      } catch (e) {
        throw Exception('Invalid restaurant data format: $e');
      }
    } catch (e) {
      _error = e.toString();
      debugPrint('Error in fetchRestaurantById: $e');
    } finally {
      _isLoading = false;
      // Use microtask to ensure notification happens outside build phase
      Future.microtask(() => notifyListeners());
    }
  }

  // Fetch restaurant menu
  Future<void> fetchRestaurantMenu(String restaurantId) async {
    // Use a local variable to track if we need to notify
    bool shouldNotify = false;

    // Only notify if we're not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      shouldNotify = true;
    }

    // Notify outside of the build phase using microtask
    if (shouldNotify) {
      Future.microtask(() => notifyListeners());
    }

    try {
      // Use the new getRestaurantProducts method instead
      _restaurantMenu = await _restaurantService.getRestaurantProducts(restaurantId);

      // If no products were found, try the old method as fallback
      if (_restaurantMenu.isEmpty) {
        final menuData = await _restaurantService.getRestaurantMenu(restaurantId);

        // Clear existing menu to avoid duplicates
        _restaurantMenu = [];

        // Process each menu item safely
        for (var item in menuData) {
          try {
            // Ensure the item is a Map<String, dynamic>
            Map<String, dynamic> productJson = {};

            if (item is Map) {
              // Convert from Map (which could be Map<dynamic, dynamic>) to Map<String, dynamic>
              item.forEach((key, value) {
                // Only include string keys
                productJson[key.toString()] = value;
              });

              // Create Product object and add to list
              final product = Product.fromJson(productJson);
              _restaurantMenu.add(product);
            }
          } catch (itemError) {
            // Log error but continue processing other items
            debugPrint('Error processing menu item: $itemError');
          }
        }
      }

      // Group products by category for better display
      _groupProductsByCategory();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error in fetchRestaurantMenu: $e');
    } finally {
      _isLoading = false;
      // Use microtask to ensure notification happens outside build phase
      Future.microtask(() => notifyListeners());
    }
  }

  // Helper method to group products by category
  void _groupProductsByCategory() {
    // This will be used in the UI to display products by category
    // No implementation needed for now
  }

  // Fetch restaurants by category
  Future<void> fetchRestaurantsByCategory(String categoryId) async {
    // Use a local variable to track if we need to notify
    bool shouldNotify = false;

    // Only notify if we're not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      shouldNotify = true;
    }

    // Notify outside of the build phase using microtask
    if (shouldNotify) {
      Future.microtask(() => notifyListeners());
    }

    try {
      final restaurantsData = await _restaurantService.getRestaurantsByCategory(categoryId);

      // Clear existing restaurants to avoid duplicates
      _restaurants = [];

      // Process each restaurant item safely
      for (var item in restaurantsData) {
        try {
          // Ensure the item is a Map<String, dynamic>
          Map<String, dynamic> restaurantJson;

          if (item is Map) {
            // Convert from Map (which could be Map<dynamic, dynamic>) to Map<String, dynamic>
            restaurantJson = {};
            item.forEach((key, value) {
              // Only include string keys
              restaurantJson[key.toString()] = value;
            });
          } else {
            // Skip invalid items
            debugPrint('Skipping invalid restaurant data: $item');
            continue;
          }

          // Create Restaurant object and add to list
          final restaurant = Restaurant.fromJson(restaurantJson);
          _restaurants.add(restaurant);
        } catch (itemError) {
          // Log error but continue processing other items
          debugPrint('Error processing restaurant item: $itemError');
        }
      }
    } catch (e) {
      _error = e.toString();
      debugPrint('Error in fetchRestaurantsByCategory: $e');
    } finally {
      _isLoading = false;
      // Use microtask to ensure notification happens outside build phase
      Future.microtask(() => notifyListeners());
    }
  }

  // Search restaurants
  Future<void> searchRestaurants(String query) async {
    // Use a local variable to track if we need to notify
    bool shouldNotify = false;

    // Only notify if we're not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      shouldNotify = true;
    }

    // Notify outside of the build phase using microtask
    if (shouldNotify) {
      Future.microtask(() => notifyListeners());
    }

    try {
      final restaurantsData = await _restaurantService.searchRestaurants(query);

      // Clear existing restaurants to avoid duplicates
      _restaurants = [];

      // Process each restaurant item safely
      for (var item in restaurantsData) {
        try {
          // Ensure the item is a Map<String, dynamic>
          Map<String, dynamic> restaurantJson;

          if (item is Map) {
            // Convert from Map (which could be Map<dynamic, dynamic>) to Map<String, dynamic>
            restaurantJson = {};
            item.forEach((key, value) {
              // Only include string keys
              restaurantJson[key.toString()] = value;
            });
          } else {
            // Skip invalid items
            debugPrint('Skipping invalid restaurant data: $item');
            continue;
          }

          // Create Restaurant object and add to list
          final restaurant = Restaurant.fromJson(restaurantJson);
          _restaurants.add(restaurant);
        } catch (itemError) {
          // Log error but continue processing other items
          debugPrint('Error processing restaurant item: $itemError');
        }
      }
    } catch (e) {
      _error = e.toString();
      debugPrint('Error in searchRestaurants: $e');
    } finally {
      _isLoading = false;
      // Use microtask to ensure notification happens outside build phase
      Future.microtask(() => notifyListeners());
    }
  }

  // Select a restaurant
  void selectRestaurant(Restaurant restaurant) {
    _selectedRestaurant = restaurant;
    // Use microtask to ensure notification happens outside build phase
    Future.microtask(() => notifyListeners());
  }

  // Clear selected restaurant
  void clearSelectedRestaurant() {
    _selectedRestaurant = null;
    _restaurantMenu = [];
    // Use microtask to ensure notification happens outside build phase
    Future.microtask(() => notifyListeners());
  }

  // Fetch restaurant working hours
  Future<void> fetchRestaurantWorkingHours(String restaurantId) async {
    _isLoadingWorkingHours = true;
    // Use microtask to ensure notification happens outside build phase
    Future.microtask(() => notifyListeners());

    try {
      final workingHoursData = await _restaurantService.getRestaurantWorkingHours(restaurantId);
      _workingHours = workingHoursData;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoadingWorkingHours = false;
      // Use microtask to ensure notification happens outside build phase
      Future.microtask(() => notifyListeners());
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    // Use microtask to ensure notification happens outside build phase
    Future.microtask(() => notifyListeners());
  }
}
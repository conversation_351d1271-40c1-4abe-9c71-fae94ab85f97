"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(DashboardLayout)/layout",{

/***/ "(app-pages-browser)/./src/app/components/shared/OrderNotificationOverlay.tsx":
/*!****************************************************************!*\
  !*** ./src/app/components/shared/OrderNotificationOverlay.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/context/OrderNotificationContext */ \"(app-pages-browser)/./src/app/context/OrderNotificationContext.tsx\");\n/* harmony import */ var flowbite_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! flowbite-react */ \"(app-pages-browser)/./node_modules/flowbite-react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HiCheckCircle,HiRefresh,HiXCircle!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/esm/locale/ar/index.js\");\n/* harmony import */ var _barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HiExclamationTriangle!=!react-icons/hi2 */ \"(app-pages-browser)/./node_modules/react-icons/hi2/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst OrderNotificationOverlay = (param)=>{\n    let { restaurantId } = param;\n    _s();\n    const { pendingOrders, isConnected, isLoading, error, acceptOrder, rejectOrder, refreshOrders } = (0,_app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__.useOrderNotification)();\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentOrdersModal, setShowCurrentOrdersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRejectConfirmModal, setShowRejectConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderToReject, setOrderToReject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [processingOrderId, setProcessingOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentOrders, setCurrentOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [preparingOrders, setPreparingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Show modal when there are pending orders\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"OrderNotificationOverlay.useEffect\": ()=>{\n            console.log(\"OrderOverlay: Pending orders count changed to \".concat(pendingOrders.length));\n            if (pendingOrders.length > 0) {\n                console.log('OrderOverlay: Showing modal for pending orders');\n                setShowModal(true);\n            } else {\n                console.log('OrderOverlay: Hiding modal - no pending orders');\n                setShowModal(false);\n            }\n        }\n    }[\"OrderNotificationOverlay.useEffect\"], [\n        pendingOrders.length\n    ]);\n    // Fetch preparing orders (status = 1) - same pattern as pending orders\n    const fetchPreparingOrders = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\": async ()=>{\n            if (!restaurantId) {\n                console.log('OrderOverlay: No restaurant ID provided for preparing orders');\n                return;\n            }\n            try {\n                console.log(\"OrderOverlay: Fetching preparing orders for restaurant \".concat(restaurantId));\n                const accessToken = localStorage.getItem('accessToken');\n                if (!accessToken) {\n                    throw new Error('No access token found');\n                }\n                const apiUrl = \"\".concat(\"http://localhost:3001\", \"/orders/restaurant/\").concat(restaurantId, \"?page=1&size=50\");\n                console.log(\"OrderOverlay: Making request to \".concat(apiUrl));\n                const response = await fetch(apiUrl, {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(accessToken),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log(\"OrderOverlay: Response status: \".concat(response.status));\n                if (!response.ok) {\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const responseData = await response.json();\n                console.log(\"OrderOverlay: Received response for preparing orders:\", responseData);\n                // Handle paginated response format - same as pending orders\n                const orders = responseData.items || responseData.data || [];\n                console.log(\"OrderOverlay: Received \".concat(orders.length, \" total orders\"));\n                // Filter only preparing orders (Status = 1)\n                const preparing = orders.filter({\n                    \"OrderNotificationOverlay.useCallback[fetchPreparingOrders].preparing\": (order)=>order.Status === 1\n                }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders].preparing\"]);\n                console.log(\"OrderOverlay: Found \".concat(preparing.length, \" preparing orders\"));\n                setPreparingOrders(preparing);\n                // Log preparing orders for debugging\n                if (preparing.length > 0) {\n                    console.log('OrderOverlay: Preparing orders:', preparing.map({\n                        \"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\": (o)=>({\n                                id: o.OrderID,\n                                status: o.Status,\n                                total: o.CartTotalPrice\n                            })\n                    }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\"]));\n                }\n            } catch (err) {\n                console.error('OrderOverlay: Error fetching preparing orders:', err);\n            }\n        }\n    }[\"OrderNotificationOverlay.useCallback[fetchPreparingOrders]\"], [\n        restaurantId\n    ]);\n    // Fetch preparing orders periodically - same pattern as pending orders\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"OrderNotificationOverlay.useEffect\": ()=>{\n            fetchPreparingOrders(); // Initial fetch\n            const interval = setInterval(fetchPreparingOrders, 15000); // Update every 15 seconds\n            return ({\n                \"OrderNotificationOverlay.useEffect\": ()=>clearInterval(interval)\n            })[\"OrderNotificationOverlay.useEffect\"];\n        }\n    }[\"OrderNotificationOverlay.useEffect\"], [\n        fetchPreparingOrders\n    ]);\n    const handleAcceptOrder = async (orderId)=>{\n        try {\n            setProcessingOrderId(orderId);\n            await acceptOrder(orderId);\n        } catch (err) {\n            console.error('Failed to accept order:', err);\n        } finally{\n            setProcessingOrderId(null);\n        }\n    };\n    const handleRejectOrder = async (orderId)=>{\n        setOrderToReject(orderId);\n        setShowRejectConfirmModal(true);\n    };\n    const confirmRejectOrder = async ()=>{\n        if (!orderToReject) return;\n        try {\n            setProcessingOrderId(orderToReject);\n            await rejectOrder(orderToReject);\n            setShowRejectConfirmModal(false);\n            setOrderToReject(null);\n        } catch (err) {\n            console.error('Failed to reject order:', err);\n        } finally{\n            setProcessingOrderId(null);\n        }\n    };\n    const fetchCurrentOrders = async ()=>{\n        setShowCurrentOrdersModal(true);\n        // Use the already fetched preparing orders\n        setCurrentOrders(preparingOrders);\n    };\n    const formatPrice = (price)=>{\n        return \"\".concat(price.toFixed(2), \" د.ل\");\n    };\n    const getImageUrl = (imageUrl)=>{\n        if (!imageUrl) return '/images/placeholder-food.jpg';\n        if (imageUrl.startsWith('http')) return imageUrl;\n        return \"\".concat(\"http://localhost:3001\", \"/\").concat(imageUrl.replace(/^\\/+/, ''));\n    };\n    // Connection status indicator\n    const ConnectionStatus = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-4 left-4 z-50\",\n            children: !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                color: \"failure\",\n                icon: _barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle,\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"فقدان الاتصال بالخادم\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"xs\",\n                            color: \"failure\",\n                            onClick: refreshOrders,\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 52\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 160,\n            columnNumber: 5\n        }, undefined);\n    // Order notification badge - Mind-blowing design\n    const NotificationBadge = ()=>{\n        if (pendingOrders.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-6 right-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 hover:from-orange-600 hover:via-red-600 hover:to-pink-700 text-white px-8 py-6 rounded-3xl shadow-2xl cursor-pointer transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm\",\n                onClick: ()=>setShowModal(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-orange-400 via-red-400 to-pink-500 rounded-3xl blur-xl opacity-75 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                            className: \"w-7 h-7 text-white animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-12 h-12 border-2 border-white/50 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-black text-xl tracking-wide\",\n                                                children: \"طلبات جديدة عاجلة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-yellow-300 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/90 text-sm font-semibold\",\n                                        children: [\n                                            pendingOrders.length,\n                                            \" طلب يحتاج موافقة فورية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-black text-white\",\n                                            children: pendingOrders.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-14 h-14 bg-white/10 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-3 left-3 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Current Orders Button - Mind-blowing design\n    const CurrentOrdersButton = ()=>{\n        if (preparingOrders.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: fetchCurrentOrders,\n                className: \"relative bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-700 text-white px-8 py-6 rounded-3xl shadow-2xl transform hover:scale-110 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-500 rounded-3xl blur-xl opacity-75 group-hover:opacity-90 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 group-hover:rotate-12 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-7 h-7 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-12 h-12 border-2 border-white/30 rounded-full animate-spin opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-black text-xl tracking-wide\",\n                                                children: \"الطلبات الحالية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-300 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/90 text-sm font-semibold\",\n                                        children: [\n                                            preparingOrders.length,\n                                            \" طلب قيد التحضير\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/40 group-hover:bg-white/30 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-black text-white\",\n                                            children: preparingOrders.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-3 bg-white/40 rounded-full animate-bounce opacity-60\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 translate-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 -translate-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-2 bg-white/30 rounded-full animate-bounce opacity-40\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-3 left-3 w-1 h-1 bg-green-300 rounded-full animate-pulse opacity-80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 right-2 w-0.5 h-0.5 bg-white rounded-full animate-bounce opacity-70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionStatus, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationBadge, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrentOrdersButton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showModal,\n                onClose: ()=>setShowModal(false),\n                size: \"4xl\",\n                position: \"center\",\n                dismissible: false,\n                className: \"backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-0 rounded-2xl shadow-2xl overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                                className: \"border-b border-gray-100 bg-white rounded-t-2xl px-8 py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-semibold text-gray-900 mb-1\",\n                                                            children: \"طلبات جديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                pendingOrders.length,\n                                                                \" طلب يحتاج إلى موافقة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center shadow-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: pendingOrders.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                                className: \"max-h-[75vh] overflow-y-auto bg-gray-50 p-6\",\n                                children: pendingOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiCheckCircle, {\n                                            className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600\",\n                                            children: \"لا توجد طلبات في الانتظار\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: pendingOrders.map((order)=>{\n                                        var _order_Products;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-6 h-6 text-orange-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                                            children: [\n                                                                                \"طلب رقم #\",\n                                                                                order.OrderID\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 385,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 384,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(order.OrderDate), {\n                                                                                                addSuffix: true,\n                                                                                                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                                                            })\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 387,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                order.CustomerPhoneNum && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 398,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 397,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: order.CustomerPhoneNum\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 400,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 396,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-end gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-orange-100 text-orange-800 px-3 py-1 rounded-lg text-sm font-medium\",\n                                                                    children: \"في الانتظار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-semibold text-gray-900\",\n                                                                            children: formatPrice(order.CartTotalPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"المجموع الكلي\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                order.Note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"ملاحظات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 bg-gray-50 p-2 rounded\",\n                                                            children: order.Note\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3 text-blue-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: \"المنتجات المطلوبة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        ((_order_Products = order.Products) === null || _order_Products === void 0 ? void 0 : _order_Products.length) || 0,\n                                                                        \" منتج\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: order.Products && order.Products.length > 0 ? order.Products.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-16 h-16 rounded-xl overflow-hidden bg-gray-100\",\n                                                                                        children: item.Product.Image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            src: getImageUrl(item.Product.Image),\n                                                                                            alt: item.Product.ProductName,\n                                                                                            className: \"w-full h-full object-cover\",\n                                                                                            onError: (e)=>{\n                                                                                                const target = e.target;\n                                                                                                target.src = '/images/placeholder-food.jpg';\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 459,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-full h-full flex items-center justify-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-6 h-6 text-gray-400\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    fillRule: \"evenodd\",\n                                                                                                    d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                                                    clipRule: \"evenodd\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 471,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 470,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 469,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 457,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n                                                                                        children: item.Quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 477,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 min-w-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-start mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"font-semibold text-gray-900 truncate\",\n                                                                                                children: item.Product.ProductName\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 485,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-right ml-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        className: \"text-lg font-bold text-green-600\",\n                                                                                                        children: formatPrice(item.Product.Price * item.Quantity)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 489,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: [\n                                                                                                            formatPrice(item.Product.Price),\n                                                                                                            \" \\xd7 \",\n                                                                                                            item.Quantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 492,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 488,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 484,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    item.IngredientUsages && item.IngredientUsages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-medium text-gray-700 mb-2\",\n                                                                                                children: \"المكونات المختارة:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 501,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex flex-wrap gap-1\",\n                                                                                                children: item.IngredientUsages.map((usage, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(usage.IsNeeded ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"w-1.5 h-1.5 rounded-full mr-1 \".concat(usage.IsNeeded ? 'bg-green-500' : 'bg-red-500')\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                                lineNumber: 512,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            usage.Ingredient.IngredientName,\n                                                                                                            usage.IsNeeded ? ' ✓' : ' ✗'\n                                                                                                        ]\n                                                                                                    }, idx, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                        lineNumber: 504,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 502,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 500,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center py-8 bg-gray-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-gray-400 mx-auto mb-3\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500 font-medium\",\n                                                                        children: \"لا توجد منتجات في هذا الطلب\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                (order.RestaurantName || order.DriverFirstName || order.DriverLastName || order.DistanceInMeters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: \"معلومات إضافية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 space-y-3\",\n                                                            children: [\n                                                                order.RestaurantName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 553,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"المطعم:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: order.RestaurantName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (order.DriverFirstName || order.DriverLastName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 566,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"السائق:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"\".concat(order.DriverFirstName || '', \" \").concat(order.DriverLastName || '').trim()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                order.DistanceInMeters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 579,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"المسافة:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                (order.DistanceInMeters / 1000).toFixed(2),\n                                                                                \" كم\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-3 pt-6 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAcceptOrder(order.OrderID),\n                                                            disabled: processingOrderId === order.OrderID,\n                                                            className: \"flex-1 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: processingOrderId === order.OrderID ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                                        size: \"sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"جاري المعالجة...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiCheckCircle, {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"قبول وبدء التحضير\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRejectOrder(order.OrderID),\n                                                            disabled: processingOrderId === order.OrderID,\n                                                            className: \"flex-1 bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: processingOrderId === order.OrderID ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                                        size: \"sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"جاري المعالجة...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"رفض الطلبية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, order.OrderID, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Footer, {\n                        className: \"bg-white border-t border-gray-200 rounded-b-2xl px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: refreshOrders,\n                                    disabled: isLoading,\n                                    className: \"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                    children: [\n                                        isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 28\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 52\n                                        }, undefined),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowModal(false),\n                                    className: \"px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200\",\n                                    children: \"إغلاق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showRejectConfirmModal,\n                onClose: ()=>setShowRejectConfirmModal(false),\n                size: \"md\",\n                position: \"center\",\n                className: \"backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                            className: \"border-b border-gray-100 bg-gradient-to-r from-red-50 to-red-100 rounded-t-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-red-900\",\n                                                children: \"تأكيد رفض الطلبية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-700\",\n                                                children: \"هذا الإجراء لا يمكن التراجع عنه\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiExclamationTriangle_react_icons_hi2__WEBPACK_IMPORTED_MODULE_4__.HiExclamationTriangle, {\n                                            className: \"w-8 h-8 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"هل أنت متأكد من رفض هذه الطلبية؟\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: \"سيتم إشعار العميل برفض الطلبية ولن تتمكن من قبولها مرة أخرى\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: confirmRejectOrder,\n                                                disabled: processingOrderId !== null,\n                                                className: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\",\n                                                children: processingOrderId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                            size: \"sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"جاري الرفض...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiXCircle, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"نعم، رفض الطلبية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowRejectConfirmModal(false),\n                                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200\",\n                                                children: \"إلغاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                show: showCurrentOrdersModal,\n                onClose: ()=>setShowCurrentOrdersModal(false),\n                size: \"5xl\",\n                position: \"center\",\n                className: \"backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 border-0 rounded-2xl shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Header, {\n                            className: \"border-b border-gray-100 bg-white rounded-t-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-900 mb-1\",\n                                                        children: \"الطلبات الحالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-600\",\n                                                        children: [\n                                                            \"الطلبات قيد التحضير (\",\n                                                            currentOrders.length,\n                                                            \" طلب)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-400 to-indigo-500 text-white text-xl font-bold px-5 py-3 rounded-full shadow-lg\",\n                                                children: currentOrders.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-pulse shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Body, {\n                            className: \"max-h-[70vh] overflow-y-auto bg-gradient-to-br from-gray-50 to-white p-6\",\n                            children: currentOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 text-blue-500\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                        children: \"لا توجد طلبات قيد التحضير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"جميع الطلبات تم تحضيرها أو لا توجد طلبات مقبولة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: currentOrders.map((order)=>{\n                                    var _order_Products;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl p-6 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 mb-1\",\n                                                                        children: [\n                                                                            \"طلب رقم #\",\n                                                                            order.OrderID\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 795,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 794,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(order.OrderDate), {\n                                                                                            addSuffix: true,\n                                                                                            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 797,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            order.CustomerPhoneNum && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 807,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 806,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: order.CustomerPhoneNum\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 809,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                lineNumber: 805,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-green-400 to-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg mb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"قيد التحضير\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: formatPrice(order.CartTotalPrice)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"المجموع الكلي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 text-green-600\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 834,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"المنتجات المطلوبة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full\",\n                                                                children: [\n                                                                    ((_order_Products = order.Products) === null || _order_Products === void 0 ? void 0 : _order_Products.length) || 0,\n                                                                    \" منتج\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: order.Products && order.Products.length > 0 ? order.Products.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 border border-gray-100 rounded-xl p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-16 h-16 rounded-xl overflow-hidden bg-gray-100\",\n                                                                                    children: item.Product.Image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: getImageUrl(item.Product.Image),\n                                                                                        alt: item.Product.ProductName,\n                                                                                        className: \"w-full h-full object-cover\",\n                                                                                        onError: (e)=>{\n                                                                                            const target = e.target;\n                                                                                            target.src = '/images/placeholder-food.jpg';\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 855,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-6 h-6 text-gray-400\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                lineNumber: 867,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 866,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                        lineNumber: 865,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 853,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n                                                                                    children: item.Quantity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 873,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 852,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                            className: \"font-semibold text-gray-900 truncate\",\n                                                                                            children: item.Product.ProductName\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 881,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-right ml-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                                    children: formatPrice(item.Product.Price * item.Quantity)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 885,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        formatPrice(item.Product.Price),\n                                                                                                        \" \\xd7 \",\n                                                                                                        item.Quantity\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 888,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 884,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 880,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                item.IngredientUsages && item.IngredientUsages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-medium text-gray-700 mb-2\",\n                                                                                            children: \"المكونات المختارة:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 897,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex flex-wrap gap-1\",\n                                                                                            children: item.IngredientUsages.map((usage, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(usage.IsNeeded ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"w-1.5 h-1.5 rounded-full mr-1 \".concat(usage.IsNeeded ? 'bg-green-500' : 'bg-red-500')\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                            lineNumber: 908,\n                                                                                                            columnNumber: 45\n                                                                                                        }, undefined),\n                                                                                                        usage.Ingredient.IngredientName,\n                                                                                                        usage.IsNeeded ? ' ✓' : ' ✗'\n                                                                                                    ]\n                                                                                                }, idx, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                                    lineNumber: 900,\n                                                                                                    columnNumber: 43\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                            lineNumber: 898,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                                    lineNumber: 896,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 29\n                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8 bg-gray-50 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-3\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 font-medium\",\n                                                                    children: \"لا توجد منتجات في هذا الطلب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, order.OrderID, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Modal.Footer, {\n                            className: \"bg-white border-t border-gray-100 rounded-b-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        color: \"gray\",\n                                        onClick: fetchCurrentOrders,\n                                        disabled: isLoading,\n                                        children: [\n                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Spinner, {\n                                                size: \"sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiCheckCircle_HiRefresh_HiXCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_5__.HiRefresh, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 54\n                                            }, undefined),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(flowbite_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        color: \"light\",\n                                        onClick: ()=>setShowCurrentOrdersModal(false),\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rest Delivery\\\\restaurant_mange\\\\src\\\\app\\\\components\\\\shared\\\\OrderNotificationOverlay.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(OrderNotificationOverlay, \"JxB6qRQkP96uprzgosg8sJ6dKaE=\", false, function() {\n    return [\n        _app_context_OrderNotificationContext__WEBPACK_IMPORTED_MODULE_2__.useOrderNotification\n    ];\n});\n_c = OrderNotificationOverlay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderNotificationOverlay);\nvar _c;\n$RefreshReg$(_c, \"OrderNotificationOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29tcG9uZW50cy9zaGFyZWQvT3JkZXJOb3RpZmljYXRpb25PdmVybGF5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDc0M7QUFDZjtBQUMyQjtBQUMzQztBQUNWO0FBQ21CO0FBTXhELE1BQU1hLDJCQUFvRTtRQUFDLEVBQUVDLFlBQVksRUFBRTs7SUFDekYsTUFBTSxFQUNKQyxhQUFhLEVBQ2JDLFdBQVcsRUFDWEMsU0FBUyxFQUNUQyxLQUFLLEVBQ0xDLFdBQVcsRUFDWEMsV0FBVyxFQUNYQyxhQUFhLEVBQ2QsR0FBR25CLDJGQUFvQkE7SUFFeEIsTUFBTSxDQUFDb0IsV0FBV0MsYUFBYSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDdUIsd0JBQXdCQywwQkFBMEIsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ3lCLHdCQUF3QkMsMEJBQTBCLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUNyRSxNQUFNLENBQUMyQixlQUFlQyxpQkFBaUIsR0FBRzVCLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUM2QixtQkFBbUJDLHFCQUFxQixHQUFHOUIsK0NBQVFBLENBQWdCO0lBQzFFLE1BQU0sQ0FBQytCLGVBQWVDLGlCQUFpQixHQUFHaEMsK0NBQVFBLENBQVEsRUFBRTtJQUM1RCxNQUFNLENBQUNpQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQVEsRUFBRTtJQUVoRSwyQ0FBMkM7SUFDM0NELHNEQUFlOzhDQUFDO1lBQ2RxQyxRQUFRQyxHQUFHLENBQUMsaURBQXNFLE9BQXJCdkIsY0FBY3dCLE1BQU07WUFDakYsSUFBSXhCLGNBQWN3QixNQUFNLEdBQUcsR0FBRztnQkFDNUJGLFFBQVFDLEdBQUcsQ0FBQztnQkFDWmYsYUFBYTtZQUNmLE9BQU87Z0JBQ0xjLFFBQVFDLEdBQUcsQ0FBQztnQkFDWmYsYUFBYTtZQUNmO1FBQ0Y7NkNBQUc7UUFBQ1IsY0FBY3dCLE1BQU07S0FBQztJQUV6Qix1RUFBdUU7SUFDdkUsTUFBTUMsdUJBQXVCeEMsd0RBQWlCO3NFQUFDO1lBQzdDLElBQUksQ0FBQ2MsY0FBYztnQkFDakJ1QixRQUFRQyxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLElBQUk7Z0JBQ0ZELFFBQVFDLEdBQUcsQ0FBQywwREFBdUUsT0FBYnhCO2dCQUV0RSxNQUFNNEIsY0FBY0MsYUFBYUMsT0FBTyxDQUFDO2dCQUN6QyxJQUFJLENBQUNGLGFBQWE7b0JBQ2hCLE1BQU0sSUFBSUcsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTUMsU0FBUyxHQUF3RGhDLE9BQXJEaUMsdUJBQStCLEVBQUMsdUJBQWtDLE9BQWJqQyxjQUFhO2dCQUNwRnVCLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBMEMsT0FBUFE7Z0JBRS9DLE1BQU1JLFdBQVcsTUFBTUMsTUFBTUwsUUFBUTtvQkFDbkNNLFNBQVM7d0JBQ1AsaUJBQWlCLFVBQXNCLE9BQVpWO3dCQUMzQixnQkFBZ0I7b0JBQ2xCO2dCQUNGO2dCQUVBTCxRQUFRQyxHQUFHLENBQUMsa0NBQWtELE9BQWhCWSxTQUFTRyxNQUFNO2dCQUU3RCxJQUFJLENBQUNILFNBQVNJLEVBQUUsRUFBRTtvQkFDaEIsTUFBTSxJQUFJVCxNQUFNLHVCQUF1QyxPQUFoQkssU0FBU0csTUFBTTtnQkFDeEQ7Z0JBRUEsTUFBTUUsZUFBZSxNQUFNTCxTQUFTTSxJQUFJO2dCQUN4Q25CLFFBQVFDLEdBQUcsQ0FBRSx5REFBd0RpQjtnQkFFckUsNERBQTREO2dCQUM1RCxNQUFNRSxTQUFnQkYsYUFBYUcsS0FBSyxJQUFJSCxhQUFhSSxJQUFJLElBQUksRUFBRTtnQkFDbkV0QixRQUFRQyxHQUFHLENBQUMsMEJBQXdDLE9BQWRtQixPQUFPbEIsTUFBTSxFQUFDO2dCQUVwRCw0Q0FBNEM7Z0JBQzVDLE1BQU1xQixZQUFZSCxPQUFPSSxNQUFNOzRGQUFDQyxDQUFBQSxRQUFTQSxNQUFNQyxNQUFNLEtBQUs7O2dCQUMxRDFCLFFBQVFDLEdBQUcsQ0FBQyx1QkFBd0MsT0FBakJzQixVQUFVckIsTUFBTSxFQUFDO2dCQUVwREosbUJBQW1CeUI7Z0JBRW5CLHFDQUFxQztnQkFDckMsSUFBSUEsVUFBVXJCLE1BQU0sR0FBRyxHQUFHO29CQUN4QkYsUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ3NCLFVBQVVJLEdBQUc7c0ZBQUNDLENBQUFBLElBQU07Z0NBQ2pFQyxJQUFJRCxFQUFFRSxPQUFPO2dDQUNiZCxRQUFRWSxFQUFFRixNQUFNO2dDQUNoQkssT0FBT0gsRUFBRUksY0FBYzs0QkFDekI7O2dCQUNGO1lBQ0YsRUFBRSxPQUFPQyxLQUFLO2dCQUNaakMsUUFBUW5CLEtBQUssQ0FBQyxrREFBa0RvRDtZQUNsRTtRQUNGO3FFQUFHO1FBQUN4RDtLQUFhO0lBRWpCLHVFQUF1RTtJQUN2RWQsc0RBQWU7OENBQUM7WUFDZHdDLHdCQUF3QixnQkFBZ0I7WUFDeEMsTUFBTStCLFdBQVdDLFlBQVloQyxzQkFBc0IsUUFBUSwwQkFBMEI7WUFFckY7c0RBQU8sSUFBTWlDLGNBQWNGOztRQUM3Qjs2Q0FBRztRQUFDL0I7S0FBcUI7SUFFekIsTUFBTWtDLG9CQUFvQixPQUFPQztRQUMvQixJQUFJO1lBQ0Y1QyxxQkFBcUI0QztZQUNyQixNQUFNeEQsWUFBWXdEO1FBQ3BCLEVBQUUsT0FBT0wsS0FBSztZQUNaakMsUUFBUW5CLEtBQUssQ0FBQywyQkFBMkJvRDtRQUMzQyxTQUFVO1lBQ1J2QyxxQkFBcUI7UUFDdkI7SUFDRjtJQUVBLE1BQU02QyxvQkFBb0IsT0FBT0Q7UUFDL0I5QyxpQkFBaUI4QztRQUNqQmhELDBCQUEwQjtJQUM1QjtJQUVBLE1BQU1rRCxxQkFBcUI7UUFDekIsSUFBSSxDQUFDakQsZUFBZTtRQUVwQixJQUFJO1lBQ0ZHLHFCQUFxQkg7WUFDckIsTUFBTVIsWUFBWVE7WUFDbEJELDBCQUEwQjtZQUMxQkUsaUJBQWlCO1FBQ25CLEVBQUUsT0FBT3lDLEtBQUs7WUFDWmpDLFFBQVFuQixLQUFLLENBQUMsMkJBQTJCb0Q7UUFDM0MsU0FBVTtZQUNSdkMscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNK0MscUJBQXFCO1FBQ3pCckQsMEJBQTBCO1FBQzFCLDJDQUEyQztRQUMzQ1EsaUJBQWlCQztJQUNuQjtJQUVBLE1BQU02QyxjQUFjLENBQUNDO1FBQ25CLE9BQU8sR0FBb0IsT0FBakJBLE1BQU1DLE9BQU8sQ0FBQyxJQUFHO0lBQzdCO0lBRUEsTUFBTUMsY0FBYyxDQUFDQztRQUNuQixJQUFJLENBQUNBLFVBQVUsT0FBTztRQUN0QixJQUFJQSxTQUFTQyxVQUFVLENBQUMsU0FBUyxPQUFPRDtRQUN4QyxPQUFPLEdBQXNDQSxPQUFuQ3BDLHVCQUErQixFQUFDLEtBQWdDLE9BQTdCb0MsU0FBU0UsT0FBTyxDQUFDLFFBQVE7SUFDeEU7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTUMsbUJBQW1CLGtCQUN2Qiw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDWixDQUFDeEUsNkJBQ0EsOERBQUNWLGlEQUFLQTtnQkFBQ21GLE9BQU07Z0JBQVVDLE1BQU05RSwrR0FBcUJBO2dCQUFFNEUsV0FBVTswQkFDNUQsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0c7c0NBQUs7Ozs7OztzQ0FDTiw4REFBQ3ZGLGtEQUFNQTs0QkFDTHdGLE1BQUs7NEJBQ0xILE9BQU07NEJBQ05JLFNBQVN4RTs0QkFDVHlFLFVBQVU3RTtzQ0FFVEEsMEJBQVksOERBQUNaLG1EQUFPQTtnQ0FBQ3VGLE1BQUs7Ozs7OzBEQUFVLDhEQUFDbkYsOEdBQVNBO2dDQUFDK0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUXRFLGlEQUFpRDtJQUNqRCxNQUFNTyxvQkFBb0I7UUFDeEIsSUFBSWhGLGNBQWN3QixNQUFNLEtBQUssR0FBRyxPQUFPO1FBRXZDLHFCQUNFLDhEQUFDZ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQ0NDLFdBQVU7Z0JBQ1ZLLFNBQVMsSUFBTXRFLGFBQWE7O2tDQUc1Qiw4REFBQ2dFO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzVFLCtHQUFxQkE7NENBQUM0RSxXQUFVOzs7Ozs7Ozs7OztrREFHbkMsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7MENBSWpCLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQW1DOzs7Ozs7MERBR25ELDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUVqQiw4REFBQ0c7d0NBQUtILFdBQVU7OzRDQUNiekUsY0FBY3dCLE1BQU07NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBSzFCLDhEQUFDZ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0c7NENBQUtILFdBQVU7c0RBQ2J6RSxjQUFjd0IsTUFBTTs7Ozs7Ozs7Ozs7a0RBSXpCLDhEQUFDZ0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLbkIsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztJQUl2QjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNUSxzQkFBc0I7UUFDMUIsSUFBSTlELGdCQUFnQkssTUFBTSxLQUFLLEdBQUcsT0FBTztRQUV6QyxxQkFDRSw4REFBQ2dEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNTO2dCQUNDSixTQUFTZjtnQkFDVFUsV0FBVTs7a0NBR1YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ1U7NENBQUlWLFdBQVU7NENBQXFCVyxNQUFLOzRDQUFlQyxTQUFROzs4REFDOUQsOERBQUNDO29EQUFLQyxHQUFFOzs7Ozs7OERBQ1IsOERBQUNEO29EQUFLRSxVQUFTO29EQUFVRCxHQUFFO29EQUFtS0UsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTNNLDhEQUFDakI7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FJakIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRztnREFBS0gsV0FBVTswREFBbUM7Ozs7OzswREFHbkQsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7a0RBRWpCLDhEQUFDRzt3Q0FBS0gsV0FBVTs7NENBQ2J0RCxnQkFBZ0JLLE1BQU07NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBSzVCLDhEQUFDZ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0c7NENBQUtILFdBQVU7c0RBQ2J0RCxnQkFBZ0JLLE1BQU07Ozs7Ozs7Ozs7O2tEQUkzQiw4REFBQ2dEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7a0RBRWpCLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7NENBQStEaUIsT0FBTztnREFBQ0MsZ0JBQWdCOzRDQUFNOzs7Ozs7Ozs7OztrREFFOUcsOERBQUNuQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7NENBQStEaUIsT0FBTztnREFBQ0MsZ0JBQWdCOzRDQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbEgsOERBQUNuQjt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkI7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUNGOzs7OzswQkFDRCw4REFBQ1M7Ozs7OzBCQUNELDhEQUFDQzs7Ozs7MEJBR0QsOERBQUM3RixpREFBS0E7Z0JBQ0p3RyxNQUFNckY7Z0JBQ05zRixTQUFTLElBQU1yRixhQUFhO2dCQUM1QnFFLE1BQUs7Z0JBQ0xpQixVQUFTO2dCQUNUQyxhQUFhO2dCQUNidEIsV0FBVTs7a0NBRVYsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ3JGLGlEQUFLQSxDQUFDNEcsTUFBTTtnQ0FBQ3ZCLFdBQVU7MENBQ3RCLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUM1RSwrR0FBcUJBO3dEQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7OERBSW5DLDhEQUFDRDs7c0VBQ0MsOERBQUN5Qjs0REFBR3hCLFdBQVU7c0VBQTRDOzs7Ozs7c0VBRzFELDhEQUFDeUI7NERBQUV6QixXQUFVOztnRUFDVnpFLGNBQWN3QixNQUFNO2dFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU01Qiw4REFBQ2dEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRztnREFBS0gsV0FBVTswREFDYnpFLGNBQWN3QixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU83Qiw4REFBQ3BDLGlEQUFLQSxDQUFDK0csSUFBSTtnQ0FBQzFCLFdBQVU7MENBQ3JCekUsY0FBY3dCLE1BQU0sS0FBSyxrQkFDeEIsOERBQUNnRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNqRixrSEFBYUE7NENBQUNpRixXQUFVOzs7Ozs7c0RBQ3pCLDhEQUFDeUI7NENBQUV6QixXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OERBR3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnpFLGNBQWNpRCxHQUFHLENBQUMsQ0FBQ0Y7NENBbUZUQTs2REFsRlQsOERBQUN5Qjs0Q0FFQ0MsV0FBVTs7OERBR1YsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUViLDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ1U7d0VBQUlWLFdBQVU7d0VBQTBCVyxNQUFLO3dFQUFlQyxTQUFROzswRkFDbkUsOERBQUNDO2dGQUFLQyxHQUFFOzs7Ozs7MEZBQ1IsOERBQUNEO2dGQUFLRSxVQUFTO2dGQUFVRCxHQUFFO2dGQUFtS0UsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBSzNNLDhEQUFDakI7O3NGQUNDLDhEQUFDNEI7NEVBQUczQixXQUFVOztnRkFBMkM7Z0ZBQzdDMUIsTUFBTUssT0FBTzs7Ozs7OztzRkFHekIsOERBQUNvQjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ1U7NEZBQUlWLFdBQVU7NEZBQVVXLE1BQUs7NEZBQWVDLFNBQVE7c0dBQ25ELDRFQUFDQztnR0FBS0UsVUFBUztnR0FBVUQsR0FBRTtnR0FBcUhFLFVBQVM7Ozs7Ozs7Ozs7O3NHQUUzSiw4REFBQ2I7c0dBQ0VqRiwrRkFBbUJBLENBQUMsSUFBSTBHLEtBQUt0RCxNQUFNdUQsU0FBUyxHQUFHO2dHQUM5Q0MsV0FBVztnR0FDWEMsUUFBUTVHLHVEQUFFQTs0RkFDWjs7Ozs7Ozs7Ozs7O2dGQUlIbUQsTUFBTTBELGdCQUFnQixrQkFDckIsOERBQUNqQztvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNVOzRGQUFJVixXQUFVOzRGQUFVVyxNQUFLOzRGQUFlQyxTQUFRO3NHQUNuRCw0RUFBQ0M7Z0dBQUtDLEdBQUU7Ozs7Ozs7Ozs7O3NHQUVWLDhEQUFDWDtzR0FBTTdCLE1BQU0wRCxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFRdkMsOERBQUNqQzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUF5RTs7Ozs7OzhFQUl4Riw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDeUI7NEVBQUV6QixXQUFVO3NGQUNWVCxZQUFZakIsTUFBTU8sY0FBYzs7Ozs7O3NGQUVuQyw4REFBQzRDOzRFQUFFekIsV0FBVTtzRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnREFPMUMxQixNQUFNMkQsSUFBSSxrQkFDVCw4REFBQ2xDO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3lCOzREQUFFekIsV0FBVTtzRUFBb0M7Ozs7OztzRUFDakQsOERBQUN5Qjs0REFBRXpCLFdBQVU7c0VBQ1YxQixNQUFNMkQsSUFBSTs7Ozs7Ozs7Ozs7OzhEQU1qQiw4REFBQ2xDO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ1U7d0VBQUlWLFdBQVU7d0VBQXdCVyxNQUFLO3dFQUFlQyxTQUFRO2tGQUNqRSw0RUFBQ0M7NEVBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBR1osOERBQUNvQjtvRUFBR2xDLFdBQVU7OEVBQXNDOzs7Ozs7OEVBQ3BELDhEQUFDRztvRUFBS0gsV0FBVTs7d0VBQ2IxQixFQUFBQSxrQkFBQUEsTUFBTTZELFFBQVEsY0FBZDdELHNDQUFBQSxnQkFBZ0J2QixNQUFNLEtBQUk7d0VBQUU7Ozs7Ozs7Ozs7Ozs7c0VBSWpDLDhEQUFDZ0Q7NERBQUlDLFdBQVU7c0VBQ1oxQixNQUFNNkQsUUFBUSxJQUFJN0QsTUFBTTZELFFBQVEsQ0FBQ3BGLE1BQU0sR0FBRyxJQUN6Q3VCLE1BQU02RCxRQUFRLENBQUMzRCxHQUFHLENBQUMsQ0FBQzRELE1BQU1DLHNCQUN4Qiw4REFBQ3RDO29FQUVDQyxXQUFVOzhFQUVWLDRFQUFDRDt3RUFBSUMsV0FBVTs7MEZBRWIsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ0Q7d0ZBQUlDLFdBQVU7a0dBQ1pvQyxLQUFLRSxPQUFPLENBQUNDLEtBQUssaUJBQ2pCLDhEQUFDQzs0RkFDQ0MsS0FBSy9DLFlBQVkwQyxLQUFLRSxPQUFPLENBQUNDLEtBQUs7NEZBQ25DRyxLQUFLTixLQUFLRSxPQUFPLENBQUNLLFdBQVc7NEZBQzdCM0MsV0FBVTs0RkFDVjRDLFNBQVMsQ0FBQ0M7Z0dBQ1IsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTtnR0FDdkJBLE9BQU9MLEdBQUcsR0FBRzs0RkFDZjs7Ozs7c0hBR0YsOERBQUMxQzs0RkFBSUMsV0FBVTtzR0FDYiw0RUFBQ1U7Z0dBQUlWLFdBQVU7Z0dBQXdCVyxNQUFLO2dHQUFlQyxTQUFROzBHQUNqRSw0RUFBQ0M7b0dBQUtFLFVBQVM7b0dBQVVELEdBQUU7b0dBQTZGRSxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBTXpJLDhEQUFDakI7d0ZBQUlDLFdBQVU7a0dBQ1pvQyxLQUFLVyxRQUFROzs7Ozs7Ozs7Ozs7MEZBS2xCLDhEQUFDaEQ7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTs7MEdBQ2IsOERBQUNnRDtnR0FBR2hELFdBQVU7MEdBQ1hvQyxLQUFLRSxPQUFPLENBQUNLLFdBQVc7Ozs7OzswR0FFM0IsOERBQUM1QztnR0FBSUMsV0FBVTs7a0hBQ2IsOERBQUN5Qjt3R0FBRXpCLFdBQVU7a0hBQ1ZULFlBQVk2QyxLQUFLRSxPQUFPLENBQUNXLEtBQUssR0FBR2IsS0FBS1csUUFBUTs7Ozs7O2tIQUVqRCw4REFBQ3RCO3dHQUFFekIsV0FBVTs7NEdBQ1ZULFlBQVk2QyxLQUFLRSxPQUFPLENBQUNXLEtBQUs7NEdBQUU7NEdBQUliLEtBQUtXLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0ZBTXZEWCxLQUFLYyxnQkFBZ0IsSUFBSWQsS0FBS2MsZ0JBQWdCLENBQUNuRyxNQUFNLEdBQUcsbUJBQ3ZELDhEQUFDZ0Q7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDeUI7Z0dBQUV6QixXQUFVOzBHQUF5Qzs7Ozs7OzBHQUN0RCw4REFBQ0Q7Z0dBQUlDLFdBQVU7MEdBQ1pvQyxLQUFLYyxnQkFBZ0IsQ0FBQzFFLEdBQUcsQ0FBQyxDQUFDMkUsT0FBT0Msb0JBQ2pDLDhEQUFDakQ7d0dBRUNILFdBQVcsdUVBSVYsT0FIQ21ELE1BQU1FLFFBQVEsR0FDVix3REFDQTs7MEhBR04sOERBQUNsRDtnSEFBS0gsV0FBVyxpQ0FFaEIsT0FEQ21ELE1BQU1FLFFBQVEsR0FBRyxpQkFBaUI7Ozs7Ozs0R0FFbkNGLE1BQU1HLFVBQVUsQ0FBQ0MsY0FBYzs0R0FDL0JKLE1BQU1FLFFBQVEsR0FBRyxPQUFPOzt1R0FYcEJEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21FQXREZGY7Ozs7K0ZBNEVULDhEQUFDdEM7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDVTt3RUFBSVYsV0FBVTt3RUFBdUNXLE1BQUs7d0VBQU82QyxRQUFPO3dFQUFlNUMsU0FBUTtrRkFDOUYsNEVBQUNDOzRFQUFLNEMsZUFBYzs0RUFBUUMsZ0JBQWU7NEVBQVFDLGFBQWE7NEVBQUc3QyxHQUFFOzs7Ozs7Ozs7OztrRkFFdkUsOERBQUNXO3dFQUFFekIsV0FBVTtrRkFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQU8vQzFCLENBQUFBLE1BQU1zRixjQUFjLElBQUl0RixNQUFNdUYsZUFBZSxJQUFJdkYsTUFBTXdGLGNBQWMsSUFBSXhGLE1BQU15RixnQkFBZ0IsbUJBQy9GLDhEQUFDaEU7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDVTt3RUFBSVYsV0FBVTt3RUFBd0JXLE1BQUs7d0VBQWVDLFNBQVE7a0ZBQ2pFLDRFQUFDQzs0RUFBS0UsVUFBUzs0RUFBVUQsR0FBRTs0RUFBbUlFLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBRzNLLDhEQUFDa0I7b0VBQUdsQyxXQUFVOzhFQUE4Qjs7Ozs7Ozs7Ozs7O3NFQUU5Qyw4REFBQ0Q7NERBQUlDLFdBQVU7O2dFQUNaMUIsTUFBTXNGLGNBQWMsa0JBQ25CLDhEQUFDN0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNVO29GQUFJVixXQUFVO29GQUF3QlcsTUFBSztvRkFBZUMsU0FBUTs4RkFDakUsNEVBQUNDO3dGQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs4RkFFViw4REFBQ1g7b0ZBQUtILFdBQVU7OEZBQXdCOzs7Ozs7Ozs7Ozs7c0ZBRTFDLDhEQUFDRzs0RUFBS0gsV0FBVTtzRkFDYjFCLE1BQU1zRixjQUFjOzs7Ozs7Ozs7Ozs7Z0VBSXpCdEYsQ0FBQUEsTUFBTXVGLGVBQWUsSUFBSXZGLE1BQU13RixjQUFjLG1CQUM3Qyw4REFBQy9EO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDVTtvRkFBSVYsV0FBVTtvRkFBd0JXLE1BQUs7b0ZBQWVDLFNBQVE7OEZBQ2pFLDRFQUFDQzt3RkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7OEZBRVYsOERBQUNYO29GQUFLSCxXQUFVOzhGQUF3Qjs7Ozs7Ozs7Ozs7O3NGQUUxQyw4REFBQ0c7NEVBQUtILFdBQVU7c0ZBQ2IsR0FBa0MxQixPQUEvQkEsTUFBTXVGLGVBQWUsSUFBSSxJQUFHLEtBQThCLE9BQTNCdkYsTUFBTXdGLGNBQWMsSUFBSSxJQUFLRSxJQUFJOzs7Ozs7Ozs7Ozs7Z0VBSXpFMUYsTUFBTXlGLGdCQUFnQixrQkFDckIsOERBQUNoRTtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ1U7b0ZBQUlWLFdBQVU7b0ZBQXdCVyxNQUFLO29GQUFlQyxTQUFROzhGQUNqRSw0RUFBQ0M7d0ZBQUtFLFVBQVM7d0ZBQVVELEdBQUU7d0ZBQThGRSxVQUFTOzs7Ozs7Ozs7Ozs4RkFFcEksOERBQUNiO29GQUFLSCxXQUFVOzhGQUF3Qjs7Ozs7Ozs7Ozs7O3NGQUUxQyw4REFBQ0c7NEVBQUtILFdBQVU7O2dGQUNaMUIsQ0FBQUEsTUFBTXlGLGdCQUFnQixHQUFHLElBQUcsRUFBR3RFLE9BQU8sQ0FBQztnRkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFTeEQsOERBQUNNO29EQUFJQyxXQUFVOztzRUFFYiw4REFBQ1M7NERBQ0NKLFNBQVMsSUFBTW5CLGtCQUFrQlosTUFBTUssT0FBTzs0REFDOUMyQixVQUFVaEUsc0JBQXNCZ0MsTUFBTUssT0FBTzs0REFDN0NxQixXQUFVO3NFQUVUMUQsc0JBQXNCZ0MsTUFBTUssT0FBTyxpQkFDbEMsOERBQUNvQjtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNuRixtREFBT0E7d0VBQUN1RixNQUFLOzs7Ozs7a0ZBQ2QsOERBQUNEO2tGQUFLOzs7Ozs7Ozs7OzswRkFHUiw4REFBQ0o7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDakYsa0hBQWFBO3dFQUFDaUYsV0FBVTs7Ozs7O2tGQUN6Qiw4REFBQ0c7a0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQU1aLDhEQUFDTTs0REFDQ0osU0FBUyxJQUFNakIsa0JBQWtCZCxNQUFNSyxPQUFPOzREQUM5QzJCLFVBQVVoRSxzQkFBc0JnQyxNQUFNSyxPQUFPOzREQUM3Q3FCLFdBQVU7c0VBRVQxRCxzQkFBc0JnQyxNQUFNSyxPQUFPLGlCQUNsQyw4REFBQ29CO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ25GLG1EQUFPQTt3RUFBQ3VGLE1BQUs7Ozs7OztrRkFDZCw4REFBQ0Q7a0ZBQUs7Ozs7Ozs7Ozs7OzBGQUdSLDhEQUFDSjtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNoRiw4R0FBU0E7d0VBQUNnRixXQUFVOzs7Ozs7a0ZBQ3JCLDhEQUFDRztrRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQXpRVDdCLE1BQU1LLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBc1I1Qiw4REFBQ2hFLGlEQUFLQSxDQUFDc0osTUFBTTt3QkFBQ2pFLFdBQVU7a0NBQ3RCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNTO29DQUNDSixTQUFTeEU7b0NBQ1R5RSxVQUFVN0U7b0NBQ1Z1RSxXQUFVOzt3Q0FFVHZFLDBCQUFZLDhEQUFDWixtREFBT0E7NENBQUN1RixNQUFLOzs7OztzRUFBVSw4REFBQ25GLDhHQUFTQTs0Q0FBQytFLFdBQVU7Ozs7Ozt3Q0FBYTs7Ozs7Ozs4Q0FHekUsOERBQUNTO29DQUNDSixTQUFTLElBQU10RSxhQUFhO29DQUM1QmlFLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFQLDhEQUFDckYsaURBQUtBO2dCQUNKd0csTUFBTWpGO2dCQUNOa0YsU0FBUyxJQUFNakYsMEJBQTBCO2dCQUN6Q2lFLE1BQUs7Z0JBQ0xpQixVQUFTO2dCQUNUckIsV0FBVTswQkFFViw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDckYsaURBQUtBLENBQUM0RyxNQUFNOzRCQUFDdkIsV0FBVTtzQ0FDdEIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNoRiw4R0FBU0E7NENBQUNnRixXQUFVOzs7Ozs7Ozs7OztrREFFdkIsOERBQUNEOzswREFDQyw4REFBQzRCO2dEQUFHM0IsV0FBVTswREFBaUM7Ozs7OzswREFDL0MsOERBQUN5QjtnREFBRXpCLFdBQVU7MERBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLMUMsOERBQUNyRixpREFBS0EsQ0FBQytHLElBQUk7NEJBQUMxQixXQUFVO3NDQUNwQiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzVFLCtHQUFxQkE7NENBQUM0RSxXQUFVOzs7Ozs7Ozs7OztrREFFbkMsOERBQUNrQzt3Q0FBR2xDLFdBQVU7a0RBQTJDOzs7Ozs7a0RBR3pELDhEQUFDeUI7d0NBQUV6QixXQUFVO2tEQUFxQjs7Ozs7O2tEQUlsQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDUztnREFDQ0osU0FBU2hCO2dEQUNUaUIsVUFBVWhFLHNCQUFzQjtnREFDaEMwRCxXQUFVOzBEQUVUMUQsa0NBQ0MsOERBQUN5RDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNuRixtREFBT0E7NERBQUN1RixNQUFLOzs7Ozs7c0VBQ2QsOERBQUNEO3NFQUFLOzs7Ozs7Ozs7Ozs4RUFHUiw4REFBQ0o7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDaEYsOEdBQVNBOzREQUFDZ0YsV0FBVTs7Ozs7O3NFQUNyQiw4REFBQ0c7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUtaLDhEQUFDTTtnREFDQ0osU0FBUyxJQUFNbEUsMEJBQTBCO2dEQUN6QzZELFdBQVU7MERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVVgsOERBQUNyRixpREFBS0E7Z0JBQ0p3RyxNQUFNbkY7Z0JBQ05vRixTQUFTLElBQU1uRiwwQkFBMEI7Z0JBQ3pDbUUsTUFBSztnQkFDTGlCLFVBQVM7Z0JBQ1RyQixXQUFVOzBCQUVWLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNyRixpREFBS0EsQ0FBQzRHLE1BQU07NEJBQUN2QixXQUFVO3NDQUN0Qiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDVTtvREFBSVYsV0FBVTtvREFBcUJXLE1BQUs7b0RBQWVDLFNBQVE7O3NFQUM5RCw4REFBQ0M7NERBQUtDLEdBQUU7Ozs7OztzRUFDUiw4REFBQ0Q7NERBQUtFLFVBQVM7NERBQVVELEdBQUU7NERBQW1LRSxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHM00sOERBQUNqQjs7a0VBQ0MsOERBQUN5Qjt3REFBR3hCLFdBQVU7a0VBQXdDOzs7Ozs7a0VBR3RELDhEQUFDeUI7d0RBQUV6QixXQUFVOzs0REFBd0I7NERBQ2J4RCxjQUFjTyxNQUFNOzREQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUlqRCw4REFBQ2dEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQ2J4RCxjQUFjTyxNQUFNOzs7Ozs7MERBRXZCLDhEQUFDZ0Q7Z0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtyQiw4REFBQ3JGLGlEQUFLQSxDQUFDK0csSUFBSTs0QkFBQzFCLFdBQVU7c0NBQ25CeEQsY0FBY08sTUFBTSxLQUFLLGtCQUN4Qiw4REFBQ2dEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNVOzRDQUFJVixXQUFVOzRDQUEwQlcsTUFBSzs0Q0FBTzZDLFFBQU87NENBQWU1QyxTQUFRO3NEQUNqRiw0RUFBQ0M7Z0RBQUs0QyxlQUFjO2dEQUFRQyxnQkFBZTtnREFBUUMsYUFBYTtnREFBRzdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3pFLDhEQUFDYTt3Q0FBRzNCLFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDeUI7d0NBQUV6QixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7MERBRy9CLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWnhELGNBQWNnQyxHQUFHLENBQUMsQ0FBQ0Y7d0NBaUVUQTt5REFoRVQsOERBQUN5Qjt3Q0FFQ0MsV0FBVTs7MERBR1YsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ1U7b0VBQUlWLFdBQVU7b0VBQXFCVyxNQUFLO29FQUFlQyxTQUFROztzRkFDOUQsOERBQUNDOzRFQUFLQyxHQUFFOzs7Ozs7c0ZBQ1IsOERBQUNEOzRFQUFLRSxVQUFTOzRFQUFVRCxHQUFFOzRFQUFtS0UsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzNNLDhEQUFDakI7O2tGQUNDLDhEQUFDNEI7d0VBQUczQixXQUFVOzs0RUFBdUM7NEVBQ3pDMUIsTUFBTUssT0FBTzs7Ozs7OztrRkFFekIsOERBQUNvQjt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ1U7d0ZBQUlWLFdBQVU7d0ZBQVVXLE1BQUs7d0ZBQWVDLFNBQVE7a0dBQ25ELDRFQUFDQzs0RkFBS0UsVUFBUzs0RkFBVUQsR0FBRTs0RkFBcUhFLFVBQVM7Ozs7Ozs7Ozs7O2tHQUUzSiw4REFBQ2I7a0dBQ0VqRiwrRkFBbUJBLENBQUMsSUFBSTBHLEtBQUt0RCxNQUFNdUQsU0FBUyxHQUFHOzRGQUM5Q0MsV0FBVzs0RkFDWEMsUUFBUTVHLHVEQUFFQTt3RkFDWjs7Ozs7Ozs7Ozs7OzRFQUdIbUQsTUFBTTBELGdCQUFnQixrQkFDckIsOERBQUNqQztnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNVO3dGQUFJVixXQUFVO3dGQUFVVyxNQUFLO3dGQUFlQyxTQUFRO2tHQUNuRCw0RUFBQ0M7NEZBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tHQUVWLDhEQUFDWDtrR0FBTTdCLE1BQU0wRCxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNdkMsOERBQUNqQzt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNiLDRFQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7d0VBQW9EOzs7Ozs7Ozs7Ozs7MEVBSXZFLDhEQUFDeUI7Z0VBQUV6QixXQUFVOzBFQUNWVCxZQUFZakIsTUFBTU8sY0FBYzs7Ozs7OzBFQUVuQyw4REFBQzRDO2dFQUFFekIsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLekMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ1U7b0VBQUlWLFdBQVU7b0VBQXlCVyxNQUFLO29FQUFlQyxTQUFROzhFQUNsRSw0RUFBQ0M7d0VBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR1osOERBQUNvQjtnRUFBR2xDLFdBQVU7MEVBQXNDOzs7Ozs7MEVBQ3BELDhEQUFDRztnRUFBS0gsV0FBVTs7b0VBQ2IxQixFQUFBQSxrQkFBQUEsTUFBTTZELFFBQVEsY0FBZDdELHNDQUFBQSxnQkFBZ0J2QixNQUFNLEtBQUk7b0VBQUU7Ozs7Ozs7Ozs7Ozs7a0VBSWpDLDhEQUFDZ0Q7d0RBQUlDLFdBQVU7a0VBQ1oxQixNQUFNNkQsUUFBUSxJQUFJN0QsTUFBTTZELFFBQVEsQ0FBQ3BGLE1BQU0sR0FBRyxJQUN6Q3VCLE1BQU02RCxRQUFRLENBQUMzRCxHQUFHLENBQUMsQ0FBQzRELE1BQVdDLHNCQUM3Qiw4REFBQ3RDO2dFQUVDQyxXQUFVOzBFQUVWLDRFQUFDRDtvRUFBSUMsV0FBVTs7c0ZBRWIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQ1pvQyxLQUFLRSxPQUFPLENBQUNDLEtBQUssaUJBQ2pCLDhEQUFDQzt3RkFDQ0MsS0FBSy9DLFlBQVkwQyxLQUFLRSxPQUFPLENBQUNDLEtBQUs7d0ZBQ25DRyxLQUFLTixLQUFLRSxPQUFPLENBQUNLLFdBQVc7d0ZBQzdCM0MsV0FBVTt3RkFDVjRDLFNBQVMsQ0FBQ0M7NEZBQ1IsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTs0RkFDdkJBLE9BQU9MLEdBQUcsR0FBRzt3RkFDZjs7Ozs7a0hBR0YsOERBQUMxQzt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ1U7NEZBQUlWLFdBQVU7NEZBQXdCVyxNQUFLOzRGQUFlQyxTQUFRO3NHQUNqRSw0RUFBQ0M7Z0dBQUtFLFVBQVM7Z0dBQVVELEdBQUU7Z0dBQTZGRSxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBTXpJLDhEQUFDakI7b0ZBQUlDLFdBQVU7OEZBQ1pvQyxLQUFLVyxRQUFROzs7Ozs7Ozs7Ozs7c0ZBS2xCLDhEQUFDaEQ7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNnRDs0RkFBR2hELFdBQVU7c0dBQ1hvQyxLQUFLRSxPQUFPLENBQUNLLFdBQVc7Ozs7OztzR0FFM0IsOERBQUM1Qzs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUN5QjtvR0FBRXpCLFdBQVU7OEdBQ1ZULFlBQVk2QyxLQUFLRSxPQUFPLENBQUNXLEtBQUssR0FBR2IsS0FBS1csUUFBUTs7Ozs7OzhHQUVqRCw4REFBQ3RCO29HQUFFekIsV0FBVTs7d0dBQ1ZULFlBQVk2QyxLQUFLRSxPQUFPLENBQUNXLEtBQUs7d0dBQUU7d0dBQUliLEtBQUtXLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0ZBTXZEWCxLQUFLYyxnQkFBZ0IsSUFBSWQsS0FBS2MsZ0JBQWdCLENBQUNuRyxNQUFNLEdBQUcsbUJBQ3ZELDhEQUFDZ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDeUI7NEZBQUV6QixXQUFVO3NHQUF5Qzs7Ozs7O3NHQUN0RCw4REFBQ0Q7NEZBQUlDLFdBQVU7c0dBQ1pvQyxLQUFLYyxnQkFBZ0IsQ0FBQzFFLEdBQUcsQ0FBQyxDQUFDMkUsT0FBWUMsb0JBQ3RDLDhEQUFDakQ7b0dBRUNILFdBQVcsdUVBSVYsT0FIQ21ELE1BQU1FLFFBQVEsR0FDVix3REFDQTs7c0hBR04sOERBQUNsRDs0R0FBS0gsV0FBVyxpQ0FFaEIsT0FEQ21ELE1BQU1FLFFBQVEsR0FBRyxpQkFBaUI7Ozs7Ozt3R0FFbkNGLE1BQU1HLFVBQVUsQ0FBQ0MsY0FBYzt3R0FDL0JKLE1BQU1FLFFBQVEsR0FBRyxPQUFPOzttR0FYcEJEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytEQXREZGY7Ozs7MkZBNEVULDhEQUFDdEM7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDVTtvRUFBSVYsV0FBVTtvRUFBdUNXLE1BQUs7b0VBQU82QyxRQUFPO29FQUFlNUMsU0FBUTs4RUFDOUYsNEVBQUNDO3dFQUFLNEMsZUFBYzt3RUFBUUMsZ0JBQWU7d0VBQVFDLGFBQWE7d0VBQUc3QyxHQUFFOzs7Ozs7Ozs7Ozs4RUFFdkUsOERBQUNXO29FQUFFekIsV0FBVTs4RUFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0F2SjVDMUIsTUFBTUssT0FBTzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FrSzVCLDhEQUFDaEUsaURBQUtBLENBQUNzSixNQUFNOzRCQUFDakUsV0FBVTtzQ0FDdEIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BGLGtEQUFNQTt3Q0FDTHFGLE9BQU07d0NBQ05JLFNBQVNmO3dDQUNUZ0IsVUFBVTdFOzs0Q0FFVEEsMEJBQVksOERBQUNaLG1EQUFPQTtnREFBQ3VGLE1BQUs7Ozs7OzBFQUFVLDhEQUFDbkYsOEdBQVNBO2dEQUFDK0UsV0FBVTs7Ozs7OzRDQUFrQjs7Ozs7OztrREFHOUUsOERBQUNwRixrREFBTUE7d0NBQ0xxRixPQUFNO3dDQUNOSSxTQUFTLElBQU1wRSwwQkFBMEI7a0RBQzFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTZjtHQWo3Qk1aOztRQVNBWCx1RkFBb0JBOzs7S0FUcEJXO0FBbTdCTixpRUFBZUEsd0JBQXdCQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERlc2t0b3BcXFJlc3QgRGVsaXZlcnlcXHJlc3RhdXJhbnRfbWFuZ2VcXHNyY1xcYXBwXFxjb21wb25lbnRzXFxzaGFyZWRcXE9yZGVyTm90aWZpY2F0aW9uT3ZlcmxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VPcmRlck5vdGlmaWNhdGlvbiB9IGZyb20gJ0AvYXBwL2NvbnRleHQvT3JkZXJOb3RpZmljYXRpb25Db250ZXh0JztcbmltcG9ydCB7IE1vZGFsLCBCdXR0b24sIFNwaW5uZXIsIEFsZXJ0IH0gZnJvbSAnZmxvd2JpdGUtcmVhY3QnO1xuaW1wb3J0IHsgSGlFeGNsYW1hdGlvbkNpcmNsZSwgSGlDaGVja0NpcmNsZSwgSGlYQ2lyY2xlLCBIaVJlZnJlc2ggfSBmcm9tICdyZWFjdC1pY29ucy9oaSc7XG5pbXBvcnQgeyBmb3JtYXREaXN0YW5jZVRvTm93IH0gZnJvbSAnZGF0ZS1mbnMnO1xuaW1wb3J0IHsgYXIgfSBmcm9tICdkYXRlLWZucy9sb2NhbGUnO1xuaW1wb3J0IHsgSGlFeGNsYW1hdGlvblRyaWFuZ2xlIH0gZnJvbSAncmVhY3QtaWNvbnMvaGkyJztcblxuaW50ZXJmYWNlIE9yZGVyTm90aWZpY2F0aW9uT3ZlcmxheVByb3BzIHtcbiAgcmVzdGF1cmFudElkOiBzdHJpbmc7XG59XG5cbmNvbnN0IE9yZGVyTm90aWZpY2F0aW9uT3ZlcmxheTogUmVhY3QuRkM8T3JkZXJOb3RpZmljYXRpb25PdmVybGF5UHJvcHM+ID0gKHsgcmVzdGF1cmFudElkIH0pID0+IHtcbiAgY29uc3Qge1xuICAgIHBlbmRpbmdPcmRlcnMsXG4gICAgaXNDb25uZWN0ZWQsXG4gICAgaXNMb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIGFjY2VwdE9yZGVyLFxuICAgIHJlamVjdE9yZGVyLFxuICAgIHJlZnJlc2hPcmRlcnNcbiAgfSA9IHVzZU9yZGVyTm90aWZpY2F0aW9uKCk7XG5cbiAgY29uc3QgW3Nob3dNb2RhbCwgc2V0U2hvd01vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dDdXJyZW50T3JkZXJzTW9kYWwsIHNldFNob3dDdXJyZW50T3JkZXJzTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1JlamVjdENvbmZpcm1Nb2RhbCwgc2V0U2hvd1JlamVjdENvbmZpcm1Nb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtvcmRlclRvUmVqZWN0LCBzZXRPcmRlclRvUmVqZWN0XSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcHJvY2Vzc2luZ09yZGVySWQsIHNldFByb2Nlc3NpbmdPcmRlcklkXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY3VycmVudE9yZGVycywgc2V0Q3VycmVudE9yZGVyc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbcHJlcGFyaW5nT3JkZXJzLCBzZXRQcmVwYXJpbmdPcmRlcnNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcblxuICAvLyBTaG93IG1vZGFsIHdoZW4gdGhlcmUgYXJlIHBlbmRpbmcgb3JkZXJzXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coYE9yZGVyT3ZlcmxheTogUGVuZGluZyBvcmRlcnMgY291bnQgY2hhbmdlZCB0byAke3BlbmRpbmdPcmRlcnMubGVuZ3RofWApO1xuICAgIGlmIChwZW5kaW5nT3JkZXJzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKCdPcmRlck92ZXJsYXk6IFNob3dpbmcgbW9kYWwgZm9yIHBlbmRpbmcgb3JkZXJzJyk7XG4gICAgICBzZXRTaG93TW9kYWwodHJ1ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKCdPcmRlck92ZXJsYXk6IEhpZGluZyBtb2RhbCAtIG5vIHBlbmRpbmcgb3JkZXJzJyk7XG4gICAgICBzZXRTaG93TW9kYWwoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3BlbmRpbmdPcmRlcnMubGVuZ3RoXSk7XG5cbiAgLy8gRmV0Y2ggcHJlcGFyaW5nIG9yZGVycyAoc3RhdHVzID0gMSkgLSBzYW1lIHBhdHRlcm4gYXMgcGVuZGluZyBvcmRlcnNcbiAgY29uc3QgZmV0Y2hQcmVwYXJpbmdPcmRlcnMgPSBSZWFjdC51c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFyZXN0YXVyYW50SWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdPcmRlck92ZXJsYXk6IE5vIHJlc3RhdXJhbnQgSUQgcHJvdmlkZWQgZm9yIHByZXBhcmluZyBvcmRlcnMnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYE9yZGVyT3ZlcmxheTogRmV0Y2hpbmcgcHJlcGFyaW5nIG9yZGVycyBmb3IgcmVzdGF1cmFudCAke3Jlc3RhdXJhbnRJZH1gKTtcblxuICAgICAgY29uc3QgYWNjZXNzVG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWNjZXNzVG9rZW4nKTtcbiAgICAgIGlmICghYWNjZXNzVG9rZW4pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBhY2Nlc3MgdG9rZW4gZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgYXBpVXJsID0gYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vb3JkZXJzL3Jlc3RhdXJhbnQvJHtyZXN0YXVyYW50SWR9P3BhZ2U9MSZzaXplPTUwYDtcbiAgICAgIGNvbnNvbGUubG9nKGBPcmRlck92ZXJsYXk6IE1ha2luZyByZXF1ZXN0IHRvICR7YXBpVXJsfWApO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGFwaVVybCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGBPcmRlck92ZXJsYXk6IFJlc3BvbnNlIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXNwb25zZURhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zb2xlLmxvZyhgT3JkZXJPdmVybGF5OiBSZWNlaXZlZCByZXNwb25zZSBmb3IgcHJlcGFyaW5nIG9yZGVyczpgLCByZXNwb25zZURhdGEpO1xuXG4gICAgICAvLyBIYW5kbGUgcGFnaW5hdGVkIHJlc3BvbnNlIGZvcm1hdCAtIHNhbWUgYXMgcGVuZGluZyBvcmRlcnNcbiAgICAgIGNvbnN0IG9yZGVyczogYW55W10gPSByZXNwb25zZURhdGEuaXRlbXMgfHwgcmVzcG9uc2VEYXRhLmRhdGEgfHwgW107XG4gICAgICBjb25zb2xlLmxvZyhgT3JkZXJPdmVybGF5OiBSZWNlaXZlZCAke29yZGVycy5sZW5ndGh9IHRvdGFsIG9yZGVyc2ApO1xuXG4gICAgICAvLyBGaWx0ZXIgb25seSBwcmVwYXJpbmcgb3JkZXJzIChTdGF0dXMgPSAxKVxuICAgICAgY29uc3QgcHJlcGFyaW5nID0gb3JkZXJzLmZpbHRlcihvcmRlciA9PiBvcmRlci5TdGF0dXMgPT09IDEpO1xuICAgICAgY29uc29sZS5sb2coYE9yZGVyT3ZlcmxheTogRm91bmQgJHtwcmVwYXJpbmcubGVuZ3RofSBwcmVwYXJpbmcgb3JkZXJzYCk7XG5cbiAgICAgIHNldFByZXBhcmluZ09yZGVycyhwcmVwYXJpbmcpO1xuXG4gICAgICAvLyBMb2cgcHJlcGFyaW5nIG9yZGVycyBmb3IgZGVidWdnaW5nXG4gICAgICBpZiAocHJlcGFyaW5nLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ09yZGVyT3ZlcmxheTogUHJlcGFyaW5nIG9yZGVyczonLCBwcmVwYXJpbmcubWFwKG8gPT4gKHtcbiAgICAgICAgICBpZDogby5PcmRlcklELFxuICAgICAgICAgIHN0YXR1czogby5TdGF0dXMsXG4gICAgICAgICAgdG90YWw6IG8uQ2FydFRvdGFsUHJpY2VcbiAgICAgICAgfSkpKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ09yZGVyT3ZlcmxheTogRXJyb3IgZmV0Y2hpbmcgcHJlcGFyaW5nIG9yZGVyczonLCBlcnIpO1xuICAgIH1cbiAgfSwgW3Jlc3RhdXJhbnRJZF0pO1xuXG4gIC8vIEZldGNoIHByZXBhcmluZyBvcmRlcnMgcGVyaW9kaWNhbGx5IC0gc2FtZSBwYXR0ZXJuIGFzIHBlbmRpbmcgb3JkZXJzXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hQcmVwYXJpbmdPcmRlcnMoKTsgLy8gSW5pdGlhbCBmZXRjaFxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoZmV0Y2hQcmVwYXJpbmdPcmRlcnMsIDE1MDAwKTsgLy8gVXBkYXRlIGV2ZXJ5IDE1IHNlY29uZHNcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW2ZldGNoUHJlcGFyaW5nT3JkZXJzXSk7XG5cbiAgY29uc3QgaGFuZGxlQWNjZXB0T3JkZXIgPSBhc3luYyAob3JkZXJJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFByb2Nlc3NpbmdPcmRlcklkKG9yZGVySWQpO1xuICAgICAgYXdhaXQgYWNjZXB0T3JkZXIob3JkZXJJZCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gYWNjZXB0IG9yZGVyOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFByb2Nlc3NpbmdPcmRlcklkKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZWplY3RPcmRlciA9IGFzeW5jIChvcmRlcklkOiBudW1iZXIpID0+IHtcbiAgICBzZXRPcmRlclRvUmVqZWN0KG9yZGVySWQpO1xuICAgIHNldFNob3dSZWplY3RDb25maXJtTW9kYWwodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgY29uZmlybVJlamVjdE9yZGVyID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghb3JkZXJUb1JlamVjdCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldFByb2Nlc3NpbmdPcmRlcklkKG9yZGVyVG9SZWplY3QpO1xuICAgICAgYXdhaXQgcmVqZWN0T3JkZXIob3JkZXJUb1JlamVjdCk7XG4gICAgICBzZXRTaG93UmVqZWN0Q29uZmlybU1vZGFsKGZhbHNlKTtcbiAgICAgIHNldE9yZGVyVG9SZWplY3QobnVsbCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcmVqZWN0IG9yZGVyOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFByb2Nlc3NpbmdPcmRlcklkKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaEN1cnJlbnRPcmRlcnMgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0U2hvd0N1cnJlbnRPcmRlcnNNb2RhbCh0cnVlKTtcbiAgICAvLyBVc2UgdGhlIGFscmVhZHkgZmV0Y2hlZCBwcmVwYXJpbmcgb3JkZXJzXG4gICAgc2V0Q3VycmVudE9yZGVycyhwcmVwYXJpbmdPcmRlcnMpO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFByaWNlID0gKHByaWNlOiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gYCR7cHJpY2UudG9GaXhlZCgyKX0g2K8u2YRgO1xuICB9O1xuXG4gIGNvbnN0IGdldEltYWdlVXJsID0gKGltYWdlVXJsPzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFpbWFnZVVybCkgcmV0dXJuICcvaW1hZ2VzL3BsYWNlaG9sZGVyLWZvb2QuanBnJztcbiAgICBpZiAoaW1hZ2VVcmwuc3RhcnRzV2l0aCgnaHR0cCcpKSByZXR1cm4gaW1hZ2VVcmw7XG4gICAgcmV0dXJuIGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9LyR7aW1hZ2VVcmwucmVwbGFjZSgvXlxcLysvLCAnJyl9YDtcbiAgfTtcblxuICAvLyBDb25uZWN0aW9uIHN0YXR1cyBpbmRpY2F0b3JcbiAgY29uc3QgQ29ubmVjdGlvblN0YXR1cyA9ICgpID0+IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC00IGxlZnQtNCB6LTUwXCI+XG4gICAgICB7IWlzQ29ubmVjdGVkICYmIChcbiAgICAgICAgPEFsZXJ0IGNvbG9yPVwiZmFpbHVyZVwiIGljb249e0hpRXhjbGFtYXRpb25UcmlhbmdsZX0gY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8c3Bhbj7ZgdmC2K/Yp9mGINin2YTYp9iq2LXYp9mEINio2KfZhNiu2KfYr9mFPC9zcGFuPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBzaXplPVwieHNcIlxuICAgICAgICAgICAgICBjb2xvcj1cImZhaWx1cmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtyZWZyZXNoT3JkZXJzfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gPFNwaW5uZXIgc2l6ZT1cInNtXCIgLz4gOiA8SGlSZWZyZXNoIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcblxuICAvLyBPcmRlciBub3RpZmljYXRpb24gYmFkZ2UgLSBNaW5kLWJsb3dpbmcgZGVzaWduXG4gIGNvbnN0IE5vdGlmaWNhdGlvbkJhZGdlID0gKCkgPT4ge1xuICAgIGlmIChwZW5kaW5nT3JkZXJzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNiByaWdodC02IHotNTBcIj5cbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tb3JhbmdlLTUwMCB2aWEtcmVkLTUwMCB0by1waW5rLTYwMCBob3Zlcjpmcm9tLW9yYW5nZS02MDAgaG92ZXI6dmlhLXJlZC02MDAgaG92ZXI6dG8tcGluay03MDAgdGV4dC13aGl0ZSBweC04IHB5LTYgcm91bmRlZC0zeGwgc2hhZG93LTJ4bCBjdXJzb3ItcG9pbnRlciB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBib3JkZXItMiBib3JkZXItd2hpdGUvMjAgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vZGFsKHRydWUpfVxuICAgICAgICA+XG4gICAgICAgICAgey8qIEFuaW1hdGVkIGJhY2tncm91bmQgZ2xvdyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1vcmFuZ2UtNDAwIHZpYS1yZWQtNDAwIHRvLXBpbmstNTAwIHJvdW5kZWQtM3hsIGJsdXIteGwgb3BhY2l0eS03NSBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG5cbiAgICAgICAgICB7LyogQ29udGVudCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICB7LyogSWNvbiB3aXRoIGFuaW1hdGVkIHJpbmcgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMzBcIj5cbiAgICAgICAgICAgICAgICA8SGlFeGNsYW1hdGlvblRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNyBoLTcgdGV4dC13aGl0ZSBhbmltYXRlLWJvdW5jZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7LyogQW5pbWF0ZWQgcmluZyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHctMTIgaC0xMiBib3JkZXItMiBib3JkZXItd2hpdGUvNTAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUZXh0IGNvbnRlbnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LWJsYWNrIHRleHQteGwgdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICAgICAg2LfZhNio2KfYqiDYrNiv2YrYr9ipINi52KfYrNmE2KlcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXllbGxvdy0zMDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugc2hhZG93LWxnXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzkwIHRleHQtc20gZm9udC1zZW1pYm9sZFwiPlxuICAgICAgICAgICAgICAgIHtwZW5kaW5nT3JkZXJzLmxlbmd0aH0g2LfZhNioINmK2K3Yqtin2Kwg2YXZiNin2YHZgtipINmB2YjYsdmK2KlcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb3VudCBiYWRnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE0IGgtMTQgYmctd2hpdGUvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyLTIgYm9yZGVyLXdoaXRlLzQwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ibGFjayB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICB7cGVuZGluZ09yZGVycy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgey8qIFB1bHNpbmcgZWZmZWN0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy0xNCBoLTE0IGJnLXdoaXRlLzEwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXBpbmdcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZsb2F0aW5nIHBhcnRpY2xlcyBlZmZlY3QgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIHctMSBoLTEgYmctd2hpdGUgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZyBvcGFjaXR5LTYwXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMyBsZWZ0LTMgdy0xIGgtMSBiZy15ZWxsb3ctMzAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIG9wYWNpdHktODBcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzIgbGVmdC0yIHctMC41IGgtMC41IGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZSBvcGFjaXR5LTcwXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICAvLyBDdXJyZW50IE9yZGVycyBCdXR0b24gLSBNaW5kLWJsb3dpbmcgZGVzaWduXG4gIGNvbnN0IEN1cnJlbnRPcmRlcnNCdXR0b24gPSAoKSA9PiB7XG4gICAgaWYgKHByZXBhcmluZ09yZGVycy5sZW5ndGggPT09IDApIHJldHVybiBudWxsO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTYgbGVmdC02IHotNTBcIj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoQ3VycmVudE9yZGVyc31cbiAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ncmFkaWVudC10by1iciBmcm9tLWVtZXJhbGQtNTAwIHZpYS10ZWFsLTUwMCB0by1jeWFuLTYwMCBob3Zlcjpmcm9tLWVtZXJhbGQtNjAwIGhvdmVyOnZpYS10ZWFsLTYwMCBob3Zlcjp0by1jeWFuLTcwMCB0ZXh0LXdoaXRlIHB4LTggcHktNiByb3VuZGVkLTN4bCBzaGFkb3ctMnhsIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGJvcmRlci0yIGJvcmRlci13aGl0ZS8yMCBiYWNrZHJvcC1ibHVyLXNtIGdyb3VwXCJcbiAgICAgICAgPlxuICAgICAgICAgIHsvKiBBbmltYXRlZCBiYWNrZ3JvdW5kIGdsb3cgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZW1lcmFsZC00MDAgdmlhLXRlYWwtNDAwIHRvLWN5YW4tNTAwIHJvdW5kZWQtM3hsIGJsdXIteGwgb3BhY2l0eS03NSBncm91cC1ob3ZlcjpvcGFjaXR5LTkwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDBcIj48L2Rpdj5cblxuICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIHsvKiBJY29uIHdpdGggYW5pbWF0ZWQgcm90YXRpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMzAgZ3JvdXAtaG92ZXI6cm90YXRlLTEyIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy03IGgtNyB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOSAyYTEgMSAwIDAwMCAyaDJhMSAxIDAgMTAwLTJIOXpcIiAvPlxuICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk00IDVhMiAyIDAgMDEyLTJ2MWExIDEgMCAwMDEgMWg2YTEgMSAwIDAwMS0xVjNhMiAyIDAgMDEyIDJ2NmEyIDIgMCAwMS0yIDJINmEyIDIgMCAwMS0yLTJWNXptMyA0YTEgMSAwIDAwMCAyaC4wMWExIDEgMCAxMDAtMkg3em0zIDBhMSAxIDAgMDAwIDJoM2ExIDEgMCAxMDAtMmgtM3pcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgey8qIFJvdGF0aW5nIHJpbmcgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LTEyIGgtMTIgYm9yZGVyLTIgYm9yZGVyLXdoaXRlLzMwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gb3BhY2l0eS01MFwiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUZXh0IGNvbnRlbnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LWJsYWNrIHRleHQteGwgdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICAgICAg2KfZhNi32YTYqNin2Kog2KfZhNit2KfZhNmK2KlcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTMwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBzaGFkb3ctbGdcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvOTAgdGV4dC1zbSBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAge3ByZXBhcmluZ09yZGVycy5sZW5ndGh9INi32YTYqCDZgtmK2K8g2KfZhNiq2K3YttmK2LFcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb3VudCBiYWRnZSB3aXRoIGNvb2tpbmcgYW5pbWF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTQgaC0xNCBiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXItMiBib3JkZXItd2hpdGUvNDAgZ3JvdXAtaG92ZXI6Ymctd2hpdGUvMzAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ibGFjayB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICB7cHJlcGFyaW5nT3JkZXJzLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7LyogU3RlYW0gZWZmZWN0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMSBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMyBiZy13aGl0ZS80MCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2Ugb3BhY2l0eS02MFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgdHJhbnNsYXRlLXgtMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0wLjUgaC0yIGJnLXdoaXRlLzMwIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZSBvcGFjaXR5LTQwXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuMnMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMiBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiAtdHJhbnNsYXRlLXgtMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0wLjUgaC0yIGJnLXdoaXRlLzMwIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZSBvcGFjaXR5LTQwXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuNHMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRmxvYXRpbmcgc3BhcmtsZXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIHctMSBoLTEgYmctd2hpdGUgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZyBvcGFjaXR5LTYwXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMyBsZWZ0LTMgdy0xIGgtMSBiZy1ncmVlbi0zMDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugb3BhY2l0eS04MFwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvMiByaWdodC0yIHctMC41IGgtMC41IGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZSBvcGFjaXR5LTcwXCI+PC9kaXY+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8Q29ubmVjdGlvblN0YXR1cyAvPlxuICAgICAgPE5vdGlmaWNhdGlvbkJhZGdlIC8+XG4gICAgICA8Q3VycmVudE9yZGVyc0J1dHRvbiAvPlxuXG4gICAgICB7LyogTmV3IE9yZGVycyBNb2RhbCAtIEFwcGxlLXN0eWxlIENsZWFuIERlc2lnbiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICBzaG93PXtzaG93TW9kYWx9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dNb2RhbChmYWxzZSl9XG4gICAgICAgIHNpemU9XCI0eGxcIlxuICAgICAgICBwb3NpdGlvbj1cImNlbnRlclwiXG4gICAgICAgIGRpc21pc3NpYmxlPXtmYWxzZX1cbiAgICAgICAgY2xhc3NOYW1lPVwiYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLTAgcm91bmRlZC0yeGwgc2hhZG93LTJ4bCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICB7LyogQ2xlYW4gSGVhZGVyICovfVxuICAgICAgICAgIDxNb2RhbC5IZWFkZXIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGJnLXdoaXRlIHJvdW5kZWQtdC0yeGwgcHgtOCBweS02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHsvKiBTaW1wbGUsIGNsZWFuIGljb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctb3JhbmdlLTUwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1zbVwiPlxuICAgICAgICAgICAgICAgICAgPEhpRXhjbGFtYXRpb25UcmlhbmdsZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENsZWFuIHR5cG9ncmFwaHkgKi99XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICDYt9mE2KjYp9iqINis2K/Zitiv2KlcbiAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3BlbmRpbmdPcmRlcnMubGVuZ3RofSDYt9mE2Kgg2YrYrdiq2KfYrCDYpdmE2Ykg2YXZiNin2YHZgtipXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTaW1wbGUgY291bnQgYmFkZ2UgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLW9yYW5nZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1zbVwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICB7cGVuZGluZ09yZGVycy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTW9kYWwuSGVhZGVyPlxuXG4gICAgICAgICAgey8qIENsZWFuIE1vZGFsIEJvZHkgKi99XG4gICAgICAgICAgPE1vZGFsLkJvZHkgY2xhc3NOYW1lPVwibWF4LWgtWzc1dmhdIG92ZXJmbG93LXktYXV0byBiZy1ncmF5LTUwIHAtNlwiPlxuICAgICAgICAgIHtwZW5kaW5nT3JkZXJzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgPEhpQ2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JlZW4tNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMFwiPtmE2Kcg2KrZiNis2K8g2LfZhNio2KfYqiDZgdmKINin2YTYp9mG2KrYuNin2LE8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge3BlbmRpbmdPcmRlcnMubWFwKChvcmRlcikgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17b3JkZXIuT3JkZXJJRH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgcC02IHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHsvKiBDbGVhbiBPcmRlciBIZWFkZXIgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIFNpbXBsZSBpY29uICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLW9yYW5nZS0xMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtb3JhbmdlLTYwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOSAyYTEgMSAwIDAwMCAyaDJhMSAxIDAgMTAwLTJIOXpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTQgNWEyIDIgMCAwMTItMnYxYTEgMSAwIDAwMSAxaDZhMSAxIDAgMDAxLTFWM2EyIDIgMCAwMTIgMnY2YTIgMiAwIDAxLTIgMkg2YTIgMiAwIDAxLTItMlY1em0zIDRhMSAxIDAgMDAwIDJoLjAxYTEgMSAwIDEwMC0ySDd6bTMgMGExIDEgMCAwMDAgMmgzYTEgMSAwIDEwMC0yaC0zelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBDbGVhbiB0ZXh0IGNvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgINi32YTYqCDYsdmC2YUgI3tvcmRlci5PcmRlcklEfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTEtMTJhMSAxIDAgMTAtMiAwdjRhMSAxIDAgMDAuMjkzLjcwN2wyLjgyOCAyLjgyOWExIDEgMCAxMDEuNDE1LTEuNDE1TDExIDkuNTg2VjZ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREaXN0YW5jZVRvTm93KG5ldyBEYXRlKG9yZGVyLk9yZGVyRGF0ZSksIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkU3VmZml4OiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbGU6IGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5DdXN0b21lclBob25lTnVtICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMiAzYTEgMSAwIDAxMS0xaDIuMTUzYTEgMSAwIDAxLjk4Ni44MzZsLjc0IDQuNDM1YTEgMSAwIDAxLS41NCAxLjA2bC0xLjU0OC43NzNhMTEuMDM3IDExLjAzNyAwIDAwNi4xMDUgNi4xMDVsLjc3NC0xLjU0OGExIDEgMCAwMTEuMDU5LS41NGw0LjQzNS43NGExIDEgMCAwMS44MzYuOTg2VjE3YTEgMSAwIDAxLTEgMWgtMkM3LjgyIDE4IDIgMTIuMTggMiA1VjN6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e29yZGVyLkN1c3RvbWVyUGhvbmVOdW19PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBDbGVhbiBzdGF0dXMgYW5kIHByaWNlIHNlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1lbmQgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwIHB4LTMgcHktMSByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgINmB2Yog2KfZhNin2YbYqti42KfYsVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKG9yZGVyLkNhcnRUb3RhbFByaWNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtin2YTZhdis2YXZiNi5INin2YTZg9mE2Yo8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cblxuXG4gICAgICAgICAgICAgICAgICB7b3JkZXIuTm90ZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPtmF2YTYp9it2LjYp9iqOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgYmctZ3JheS01MCBwLTIgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLk5vdGV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBPcmRlciBJdGVtcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTMgdGV4dC1ibHVlLTYwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMyA0YTEgMSAwIDAxMS0xaDEyYTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xVjR6TTMgMTBhMSAxIDAgMDExLTFoNmExIDEgMCAwMTEgMXY2YTEgMSAwIDAxLTEgMUg0YTEgMSAwIDAxLTEtMXYtNnpNMTQgOWExIDEgMCAwMC0xIDF2NmExIDEgMCAwMDEgMWgyYTEgMSAwIDAwMS0xdi02YTEgMSAwIDAwLTEtMWgtMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+2KfZhNmF2YbYqtis2KfYqiDYp9mE2YXYt9mE2YjYqNipPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHRleHQteHMgZm9udC1tZWRpdW0gcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLlByb2R1Y3RzPy5sZW5ndGggfHwgMH0g2YXZhtiq2KxcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge29yZGVyLlByb2R1Y3RzICYmIG9yZGVyLlByb2R1Y3RzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICBvcmRlci5Qcm9kdWN0cy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0xMDAgcm91bmRlZC14bCBwLTQgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvd1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQcm9kdWN0IEltYWdlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiByb3VuZGVkLXhsIG92ZXJmbG93LWhpZGRlbiBiZy1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLlByb2R1Y3QuSW1hZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17Z2V0SW1hZ2VVcmwoaXRlbS5Qcm9kdWN0LkltYWdlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtpdGVtLlByb2R1Y3QuUHJvZHVjdE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zcmMgPSAnL2ltYWdlcy9wbGFjZWhvbGRlci1mb29kLmpwZyc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JheS00MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCAzYTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY1YTIgMiAwIDAwLTItMkg0em0xMiAxMkg0bDQtOCAzIDYgMi00IDMgNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUXVhbnRpdHkgQmFkZ2UgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1yaWdodC0yIGJnLW9yYW5nZS01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIGZvbnQtYm9sZCByb3VuZGVkLWZ1bGwgdy02IGgtNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLlF1YW50aXR5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUHJvZHVjdCBEZXRhaWxzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5Qcm9kdWN0LlByb2R1Y3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IG1sLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShpdGVtLlByb2R1Y3QuUHJpY2UgKiBpdGVtLlF1YW50aXR5KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2UoaXRlbS5Qcm9kdWN0LlByaWNlKX0gw5cge2l0ZW0uUXVhbnRpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBJbmdyZWRpZW50cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uSW5ncmVkaWVudFVzYWdlcyAmJiBpdGVtLkluZ3JlZGllbnRVc2FnZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPtin2YTZhdmD2YjZhtin2Kog2KfZhNmF2K7Yqtin2LHYqTo8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLkluZ3JlZGllbnRVc2FnZXMubWFwKCh1c2FnZSwgaWR4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aWR4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2FnZS5Jc05lZWRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCBib3JkZXIgYm9yZGVyLXJlZC0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B3LTEuNSBoLTEuNSByb3VuZGVkLWZ1bGwgbXItMSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2FnZS5Jc05lZWRlZCA/ICdiZy1ncmVlbi01MDAnIDogJ2JnLXJlZC01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzYWdlLkluZ3JlZGllbnQuSW5ncmVkaWVudE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNhZ2UuSXNOZWVkZWQgPyAnIOKckycgOiAnIOKclyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IGJnLWdyYXktNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItM1wiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0yMCAxM1Y2YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnY3bTE2IDB2NWEyIDIgMCAwMS0yIDJINmEyIDIgMCAwMS0yLTJ2LTVtMTYgMGgtMk00IDEzaDJtMTMtOFY0YTEgMSAwIDAwLTEtMUg3YTEgMSAwIDAwLTEgMXYxbTggMFY0LjVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBmb250LW1lZGl1bVwiPtmE2Kcg2KrZiNis2K8g2YXZhtiq2KzYp9iqINmB2Yog2YfYsNinINin2YTYt9mE2Kg8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQWRkaXRpb25hbCBPcmRlciBJbmZvICovfVxuICAgICAgICAgICAgICAgICAgeyhvcmRlci5SZXN0YXVyYW50TmFtZSB8fCBvcmRlci5Ecml2ZXJGaXJzdE5hbWUgfHwgb3JkZXIuRHJpdmVyTGFzdE5hbWUgfHwgb3JkZXIuRGlzdGFuY2VJbk1ldGVycykgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy01IGgtNSBiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQtZ3JheS02MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTggMTBhOCA4IDAgMTEtMTYgMCA4IDggMCAwMTE2IDB6bS03LTRhMSAxIDAgMTEtMiAwIDEgMSAwIDAxMiAwek05IDlhMSAxIDAgMDAwIDJ2M2ExIDEgMCAwMDEgMWgxYTEgMSAwIDEwMC0ydi0zYTEgMSAwIDAwLTEtMUg5elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+2YXYudmE2YjZhdin2Kog2KXYttin2YHZitipPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmF5LTUwIHRvLWJsdWUtNTAgcm91bmRlZC14bCBwLTQgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7b3JkZXIuUmVzdGF1cmFudE5hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNjAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMC4zOTQgMi4wOGExIDEgMCAwMC0uNzg4IDBsLTcgM2ExIDEgMCAwMDAgMS44NEw1LjI1IDguMDUxYS45OTkuOTk5IDAgMDEuMzU2LS4yNTdsNC0xLjcxNGExIDEgMCAxMS43ODggMS44MzhMNy42NjcgOS4wODhsMS45NC44MzFhMSAxIDAgMDAuNzg3IDBsNy0zYTEgMSAwIDAwMC0xLjgzOGwtNy0zek0zLjMxIDkuMzk3TDUgMTAuMTJ2NC4xMDJhOC45NjkgOC45NjkgMCAwMC0xLjA1LS4xNzQgMSAxIDAgMDEtLjg5LS44OSAxMS4xMTUgMTEuMTE1IDAgMDEuMjUtMy43NjJ6TTkuMyAxNi41NzNBOS4wMjYgOS4wMjYgMCAwMDcgMTQuOTM1di0zLjk1N2wxLjgxOC43OGEzIDMgMCAwMDIuMzY0IDBsNS41MDgtMi4zNjFhMTEuMDI2IDExLjAyNiAwIDAxLjI1IDMuNzYyIDEgMSAwIDAxLS44OS44OSA4Ljk2OCA4Ljk2OCAwIDAwLTUuMzUgMi41MjQgMSAxIDAgMDEtMS40IDB6TTYgMThhMSAxIDAgMDAxLTF2LTIuMDY1YTguOTM1IDguOTM1IDAgMDAtMi0uNzEyVjE3YTEgMSAwIDAwMSAxelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtin2YTZhdi32LnZhTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5SZXN0YXVyYW50TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHsob3JkZXIuRHJpdmVyRmlyc3ROYW1lIHx8IG9yZGVyLkRyaXZlckxhc3ROYW1lKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTggOWEzIDMgMCAxMDAtNiAzIDMgMCAwMDAgNnpNOCAxMWE2IDYgMCAwMTYgNkgyYTYgNiAwIDAxNi02ek0xNiA3YTEgMSAwIDEwLTIgMHYxaC0xYTEgMSAwIDEwMCAyaDF2MWExIDEgMCAxMDIgMHYtMWgxYTEgMSAwIDEwMC0yaC0xVjd6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2KfZhNiz2KfYptmCOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Ake29yZGVyLkRyaXZlckZpcnN0TmFtZSB8fCAnJ30gJHtvcmRlci5Ecml2ZXJMYXN0TmFtZSB8fCAnJ31gLnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5EaXN0YW5jZUluTWV0ZXJzICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNS4wNSA0LjA1YTcgNyAwIDExOS45IDkuOUwxMCAxOC45bC00Ljk1LTQuOTVhNyA3IDAgMDEwLTkuOXpNMTAgMTFhMiAyIDAgMTAwLTQgMiAyIDAgMDAwIDR6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2KfZhNmF2LPYp9mB2Kk6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KG9yZGVyLkRpc3RhbmNlSW5NZXRlcnMgLyAxMDAwKS50b0ZpeGVkKDIpfSDZg9mFXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgey8qIENsZWFuIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBBY2NlcHQgQnV0dG9uICovfVxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWNjZXB0T3JkZXIob3JkZXIuT3JkZXJJRCl9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Byb2Nlc3NpbmdPcmRlcklkID09PSBvcmRlci5PcmRlcklEfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcHktMyBweC02IHJvdW5kZWQtbGcgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3Byb2Nlc3NpbmdPcmRlcklkID09PSBvcmRlci5PcmRlcklEID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U3Bpbm5lciBzaXplPVwic21cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7YrNin2LHZiiDYp9mE2YXYudin2YTYrNipLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEhpQ2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPtmC2KjZiNmEINmI2KjYr9ihINin2YTYqtit2LbZitixPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFJlamVjdCBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZWplY3RPcmRlcihvcmRlci5PcmRlcklEKX1cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17cHJvY2Vzc2luZ09yZGVySWQgPT09IG9yZGVyLk9yZGVySUR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXJlZC01MDAgaG92ZXI6YmctcmVkLTYwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHB5LTMgcHgtNiByb3VuZGVkLWxnIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9jZXNzaW5nT3JkZXJJZCA9PT0gb3JkZXIuT3JkZXJJRCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNwaW5uZXIgc2l6ZT1cInNtXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+2KzYp9ix2Yog2KfZhNmF2LnYp9mE2KzYqS4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxIaVhDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPtix2YHYtiDYp9mE2LfZhNio2YrYqTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9Nb2RhbC5Cb2R5PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ2xlYW4gTW9kYWwgRm9vdGVyICovfVxuICAgICAgICA8TW9kYWwuRm9vdGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWItMnhsIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHctZnVsbFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtyZWZyZXNoT3JkZXJzfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gPFNwaW5uZXIgc2l6ZT1cInNtXCIgLz4gOiA8SGlSZWZyZXNoIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgICAg2KrYrdiv2YrYq1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2RhbChmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMiBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCB0ZXh0LWdyYXktNzAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg2KXYutmE2KfZglxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvTW9kYWwuRm9vdGVyPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIFJlamVjdGlvbiBDb25maXJtYXRpb24gTW9kYWwgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgc2hvdz17c2hvd1JlamVjdENvbmZpcm1Nb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1JlamVjdENvbmZpcm1Nb2RhbChmYWxzZSl9XG4gICAgICAgIHNpemU9XCJtZFwiXG4gICAgICAgIHBvc2l0aW9uPVwiY2VudGVyXCJcbiAgICAgICAgY2xhc3NOYW1lPVwiYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgIDxNb2RhbC5IZWFkZXIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNTAgdG8tcmVkLTEwMCByb3VuZGVkLXQtMnhsXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXJlZC01MDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxIaVhDaXJjbGUgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtcmVkLTkwMFwiPtiq2KPZg9mK2K8g2LHZgdi2INin2YTYt9mE2KjZitipPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTcwMFwiPtmH2LDYpyDYp9mE2KXYrNix2KfYoSDZhNinINmK2YXZg9mGINin2YTYqtix2KfYrNi5INi52YbZhzwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L01vZGFsLkhlYWRlcj5cblxuICAgICAgICAgIDxNb2RhbC5Cb2R5IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1yZWQtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8SGlFeGNsYW1hdGlvblRyaWFuZ2xlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAg2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINix2YHYtiDZh9iw2Ycg2KfZhNi32YTYqNmK2KnYn1xuICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICDYs9mK2KrZhSDYpdi02LnYp9ixINin2YTYudmF2YrZhCDYqNix2YHYtiDYp9mE2LfZhNio2YrYqSDZiNmE2YYg2KrYqtmF2YPZhiDZhdmGINmC2KjZiNmE2YfYpyDZhdix2Kkg2KPYrtix2YlcbiAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvbmZpcm1SZWplY3RPcmRlcn1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9jZXNzaW5nT3JkZXJJZCAhPT0gbnVsbH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNTAwIHRvLXJlZC02MDAgaG92ZXI6ZnJvbS1yZWQtNjAwIGhvdmVyOnRvLXJlZC03MDAgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHB5LTMgcHgtNiByb3VuZGVkLXhsIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6dHJhbnNmb3JtLW5vbmVcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtwcm9jZXNzaW5nT3JkZXJJZCA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxTcGlubmVyIHNpemU9XCJzbVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+2KzYp9ix2Yog2KfZhNix2YHYti4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEhpWENpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7Zhti52YXYjCDYsdmB2LYg2KfZhNi32YTYqNmK2Kk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dSZWplY3RDb25maXJtTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgdGV4dC1ncmF5LTcwMCBmb250LXNlbWlib2xkIHB5LTMgcHgtNiByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTW9kYWwuQm9keT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L01vZGFsPlxuXG4gICAgICB7LyogQ3VycmVudCBPcmRlcnMgTW9kYWwgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgc2hvdz17c2hvd0N1cnJlbnRPcmRlcnNNb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0N1cnJlbnRPcmRlcnNNb2RhbChmYWxzZSl9XG4gICAgICAgIHNpemU9XCI1eGxcIlxuICAgICAgICBwb3NpdGlvbj1cImNlbnRlclwiXG4gICAgICAgIGNsYXNzTmFtZT1cImJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tNTAgYm9yZGVyLTAgcm91bmRlZC0yeGwgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgIDxNb2RhbC5IZWFkZXIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGJnLXdoaXRlIHJvdW5kZWQtdC0yeGxcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgdy1mdWxsXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLWluZGlnby02MDAgcm91bmRlZC0yeGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk05IDJhMSAxIDAgMDAwIDJoMmExIDEgMCAxMDAtMkg5elwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCA1YTIgMiAwIDAxMi0ydjFhMSAxIDAgMDAxIDFoNmExIDEgMCAwMDEtMVYzYTIgMiAwIDAxMiAydjZhMiAyIDAgMDEtMiAySDZhMiAyIDAgMDEtMi0yVjV6bTMgNGExIDEgMCAwMDAgMmguMDFhMSAxIDAgMTAwLTJIN3ptMyAwYTEgMSAwIDAwMCAyaDNhMSAxIDAgMTAwLTJoLTN6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICDYp9mE2LfZhNio2KfYqiDYp9mE2K3Yp9mE2YrYqVxuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICDYp9mE2LfZhNio2KfYqiDZgtmK2K8g2KfZhNiq2K3YttmK2LEgKHtjdXJyZW50T3JkZXJzLmxlbmd0aH0g2LfZhNioKVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTQwMCB0by1pbmRpZ28tNTAwIHRleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgcHgtNSBweS0zIHJvdW5kZWQtZnVsbCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIHtjdXJyZW50T3JkZXJzLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZ1wiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTW9kYWwuSGVhZGVyPlxuXG4gICAgICAgICAgPE1vZGFsLkJvZHkgY2xhc3NOYW1lPVwibWF4LWgtWzcwdmhdIG92ZXJmbG93LXktYXV0byBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyYXktNTAgdG8td2hpdGUgcC02XCI+XG4gICAgICAgICAgICB7Y3VycmVudE9yZGVycy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjQgaC0yNCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWJsdWUtNTAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05IDVIN2EyIDIgMCAwMC0yIDJ2MTBhMiAyIDAgMDAyIDJoOGEyIDIgMCAwMDItMlY3YTIgMiAwIDAwLTItMmgtMk05IDVhMiAyIDAgMDAyIDJoMmEyIDIgMCAwMDItMk05IDVhMiAyIDAgMDEyLTJoMmEyIDIgMCAwMTIgMlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPtmE2Kcg2KrZiNis2K8g2LfZhNio2KfYqiDZgtmK2K8g2KfZhNiq2K3YttmK2LE8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7YrNmF2YrYuSDYp9mE2LfZhNio2KfYqiDYqtmFINiq2K3YttmK2LHZh9inINij2Ygg2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqINmF2YLYqNmI2YTYqSDYrdin2YTZitin2Ys8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICB7Y3VycmVudE9yZGVycy5tYXAoKG9yZGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17b3JkZXIuT3JkZXJJRH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgcC02IHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLWdyYXktMTAwIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgey8qIE9yZGVyIEhlYWRlciAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwMCB0by1ncmVlbi02MDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk05IDJhMSAxIDAgMDAwIDJoMmExIDEgMCAxMDAtMkg5elwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk00IDVhMiAyIDAgMDEyLTJ2MWExIDEgMCAwMDEgMWg2YTEgMSAwIDAwMS0xVjNhMiAyIDAgMDEyIDJ2NmEyIDIgMCAwMS0yIDJINmEyIDIgMCAwMS0yLTJWNXptMyA0YTEgMSAwIDAwMCAyaC4wMWExIDEgMCAxMDAtMkg3em0zIDBhMSAxIDAgMDAwIDJoM2ExIDEgMCAxMDAtMmgtM3pcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINi32YTYqCDYsdmC2YUgI3tvcmRlci5PcmRlcklEfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnptMS0xMmExIDEgMCAxMC0yIDB2NGExIDEgMCAwMC4yOTMuNzA3bDIuODI4IDIuODI5YTEgMSAwIDEwMS40MTUtMS40MTVMMTEgOS41ODZWNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERpc3RhbmNlVG9Ob3cobmV3IERhdGUob3JkZXIuT3JkZXJEYXRlKSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZFN1ZmZpeDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbGU6IGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5DdXN0b21lclBob25lTnVtICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIgM2ExIDEgMCAwMTEtMWgyLjE1M2ExIDEgMCAwMS45ODYuODM2bC43NCA0LjQzNWExIDEgMCAwMS0uNTQgMS4wNmwtMS41NDguNzczYTExLjAzNyAxMS4wMzcgMCAwMDYuMTA1IDYuMTA1bC43NzQtMS41NDhhMSAxIDAgMDExLjA1OS0uNTRsNC40MzUuNzRhMSAxIDAgMDEuODM2Ljk4NlYxN2ExIDEgMCAwMS0xIDFoLTJDNy44MiAxOCAyIDEyLjE4IDIgNVYzelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57b3JkZXIuQ3VzdG9tZXJQaG9uZU51bX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNDAwIHRvLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LXNlbWlib2xkIHNoYWRvdy1sZyBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctd2hpdGUgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDZgtmK2K8g2KfZhNiq2K3YttmK2LFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uob3JkZXIuQ2FydFRvdGFsUHJpY2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+2KfZhNmF2KzZhdmI2Lkg2KfZhNmD2YTZijwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFByb2R1Y3RzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ncmVlbi0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyZWVuLTYwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0zIDRhMSAxIDAgMDExLTFoMTJhMSAxIDAgMDExIDF2MmExIDEgMCAwMS0xIDFINGExIDEgMCAwMS0xLTFWNHpNMyAxMGExIDEgMCAwMTEtMWg2YTEgMSAwIDAxMSAxdjZhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xdi02ek0xNCA5YTEgMSAwIDAwLTEgMXY2YTEgMSAwIDAwMSAxaDJhMSAxIDAgMDAxLTF2LTZhMSAxIDAgMDAtMS0xaC0yelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj7Yp9mE2YXZhtiq2KzYp9iqINin2YTZhdi32YTZiNio2Kk8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIHRleHQteHMgZm9udC1tZWRpdW0gcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7b3JkZXIuUHJvZHVjdHM/Lmxlbmd0aCB8fCAwfSDZhdmG2KrYrFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5Qcm9kdWN0cyAmJiBvcmRlci5Qcm9kdWN0cy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcmRlci5Qcm9kdWN0cy5tYXAoKGl0ZW06IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGJvcmRlciBib3JkZXItZ3JheS0xMDAgcm91bmRlZC14bCBwLTRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUHJvZHVjdCBJbWFnZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHJvdW5kZWQteGwgb3ZlcmZsb3ctaGlkZGVuIGJnLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5Qcm9kdWN0LkltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtnZXRJbWFnZVVybChpdGVtLlByb2R1Y3QuSW1hZ2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17aXRlbS5Qcm9kdWN0LlByb2R1Y3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZS50YXJnZXQgYXMgSFRNTEltYWdlRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zcmMgPSAnL2ltYWdlcy9wbGFjZWhvbGRlci1mb29kLmpwZyc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyYXktNDAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCAzYTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY1YTIgMiAwIDAwLTItMkg0em0xMiAxMkg0bDQtOCAzIDYgMi00IDMgNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFF1YW50aXR5IEJhZGdlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1yaWdodC0yIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgZm9udC1ib2xkIHJvdW5kZWQtZnVsbCB3LTYgaC02IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5RdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFByb2R1Y3QgRGV0YWlscyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5Qcm9kdWN0LlByb2R1Y3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oNT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKGl0ZW0uUHJvZHVjdC5QcmljZSAqIGl0ZW0uUXVhbnRpdHkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShpdGVtLlByb2R1Y3QuUHJpY2UpfSDDlyB7aXRlbS5RdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogSW5ncmVkaWVudHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uSW5ncmVkaWVudFVzYWdlcyAmJiBpdGVtLkluZ3JlZGllbnRVc2FnZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7Yp9mE2YXZg9mI2YbYp9iqINin2YTZhdiu2KrYp9ix2Kk6PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uSW5ncmVkaWVudFVzYWdlcy5tYXAoKHVzYWdlOiBhbnksIGlkeDogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2lkeH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzYWdlLklzTmVlZGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCBib3JkZXIgYm9yZGVyLXJlZC0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B3LTEuNSBoLTEuNSByb3VuZGVkLWZ1bGwgbXItMSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzYWdlLklzTmVlZGVkID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmctcmVkLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9Pjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzYWdlLkluZ3JlZGllbnQuSW5ncmVkaWVudE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2FnZS5Jc05lZWRlZCA/ICcg4pyTJyA6ICcg4pyXJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IGJnLWdyYXktNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi0zXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMjAgMTNWNmEyIDIgMCAwMC0yLTJINmEyIDIgMCAwMC0yIDJ2N20xNiAwdjVhMiAyIDAgMDEtMiAySDZhMiAyIDAgMDEtMi0ydi01bTE2IDBoLTJNNCAxM2gybTEzLThWNGExIDEgMCAwMC0xLTFIN2ExIDEgMCAwMC0xIDF2MW04IDBWNC41XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGZvbnQtbWVkaXVtXCI+2YTYpyDYqtmI2KzYryDZhdmG2KrYrNin2Kog2YHZiiDZh9iw2Kcg2KfZhNi32YTYqDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9Nb2RhbC5Cb2R5PlxuXG4gICAgICAgICAgPE1vZGFsLkZvb3RlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0xMDAgcm91bmRlZC1iLTJ4bFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgdy1mdWxsXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBjb2xvcj1cImdyYXlcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoQ3VycmVudE9yZGVyc31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IDxTcGlubmVyIHNpemU9XCJzbVwiIC8+IDogPEhpUmVmcmVzaCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPn1cbiAgICAgICAgICAgICAgICDYqtit2K/ZitirXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgY29sb3I9XCJsaWdodFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0N1cnJlbnRPcmRlcnNNb2RhbChmYWxzZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDYpdi62YTYp9mCXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9Nb2RhbC5Gb290ZXI+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9Nb2RhbD5cbiAgICA8Lz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IE9yZGVyTm90aWZpY2F0aW9uT3ZlcmxheTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlT3JkZXJOb3RpZmljYXRpb24iLCJNb2RhbCIsIkJ1dHRvbiIsIlNwaW5uZXIiLCJBbGVydCIsIkhpQ2hlY2tDaXJjbGUiLCJIaVhDaXJjbGUiLCJIaVJlZnJlc2giLCJmb3JtYXREaXN0YW5jZVRvTm93IiwiYXIiLCJIaUV4Y2xhbWF0aW9uVHJpYW5nbGUiLCJPcmRlck5vdGlmaWNhdGlvbk92ZXJsYXkiLCJyZXN0YXVyYW50SWQiLCJwZW5kaW5nT3JkZXJzIiwiaXNDb25uZWN0ZWQiLCJpc0xvYWRpbmciLCJlcnJvciIsImFjY2VwdE9yZGVyIiwicmVqZWN0T3JkZXIiLCJyZWZyZXNoT3JkZXJzIiwic2hvd01vZGFsIiwic2V0U2hvd01vZGFsIiwic2hvd0N1cnJlbnRPcmRlcnNNb2RhbCIsInNldFNob3dDdXJyZW50T3JkZXJzTW9kYWwiLCJzaG93UmVqZWN0Q29uZmlybU1vZGFsIiwic2V0U2hvd1JlamVjdENvbmZpcm1Nb2RhbCIsIm9yZGVyVG9SZWplY3QiLCJzZXRPcmRlclRvUmVqZWN0IiwicHJvY2Vzc2luZ09yZGVySWQiLCJzZXRQcm9jZXNzaW5nT3JkZXJJZCIsImN1cnJlbnRPcmRlcnMiLCJzZXRDdXJyZW50T3JkZXJzIiwicHJlcGFyaW5nT3JkZXJzIiwic2V0UHJlcGFyaW5nT3JkZXJzIiwidXNlRWZmZWN0IiwiY29uc29sZSIsImxvZyIsImxlbmd0aCIsImZldGNoUHJlcGFyaW5nT3JkZXJzIiwidXNlQ2FsbGJhY2siLCJhY2Nlc3NUb2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJFcnJvciIsImFwaVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJzdGF0dXMiLCJvayIsInJlc3BvbnNlRGF0YSIsImpzb24iLCJvcmRlcnMiLCJpdGVtcyIsImRhdGEiLCJwcmVwYXJpbmciLCJmaWx0ZXIiLCJvcmRlciIsIlN0YXR1cyIsIm1hcCIsIm8iLCJpZCIsIk9yZGVySUQiLCJ0b3RhbCIsIkNhcnRUb3RhbFByaWNlIiwiZXJyIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJoYW5kbGVBY2NlcHRPcmRlciIsIm9yZGVySWQiLCJoYW5kbGVSZWplY3RPcmRlciIsImNvbmZpcm1SZWplY3RPcmRlciIsImZldGNoQ3VycmVudE9yZGVycyIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJ0b0ZpeGVkIiwiZ2V0SW1hZ2VVcmwiLCJpbWFnZVVybCIsInN0YXJ0c1dpdGgiLCJyZXBsYWNlIiwiQ29ubmVjdGlvblN0YXR1cyIsImRpdiIsImNsYXNzTmFtZSIsImNvbG9yIiwiaWNvbiIsInNwYW4iLCJzaXplIiwib25DbGljayIsImRpc2FibGVkIiwiTm90aWZpY2F0aW9uQmFkZ2UiLCJDdXJyZW50T3JkZXJzQnV0dG9uIiwiYnV0dG9uIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsImZpbGxSdWxlIiwiY2xpcFJ1bGUiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5Iiwic2hvdyIsIm9uQ2xvc2UiLCJwb3NpdGlvbiIsImRpc21pc3NpYmxlIiwiSGVhZGVyIiwiaDIiLCJwIiwiQm9keSIsImgzIiwiRGF0ZSIsIk9yZGVyRGF0ZSIsImFkZFN1ZmZpeCIsImxvY2FsZSIsIkN1c3RvbWVyUGhvbmVOdW0iLCJOb3RlIiwiaDQiLCJQcm9kdWN0cyIsIml0ZW0iLCJpbmRleCIsIlByb2R1Y3QiLCJJbWFnZSIsImltZyIsInNyYyIsImFsdCIsIlByb2R1Y3ROYW1lIiwib25FcnJvciIsImUiLCJ0YXJnZXQiLCJRdWFudGl0eSIsImg1IiwiUHJpY2UiLCJJbmdyZWRpZW50VXNhZ2VzIiwidXNhZ2UiLCJpZHgiLCJJc05lZWRlZCIsIkluZ3JlZGllbnQiLCJJbmdyZWRpZW50TmFtZSIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiUmVzdGF1cmFudE5hbWUiLCJEcml2ZXJGaXJzdE5hbWUiLCJEcml2ZXJMYXN0TmFtZSIsIkRpc3RhbmNlSW5NZXRlcnMiLCJ0cmltIiwiRm9vdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/shared/OrderNotificationOverlay.tsx\n"));

/***/ })

});
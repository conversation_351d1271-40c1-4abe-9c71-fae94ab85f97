const sequelize = require("../config/database");
const { Op } = require("sequelize");
const Sequelize = require("sequelize");
const models = require("../models/init-models")(sequelize);
const { Driver, Order, DriverInvoices, DriverModerate, UserAccount, Cart, Restaurant, Customer } = models;
const { getPagination, getPagingData } = require('../utils/pagination');
const bcrypt = require("bcrypt");
const moment = require("moment");

// Helper function to calculate distance between two points (returns meters)
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) *
      Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

// Helper function to round to nearest 0.25
function roundToQuarter(num) {
  return Math.round(num * 4) / 4;
}

exports.getAllDrivers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: drivers } = await Driver.findAndCountAll({
      where: {
        Status: 1,
      },
      attributes: {
        exclude: ["PasswordHash"],
      },
      limit,
      offset,
      order: [["DriverID", "DESC"]],
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      data: drivers,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: count,
        itemsPerPage: limit,
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


exports.getDriverById = async (req, res) => {
  try {
    const driver = await Driver.findOne({
      where: {
        DriverID: req.params.id,
        Status: 1,
      },
      attributes: {
        exclude: ["PasswordHash"],
      },
    });

    if (!driver) {
      return res.status(404).json({ message: "لم يتم العثور على السائق" });
    }

    res.json({
      success: true,
      data: driver,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateDriverStatus = async (req, res) => {
  try {
    const { status, isWorking } = req.body;
    const driver = await Driver.findByPk(req.params.id);

    if (!driver) {
      return res.status(404).json({ message: "لم يتم العثور على السائق" });
    }

    if (status !== undefined) {
      driver.Status = status;
    }
    if (isWorking !== undefined) {
      driver.IsWorking = isWorking;
    }

    await driver.save();

    const updatedDriver = await Driver.findByPk(req.params.id, {
      attributes: [
        "DriverID",
        "FirstName",
        "LastName",
        "PhoneNumber",
        "Email",
        "IsWorking",
        "Longitude",
        "Latitude",
      ],
    });
    res.json(updatedDriver);
  } catch (error) {
    console.error("Error in updateDriverStatus:", error);
    res.status(500).json({ message: "حدث خطأ أثناء تحديث حالة السائق" });
  }
};

exports.deleteDriver = async (req, res) => {
  try {
    const driver = await Driver.findByPk(req.params.id);
    if (!driver) {
      return res.status(404).json({ message: "لم يتم العثور على السائق" });
    }

    driver.Status = 0;
    driver.IsWorking = false;
    await driver.save();
    res.json({ message: "تم إلغاء تفعيل السائق بنجاح" });
  } catch (error) {
    console.error("Error in deleteDriver:", error);
    res.status(500).json({ message: "حدث خطأ أثناء إلغاء تفعيل السائق" });
  }
};

exports.getDriverOrders = async (req, res) => {
  try {
    const orders = await Order.findAll({
      where: { DriverID: req.params.id },
      include: ["Customer", "Cart"],
    });
    res.json(orders);
  } catch (error) {
    console.error("Error in getDriverOrders:", error);
    res.status(500).json({ message: "حدث خطأ أثناء جلب طلبات السائق" });
  }
};

exports.editDriver = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    // Log the request for debugging
    console.log("Edit driver request body:", JSON.stringify(req.body));
    console.log("Edit driver request headers:", JSON.stringify(req.headers));
    console.log("Driver ID:", req.params.id);

    const {
      FirstName,
      LastName,
      PhoneNumber,
      Username,
      PasswordHash,
      Address,
      CompanyCar,
      CarData,
      PlateNumber,
      licenseImage,
      JoinDate, // We'll ignore this for now to avoid conversion issues
    } = req.body;

    const driver = await Driver.findOne({
      where: {
        DriverID: req.params.id,
        Status: 1,
      },
    });

    if (!driver) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: "لم يتم العثور على السائق",
      });
    }

    if (Username !== driver.Username) {
      const existingDriver = await Driver.findOne({
        where: {
          Username,
          DriverID: { [Op.ne]: req.params.id },
          Status: 1,
        },
      });

      if (existingDriver) {
        await t.rollback();
        return res.status(400).json({
          success: false,
          message: "اسم المستخدم موجود مسبقاً",
        });
      }
    }

    const updateData = {};
    if (FirstName) updateData.FirstName = FirstName;
    if (LastName) updateData.LastName = LastName;
    if (PhoneNumber) updateData.PhoneNumber = PhoneNumber;
    if (Username) updateData.Username = Username;
    if (PasswordHash)
      updateData.PasswordHash = await bcrypt.hash(PasswordHash, 10);
    if (Address !== undefined) updateData.Address = Address;
    if (CompanyCar !== undefined) updateData.CompanyCar = CompanyCar;
    if (CarData !== undefined) updateData.CarData = CarData;
    if (PlateNumber !== undefined) updateData.PlateNumber = PlateNumber;
    if (licenseImage !== undefined) updateData.licenseImage = licenseImage;
    
    // Note: We're intentionally NOT updating the JoinDate field to avoid conversion errors
    // If JoinDate updates are required, implement a separate endpoint for that specific purpose
    
    updateData.UpdateDate = Sequelize.fn("getdate");

    await driver.update(updateData, { transaction: t });
    await t.commit();

    res.json({
      success: true,
      message: "تم تحديث بيانات السائق بنجاح",
      data: {
        DriverID: driver.DriverID,
        FirstName: driver.FirstName,
        LastName: driver.LastName,
        PhoneNumber: driver.PhoneNumber,
        Username: driver.Username,
        Address: driver.Address,
        CompanyCar: driver.CompanyCar,
        CarData: driver.CarData,
        PlateNumber: driver.PlateNumber,
        licenseImage: driver.licenseImage,
        JoinDate: driver.JoinDate,
        IsWorking: driver.IsWorking,
        Status: driver.Status,
        Activated: driver.Activated,
      },
    });
  } catch (error) {
    await t.rollback();
    console.error("Error in editDriver:", error);
    res.status(500).json({
      success: false,
      message: error.message || "حدث خطأ أثناء تحديث بيانات السائق",
    });
  }
};

exports.receiveWallet = async (req, res) => {
  try {
    const { driverId, amount } = req.body;

    if (!driverId || !amount) {
      return res.status(400).json({
        success: false,
        message: "يجب توفير معرف السائق والمبلغ المطلوب",
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "يجب أن يكون المبلغ أكبر من صفر",
      });
    }

    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({
        success: false,
        message: "لم يتم العثور على السائق",
      });
    }

    const walletAmount = driver.Wallet || 0;
    if (walletAmount < amount) {
      return res.status(400).json({
        success: false,
        message: "لا يوجد رصيد كافي في المحفظة للاستلام",
        currentBalance: walletAmount,
        requestedAmount: amount,
      });
    }

    await DriverInvoices.create({
      DriverID: driverId,
      Date: Sequelize.literal("CURRENT_TIMESTAMP"),
      Price: amount,
      ReceivedBy: (req.user && req.user.id) || null,
    });

    driver.Wallet = walletAmount - amount;
    await driver.save();

    res.json({
      success: true,
      message: "تم استلام المبلغ من المحفظة بنجاح",
      data: {
        amountReceived: amount,
        remainingBalance: driver.Wallet,
        driverName: `${driver.FirstName} ${driver.LastName}`,
      },
    });
  } catch (error) {
    console.error("Error in receiveWallet:", error);
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء استلام المبلغ من المحفظة",
      error: error.message,
    });
  }
};

exports.getInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: invoices } = await DriverInvoices.findAndCountAll({
      limit,
      offset,
      include: [
        {
          model: Driver,
          as: "Driver",
          attributes: ["FirstName", "LastName"],
        },
      ],
      order: [["Date", "DESC"]],
    });

    // Process invoices to add received by username
    const processedInvoices = await Promise.all(
      invoices.map(async (invoice) => {
        const user = await UserAccount.findOne({
          where: { UserID: invoice.ReceivedBy },
          attributes: ["Username"],
        });
        return {
          ...invoice.toJSON(),
          Driver: {
            ...invoice.Driver.toJSON(),
            Name: `${invoice.Driver.FirstName} ${invoice.Driver.LastName}`,
          },
          ReceivedByUsername: user ? user.Username : "Unknown",
        };
      })
    );

    const totalPages = Math.ceil(count / limit);

    res.json({
      data: processedInvoices,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: count,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      },
    });
  } catch (error) {
    console.error("Error in getInvoices:", error);
    res.status(500).json({ message: "حدث خطأ أثناء جلب الفواتير" });
  }
};

exports.getDriverInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: invoices } = await DriverInvoices.findAndCountAll({
      where: {
        DriverID: req.params.id,
      },
      limit,
      offset,
      include: [
        {
          model: Driver,
          as: "Driver",
          attributes: ["FirstName", "LastName"],
        },
      ],
      order: [["Date", "DESC"]],
    });

    // Process invoices to add received by username
    const processedInvoices = await Promise.all(
      invoices.map(async (invoice) => {
        const user = await UserAccount.findOne({
          where: { UserID: invoice.ReceivedBy },
          attributes: ["Username"],
        });
        return {
          ...invoice.toJSON(),
          Driver: {
            ...invoice.Driver.toJSON(),
            Name: `${invoice.Driver.FirstName} ${invoice.Driver.LastName}`,
          },
          ReceivedByUsername: user ? user.Username : "Unknown",
        };
      })
    );

    const totalPages = Math.ceil(count / limit);

    res.json({
      data: processedInvoices,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: count,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      },
    });
  } catch (error) {
    console.error("Error in getDriverInvoices:", error);
    res.status(500).json({ message: "حدث خطأ أثناء جلب فواتير السائق" });
  }
};

exports.createDriver = async (req, res) => {
  try {
    // Log the entire request body for debugging
    console.log("Create driver request body:", JSON.stringify(req.body));

    const {
      FirstName,
      LastName,
      PhoneNumber,
      Username,
      PasswordHash,
      Address,
      CompanyCar,
      CarData,
      PlateNumber,
      licenseImage,
      // JoinDate, // We're ignoring this completely
    } = req.body;

    if (!FirstName || !LastName || !Username || !PasswordHash) {
      return res.status(400).json({
        success: false,
        message: "جميع الحقول المطلوبة يجب تعبئتها",
      });
    }

    const existingDriver = await Driver.findOne({
      where: {
        Username,
        Status: 1,
      },
    });

    if (existingDriver) {
      return res.status(400).json({
        success: false,
        message: "اسم المستخدم موجود مسبقاً",
      });
    }

    const hashedPassword = await bcrypt.hash(PasswordHash, 10);

    const driverData = {
      FirstName,
      LastName,
      PhoneNumber,
      Username,
      PasswordHash: hashedPassword,
      Address,
      CompanyCar: CompanyCar || false,
      CarData: CarData || "",
      PlateNumber: PlateNumber || "",
      licenseImage: licenseImage || "",
      Status: 1,
      IsWorking: false,
      Activated: true,
      // Let Sequelize handle the date with a SQL function
      JoinDate: Sequelize.fn('GETDATE')
    };

    console.log(
      "Final driver data being sent to database:",
      JSON.stringify(driverData)
    );
    const driver = await Driver.create(driverData);

    res.status(201).json({
      success: true,
      message: "تم إضافة السائق بنجاح",
      data: {
        DriverID: driver.DriverID,
        FirstName: driver.FirstName,
        LastName: driver.LastName,
        PhoneNumber: driver.PhoneNumber,
        Username: driver.Username,
        Address: driver.Address,
        CompanyCar: driver.CompanyCar,
        CarData: driver.CarData,
        PlateNumber: driver.PlateNumber,
        licenseImage: driver.licenseImage,
        IsWorking: driver.IsWorking,
        Status: driver.Status,
        Activated: driver.Activated,
        JoinDate: driver.JoinDate,
      },
    });
  } catch (error) {
    console.error("Error in createDriver:", error);
    res.status(500).json({
      success: false,
      message: error.message || "حدث خطأ أثناء إضافة السائق",
    });
  }
};

exports.updateDriverNews = async (req, res) => {
  try {
    const { news, updatedBy, newsExpireDate } = req.body;

    if (!news || !updatedBy || !newsExpireDate) {
      return res.status(400).json({
        message: "Missing required fields: news, updatedBy, or newsExpireDate",
      });
    }

    const expireDate = new Date(newsExpireDate);
    if (isNaN(expireDate.getTime())) {
      return res.status(400).json({
        message: "Invalid newsExpireDate format",
      });
    }

    const driverModerate = await DriverModerate.findOne();
    if (!driverModerate) {
      return res.status(404).json({
        message: "Driver moderate settings not found",
      });
    }

    await driverModerate.update({
      News: news,
      UpdateBy: updatedBy,
      NewsExpireDate: expireDate,
    });

    res.status(200).json({
      message: "Driver news updated successfully",
      news: driverModerate,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error updating driver news",
      error: error.message,
    });
  }
};

exports.getDriverNews = async (req, res) => {
  try {
    const driverModerate = await DriverModerate.findOne();

    if (!driverModerate) {
      return res.status(404).json({
        message: "Driver moderate settings not found",
      });
    }

    const currentDate = new Date();
    const newsExpireDate = new Date(driverModerate.NewsExpireDate);

    if (
      !driverModerate.News ||
      !newsExpireDate ||
      newsExpireDate < currentDate
    ) {
      return res.status(200).json({
        news: "",
      });
    }

    res.status(200).json({
      news: driverModerate.News,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching driver news",
      error: error.message,
    });
  }
};
exports.getCompanyProfitPercent = async (req, res) => {
  try {
    const driverModerate = await DriverModerate.findOne({
      attributes: ["CompanyProfitPercent"],
    });

    if (!driverModerate) {
      return res.status(404).json({
        message: "Driver moderate settings not found",
      });
    }

    res.status(200).json({
      companyProfitPercent: driverModerate.CompanyProfitPercent,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching company profit percent",
      error: error.message,
    });
  }
};

exports.updateCompanyProfitPercent = async (req, res) => {
  try {
    const { profitPercent } = req.body;

    if (profitPercent === undefined || profitPercent === null) {
      return res.status(400).json({
        message: "Missing required field: profitPercent",
      });
    }

    const percent = parseFloat(profitPercent);
    if (isNaN(percent) || percent < 0 || percent > 100) {
      return res.status(400).json({
        message: "Invalid profit percent. Must be between 0 and 100",
      });
    }

    const driverModerate = await DriverModerate.findOne();
    if (!driverModerate) {
      return res.status(404).json({
        message: "Driver moderate settings not found",
      });
    }

    await driverModerate.update({
      CompanyProfitPercent: percent,
    });

    res.status(200).json({
      message: "Company profit percent updated successfully",
      companyProfitPercent: percent,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error updating company profit percent",
      error: error.message,
    });
  }
};

exports.searchDrivers = async (req, res) => {
  try {
    const { searchTerm } = req.query;

    if (!searchTerm) {
      return res.status(400).json({
        success: false,
        message: "Search term is required",
      });
    }

    const drivers = await Driver.findAll({
      where: {
        [Op.or]: [
          {
            [Op.or]: [
              {
                FirstName: {
                  [Op.like]: `%${searchTerm}%`,
                },
              },
              {
                LastName: {
                  [Op.like]: `%${searchTerm}%`,
                },
              },
            ],
          },
          {
            PhoneNumber: {
              [Op.like]: `%${searchTerm}%`,
            },
          },
        ],
      },
      attributes: ["DriverID", "FirstName", "LastName", "PhoneNumber"],
    });

    const formattedDrivers = drivers.map((driver) => ({
      DriverID: driver.DriverID,
      Name: `${driver.FirstName} ${driver.LastName}`,
      Phone: driver.PhoneNumber,
    }));

    res.status(200).json({
      success: true,
      data: formattedDrivers,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error searching drivers",
      error: error.message,
    });
  }
};

exports.toggleDriverActivation = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const driver = await Driver.findOne({
      where: {
        DriverID: req.params.id,
        Status: { [Op.ne]: 0 },
      },
      transaction: t,
    });

    if (!driver) {
      await t.rollback();
      return res.status(404).json({
        success: false,
        message: "لم يتم العثور على السائق",
      });
    }

    const hasActiveOrders = await Order.findOne({
      where: {
        DriverID: req.params.id,
        Status: { [Op.in]: [1, 2, 3] },
      },
      transaction: t,
    });

    if (hasActiveOrders && driver.Activated) {
      await t.rollback();
      return res.status(400).json({
        success: false,
        message: "لا يمكن إلغاء تفعيل السائق لوجود طلبات نشطة",
      });
    }

    await driver.update(
      {
        Activated: !driver.Activated,
        IsWorking: driver.Activated ? false : driver.IsWorking,
        UpdateDate: Sequelize.fn("getdate"),
      },
      { transaction: t }
    );

    await t.commit();

    res.json({
      success: true,
      message: driver.Activated
        ? "تم تفعيل السائق بنجاح"
        : "تم إلغاء تفعيل السائق بنجاح",
      data: {
        DriverID: driver.DriverID,
        Activated: driver.Activated,
        IsWorking: driver.IsWorking,
      },
    });
  } catch (error) {
    await t.rollback();
    console.error("Error in toggleDriverActivation:", error);
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء تحديث حالة التفعيل",
      error: error.message,
    });
  }
};

// Get driver's available orders (Status = 1 and DriverID = null)
exports.getAvailableOrders = async (req, res) => {
  try {
    const { page, size, driverLat, driverLng } = req.query;
    const { limit, offset } = getPagination(page, size);

    // Parse driver location
    const driverLatitude = parseFloat(driverLat);
    const driverLongitude = parseFloat(driverLng);

    // Validate driver location
    if (!driverLat || !driverLng || isNaN(driverLatitude) || isNaN(driverLongitude)) {
      return res.status(400).json({
        success: false,
        message: 'Driver location (driverLat and driverLng) is required'
      });
    }

    const orders = await Order.findAndCountAll({
      where: {
        Status: 1,
        DriverID: null,
        Claimed: false
      },
      limit,
      offset,
      order: [['OrderDate', 'DESC']],
      attributes: [
        'OrderID', 'DriverID', 'CartID', 'CustomerID', 'Status', 'OrderDate', 'Claimed',
        'StartPointLatitude', 'StartPointLongitude', 'EndPointLatitude', 'EndPointLongitude'
      ],
      include: [
        {
          model: Cart,
          as: 'Cart',
          attributes: ['TotalPrice', 'RestaurantID'],
        },
        {
          model: Customer,
          as: 'Customer',
          attributes: ['CustomerID', 'PhoneNum']
        }
      ]
    });

    // Calculate distances and costs with driver location
    const formattedOrders = await Promise.all(orders.rows.map(async (order) => {
      const plainOrder = order.get({ plain: true });

      const {
        StartPointLatitude,
        StartPointLongitude,
        EndPointLatitude,
        EndPointLongitude,
        ...orderData
      } = plainOrder;

      let restaurantName = null;
      let cartTotalPrice = null;

      if (order.CartID) {
        const cart = await Cart.findByPk(order.CartID);
        if (cart) {
          cartTotalPrice = cart.TotalPrice;
          if (cart.RestaurantID) {
            const restaurant = await Restaurant.findByPk(cart.RestaurantID);
            if (restaurant) {
              restaurantName = restaurant.Name;
            }
          }
        }
      }

      // Calculate distances
      let driverToRestaurantDistance = null;
      let restaurantToCustomerDistance = null;
      let totalDistance = null;
      let estimatedDriverProfit = 0;
      let estimatedCompanyProfit = 0;
      let drivingCost = 0;

      // Driver to restaurant distance
      if (StartPointLatitude != null && StartPointLongitude != null) {
        driverToRestaurantDistance = calculateDistance(
          driverLatitude,
          driverLongitude,
          StartPointLatitude,
          StartPointLongitude
        );
      }

      // Restaurant to customer distance (delivery distance)
      if (
        StartPointLatitude != null &&
        StartPointLongitude != null &&
        EndPointLatitude != null &&
        EndPointLongitude != null
      ) {
        restaurantToCustomerDistance = calculateDistance(
          StartPointLatitude,
          StartPointLongitude,
          EndPointLatitude,
          EndPointLongitude
        );
      }

      // Total distance
      if (driverToRestaurantDistance != null && restaurantToCustomerDistance != null) {
        totalDistance = driverToRestaurantDistance + restaurantToCustomerDistance;
      }

      // Use pre-calculated values from order model instead of recalculating
      if (order.DeliveryFee != null && order.DriverProfit != null) {
        // Use values calculated when order was created
        const totalDeliveryFee = parseFloat(order.DeliveryFee) || 0;
        estimatedDriverProfit = parseFloat(order.DriverProfit) || 0;
        estimatedCompanyProfit = totalDeliveryFee - estimatedDriverProfit;
        drivingCost = estimatedCompanyProfit;

        console.log(`Using pre-calculated values - DeliveryFee: ${totalDeliveryFee}, DriverProfit: ${estimatedDriverProfit}`);
      } else if (restaurantToCustomerDistance != null) {
        // Fallback to calculation if values not stored (for old orders)
        try {
          const driverModerate = await DriverModerate.findByPk(1);
          if (driverModerate) {
            const deliveryDistanceInKm = restaurantToCustomerDistance / 1000;
            let totalDeliveryFee = 0;

            if (driverModerate.IsConst) {
              totalDeliveryFee = driverModerate.ConstValue || 0;
            } else {
              totalDeliveryFee = deliveryDistanceInKm * (driverModerate.CostPerKilometer || 0);
            }

            estimatedCompanyProfit = (totalDeliveryFee * (driverModerate.CompanyProfitPercent || 0)) / 100;
            estimatedDriverProfit = totalDeliveryFee - estimatedCompanyProfit;
            drivingCost = estimatedCompanyProfit;
          }
        } catch (profitError) {
          console.log('Profit calculation error:', profitError.message);
        }
      }

      // Calculate total delivery fee for display
      const totalDeliveryFee = estimatedDriverProfit + estimatedCompanyProfit;

      return {
        ...orderData,
        StartPointLatitude: StartPointLatitude,
        StartPointLongitude: StartPointLongitude,
        EndPointLatitude: EndPointLatitude,
        EndPointLongitude: EndPointLongitude,
        RestaurantName: restaurantName,
        CartTotalPrice: cartTotalPrice,
        // Distance information
        DriverToRestaurantDistance: driverToRestaurantDistance,
        DriverToRestaurantDistanceKm: driverToRestaurantDistance ? (driverToRestaurantDistance / 1000).toFixed(2) : null,
        RestaurantToCustomerDistance: restaurantToCustomerDistance,
        RestaurantToCustomerDistanceKm: restaurantToCustomerDistance ? (restaurantToCustomerDistance / 1000).toFixed(2) : null,
        DeliveryDistance: restaurantToCustomerDistance ? roundToQuarter(restaurantToCustomerDistance / 1000) : null, // Distance in km for frontend
        TotalDistance: totalDistance,
        TotalDistanceKm: totalDistance ? (totalDistance / 1000).toFixed(2) : null,
        // Financial information
        TotalDeliveryFee: roundToQuarter(totalDeliveryFee),
        DrivingCost: roundToQuarter(drivingCost),
        EstimatedDriverProfit: roundToQuarter(estimatedDriverProfit),
        EstimatedCompanyProfit: roundToQuarter(estimatedCompanyProfit),
        TotalCustomerPayment: roundToQuarter((cartTotalPrice || 0) + totalDeliveryFee)
      };
    }));

    const response = getPagingData({ rows: formattedOrders, count: orders.count }, page, limit);
    res.status(200).json(response);

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching available orders - - -',
      error: error
    });
  }
};

// Get driver's assigned orders
exports.getMyOrders = async (req, res) => {
  try {
    const driverId = req.driver.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: orders } = await Order.findAndCountAll({
      where: {
        DriverID: driverId
      },
      attributes: [
        'OrderID', 'DriverID', 'CartID', 'CustomerID', 'Status', 'OrderDate', 'Claimed',
        'StartPointLatitude', 'StartPointLongitude', 'EndPointLatitude', 'EndPointLongitude'
      ],
      include: [
        {
          model: Cart,
          as: 'Cart',
          attributes: ['TotalPrice', 'RestaurantID']
        },
        {
          model: Customer,
          as: 'Customer',
          attributes: ['CustomerID', 'PhoneNum']
        }
      ],
      limit,
      offset,
      order: [["OrderDate", "DESC"]]
    });

    // Get restaurant details and calculate delivery distance for each order
    const ordersWithRestaurant = await Promise.all(
      orders.map(async (order) => {
        const restaurant = await Restaurant.findByPk(order.Cart.RestaurantID, {
          attributes: ['Name', 'Address']
        });

        // Use pre-calculated values from order model
        let deliveryDistance = null;
        let drivingCost = 0;
        let totalDeliveryFee = 0;
        let estimatedDriverProfit = 0;
        let estimatedCompanyProfit = 0;

        // Use pre-calculated distance from order model
        if (order.Distance != null) {
          deliveryDistance = parseFloat(order.Distance);
        } else if (order.StartPointLatitude && order.StartPointLongitude &&
                   order.EndPointLatitude && order.EndPointLongitude) {
          // Fallback calculation for old orders
          const restaurantToCustomerDistance = calculateDistance(
            order.StartPointLatitude,
            order.StartPointLongitude,
            order.EndPointLatitude,
            order.EndPointLongitude
          );
          deliveryDistance = restaurantToCustomerDistance / 1000; // Convert to km
        }

        // Use pre-calculated values from order model
        if (order.DeliveryFee != null && order.DriverProfit != null) {
          totalDeliveryFee = parseFloat(order.DeliveryFee);
          estimatedDriverProfit = parseFloat(order.DriverProfit);
          estimatedCompanyProfit = totalDeliveryFee - estimatedDriverProfit;
          drivingCost = estimatedCompanyProfit;
        } else if (deliveryDistance != null) {
          // Fallback calculation for old orders
          try {
            const driverModerate = await DriverModerate.findByPk(1);
            if (driverModerate) {
              if (driverModerate.IsConst) {
                totalDeliveryFee = driverModerate.ConstValue || 0;
              } else {
                totalDeliveryFee = deliveryDistance * (driverModerate.CostPerKilometer || 0);
              }

              estimatedCompanyProfit = (totalDeliveryFee * (driverModerate.CompanyProfitPercent || 0)) / 100;
              estimatedDriverProfit = totalDeliveryFee - estimatedCompanyProfit;
              drivingCost = estimatedCompanyProfit;
            }
          } catch (profitError) {
            console.log('Profit calculation error:', profitError.message);
          }
        }

        return {
          ...order.toJSON(),
          Cart: {
            ...order.Cart.toJSON(),
            Restaurant: restaurant
          },
          // Add delivery distance and financial information
          DeliveryDistance: deliveryDistance ? roundToQuarter(deliveryDistance) : null,
          TotalDeliveryFee: roundToQuarter(totalDeliveryFee),
          DrivingCost: roundToQuarter(drivingCost),
          EstimatedDriverProfit: roundToQuarter(estimatedDriverProfit),
          EstimatedCompanyProfit: roundToQuarter(estimatedCompanyProfit),
          TotalCustomerPayment: roundToQuarter((order.Cart.TotalPrice || 0) + totalDeliveryFee)
        };
      })
    );

    res.json({
      success: true,
      data: {
        orders: ordersWithRestaurant,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء جلب طلبات السائق",
      error: error.message
    });
  }
};

// Accept/Claim an order
exports.acceptOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const driverId = req.driver.id;

    // Check if driver already has an active order
    const activeOrder = await Order.findOne({
      where: {
        DriverID: driverId,
        Claimed: true,
        Status: {
          [Op.notIn]: [3, 4, 5, 6, 7, 8] // Not delivered, not delivered, not received, rejected, delivery failed, not prepared
        }
      }
    });

    if (activeOrder) {
      return res.status(400).json({
        success: false,
        message: "لديك طلب نشط بالفعل. يجب إنهاء الطلب الحالي أولاً",
        activeOrder: {
          OrderID: activeOrder.OrderID,
          Status: activeOrder.Status
        }
      });
    }

    // Find the order to claim
    const order = await Order.findOne({
      where: {
        OrderID: orderId,
        Status: 1, // Accepted by restaurant
        DriverID: null,
        Claimed: false
      }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "الطلب غير متاح أو تم قبوله من قبل سائق آخر"
      });
    }

    // Claim the order
    await order.update({
      DriverID: driverId,
      Claimed: true,
      Status: 2 // Out for delivery
    });

    res.json({
      success: true,
      message: "تم قبول الطلب بنجاح",
      data: {
        OrderID: order.OrderID,
        Status: order.Status,
        Claimed: order.Claimed,
        DriverID: order.DriverID
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء قبول الطلب",
      error: error.message
    });
  }
};

// Check if driver has an active order (for app startup)
exports.getActiveOrder = async (req, res) => {
  try {
    const driverId = req.driver.id;

    const activeOrder = await Order.findOne({
      where: {
        DriverID: driverId,
        Claimed: true,
        Status: {
          [Op.notIn]: [3, 4, 5, 6, 7, 8] // Not in final states
        }
      },
      attributes: [
        'OrderID', 'DriverID', 'CartID', 'CustomerID', 'Status', 'OrderDate', 'Claimed',
        'StartPointLatitude', 'StartPointLongitude', 'EndPointLatitude', 'EndPointLongitude'
      ],
      include: [
        {
          model: Cart,
          as: 'Cart',
          attributes: ['TotalPrice', 'RestaurantID']
        },
        {
          model: Customer,
          as: 'Customer',
          attributes: ['CustomerID', 'PhoneNum']
        }
      ]
    });

    if (activeOrder) {
      // Get restaurant details and calculate profit
      let restaurantName = null;
      let cartTotalPrice = null;
      let estimatedDriverProfit = 0;
      let estimatedCompanyProfit = 0;
      let totalDeliveryFee = 0;

      if (activeOrder.CartID) {
        const cart = await Cart.findByPk(activeOrder.CartID);
        if (cart) {
          cartTotalPrice = cart.TotalPrice;
          if (cart.RestaurantID) {
            const restaurant = await Restaurant.findByPk(cart.RestaurantID);
            if (restaurant) {
              restaurantName = restaurant.Name;
            }
          }
        }
      }

      // Use pre-calculated values from order model
      if (activeOrder.DeliveryFee != null && activeOrder.DriverProfit != null) {
        totalDeliveryFee = parseFloat(activeOrder.DeliveryFee);
        estimatedDriverProfit = parseFloat(activeOrder.DriverProfit);
        estimatedCompanyProfit = totalDeliveryFee - estimatedDriverProfit;
      } else {
        // Fallback calculation for old orders
        const { StartPointLatitude, StartPointLongitude, EndPointLatitude, EndPointLongitude } = activeOrder;

        if (StartPointLatitude && StartPointLongitude && EndPointLatitude && EndPointLongitude) {
          const distanceInMeters = calculateDistance(
            StartPointLatitude,
            StartPointLongitude,
            EndPointLatitude,
            EndPointLongitude
          );

          try {
            const driverModerate = await DriverModerate.findByPk(1);
            if (driverModerate) {
              const distanceInKm = distanceInMeters / 1000;

              if (driverModerate.IsConst) {
                totalDeliveryFee = driverModerate.ConstValue || 0;
              } else {
                totalDeliveryFee = distanceInKm * (driverModerate.CostPerKilometer || 0);
              }

              estimatedCompanyProfit = (totalDeliveryFee * (driverModerate.CompanyProfitPercent || 0)) / 100;
              estimatedDriverProfit = totalDeliveryFee - estimatedCompanyProfit;
            }
          } catch (profitError) {
            console.log('Profit calculation error:', profitError.message);
          }
        }
      }

      res.json({
        success: true,
        hasActiveOrder: true,
        data: {
          ...activeOrder.get({ plain: true }),
          RestaurantName: restaurantName,
          CartTotalPrice: cartTotalPrice,
          TotalDeliveryFee: totalDeliveryFee,
          EstimatedDriverProfit: estimatedDriverProfit,
          EstimatedCompanyProfit: estimatedCompanyProfit,
          TotalCustomerPayment: cartTotalPrice + totalDeliveryFee
        }
      });
    } else {
      res.json({
        success: true,
        hasActiveOrder: false,
        data: null
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء التحقق من الطلبات النشطة",
      error: error.message
    });
  }
};

// Update order status during delivery
exports.updateDeliveryStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body; // 2: Picked up, 3: Delivered, 5: Not received, 7: Delivery failed
    const driverId = req.driver.id;

    console.log(`Updating delivery status - OrderID: ${orderId}, Status: ${status}, DriverID: ${driverId}`);

    // Validate status
    const validStatuses = [2, 3, 5, 7];
    if (!validStatuses.includes(status)) {
      console.log(`Invalid status provided: ${status}`);
      return res.status(400).json({
        success: false,
        message: "حالة الطلب غير صحيحة"
      });
    }

    // Find the order
    const order = await Order.findOne({
      where: {
        OrderID: orderId,
        DriverID: driverId,
        Claimed: true
      }
    });

    if (!order) {
      console.log(`Order not found or not assigned to driver - OrderID: ${orderId}, DriverID: ${driverId}`);
      return res.status(404).json({
        success: false,
        message: "الطلب غير موجود أو غير مخصص لك"
      });
    }

    // Store old status to check if it changed
    const oldStatus = order.Status;

    // Update order status
    await order.update({
      Status: status
    });

    console.log(`Order status updated successfully - OrderID: ${orderId}, New Status: ${status}`);

    // Check if order is completed and calculate driver profit
    if (oldStatus !== status && (status === 3 || status === 5 || status === 7)) {
      console.log(`Order ${orderId} completed with status ${status}, calculating driver profit...`);

      try {
        // Import the profit calculation function from orderController
        const { calculateDriverProfit } = require('./orderController');
        const profitResult = await calculateDriverProfit(orderId, driverId);
        console.log('Driver profit calculated successfully:', profitResult);
      } catch (profitError) {
        console.error('Error calculating driver profit:', profitError);
        // Don't fail the order update if profit calculation fails
      }
    }

    let message = "";
    switch (status) {
      case 2:
        message = "تم تأكيد استلام الطلب من المطعم";
        break;
      case 3:
        message = "تم تسليم الطلب بنجاح";
        break;
      case 5:
        message = "تم تسجيل عدم استلام العميل للطلب";
        break;
      case 7:
        message = "تم إنهاء الرحلة بسبب فشل التوصيل";
        break;
    }

    res.json({
      success: true,
      message: message,
      data: {
        OrderID: order.OrderID,
        Status: order.Status,
        Claimed: order.Claimed
      }
    });
  } catch (error) {
    console.error('Error updating delivery status:', error);
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء تحديث حالة الطلب",
      error: error.message
    });
  }
};

// Get driver statistics
exports.getDriverStats = async (req, res) => {
  try {
    const driverId = req.driver.id;
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // Get today's completed orders
    const todayOrders = await Order.findAll({
      where: {
        DriverID: driverId,
        Status: 3, // Delivered
        OrderDate: {
          [Op.gte]: startOfDay,
          [Op.lt]: endOfDay
        }
      },
      include: [
        {
          model: Cart,
          attributes: ['CartTotalPrice']
        }
      ]
    });

    // Get total completed orders
    const totalOrders = await Order.count({
      where: {
        DriverID: driverId,
        Status: 3
      }
    });

    // Calculate today's earnings
    const driverModerate = await DriverModerate.findByPk(1);
    let todayEarnings = 0;

    todayOrders.forEach(order => {
      // Use pre-calculated driver profit from order model
      if (order.DriverProfit != null) {
        todayEarnings += parseFloat(order.DriverProfit);
      } else if (order.StartPointLatitude && order.StartPointLongitude &&
                 order.EndPointLatitude && order.EndPointLongitude) {
        // Fallback calculation for old orders
        const distance = calculateDistance(
          order.StartPointLatitude,
          order.StartPointLongitude,
          order.EndPointLatitude,
          order.EndPointLongitude
        );

        if (driverModerate.IsConst) {
          todayEarnings += driverModerate.ConstValue;
        } else {
          const totalDeliveryCost = distance * driverModerate.CostPerKilometer;
          const companyProfit = (totalDeliveryCost * driverModerate.CompanyProfitPercent) / 100;
          todayEarnings += totalDeliveryCost - companyProfit;
        }
      }
    });

    // Get driver wallet info
    const driver = await Driver.findByPk(driverId, {
      attributes: ['Wallet', 'LastWalletUpdate', 'Profit']
    });

    res.json({
      success: true,
      data: {
        todayDeliveries: todayOrders.length,
        todayEarnings: parseFloat(todayEarnings.toFixed(2)),
        totalDeliveries: totalOrders,
        currentWallet: parseFloat(driver.Wallet || 0),
        totalProfit: parseFloat(driver.Profit || 0),
        lastWalletUpdate: driver.LastWalletUpdate
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء جلب إحصائيات السائق",
      error: error.message
    });
  }
};

// Get driver wallet details
exports.getDriverWallet = async (req, res) => {
  try {
    const driverId = req.driver.id;

    const driver = await Driver.findByPk(driverId, {
      attributes: ['Wallet', 'LastWalletUpdate', 'Profit']
    });

    if (!driver) {
      return res.status(404).json({
        success: false,
        message: "السائق غير موجود"
      });
    }

    // Get recent invoices (without include for now to avoid relationship issues)
    const invoices = await DriverInvoices.findAll({
      where: {
        DriverID: driverId
      },
      order: [['Date', 'DESC']],
      limit: 10
    });

    res.json({
      success: true,
      data: {
        currentWallet: parseFloat(driver.Wallet || 0),
        totalProfit: parseFloat(driver.Profit || 0),
        lastUpdate: driver.LastWalletUpdate,
        recentInvoices: invoices
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء جلب تفاصيل المحفظة",
      error: error.message
    });
  }
};

// Get driver moderate settings (for news and profit calculations)
exports.getDriverModerate = async (req, res) => {
  try {
    const moderate = await DriverModerate.findByPk(1);

    if (!moderate) {
      return res.status(404).json({
        success: false,
        message: "إعدادات النظام غير موجودة"
      });
    }

    // Check if news is still valid
    const now = new Date();
    const hasValidNews = moderate.News &&
                        moderate.NewsExpireDate &&
                        new Date(moderate.NewsExpireDate) > now;

    res.json({
      success: true,
      data: {
        news: hasValidNews ? moderate.News : null,
        newsExpireDate: moderate.NewsExpireDate,
        companyProfitPercent: moderate.CompanyProfitPercent,
        isConst: moderate.IsConst,
        constValue: moderate.ConstValue,
        costPerKilometer: moderate.CostPerKilometer
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء جلب إعدادات النظام",
      error: error.message
    });
  }
};

// Update order status (for driver app)
exports.updateOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;
    const driverId = req.driver.id;

    const order = await Order.findOne({
      where: {
        OrderID: orderId,
        DriverID: driverId
      }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "الطلب غير موجود أو غير مُسند إليك"
      });
    }

    // Store old status to check if it changed
    const oldStatus = order.Status;

    await order.update({ Status: status });

    // Check if order is completed and calculate driver profit
    if (oldStatus !== status && (status === 3 || status === 5 || status === 7)) {
      console.log(`Order ${orderId} completed with status ${status}, calculating driver profit...`);

      try {
        // Import the profit calculation function from orderController
        const { calculateDriverProfit } = require('./orderController');
        const profitResult = await calculateDriverProfit(orderId, driverId);
        console.log('Driver profit calculated successfully:', profitResult);
      } catch (profitError) {
        console.error('Error calculating driver profit:', profitError);
        // Don't fail the order update if profit calculation fails
      }
    }

    res.json({
      success: true,
      message: "تم تحديث حالة الطلب بنجاح",
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "حدث خطأ أثناء تحديث حالة الطلب",
      error: error.message
    });
  }
};

module.exports = {
  getAllDrivers: exports.getAllDrivers,
  getDriverById: exports.getDriverById,
  updateDriverStatus: exports.updateDriverStatus,
  deleteDriver: exports.deleteDriver,
  getDriverOrders: exports.getDriverOrders,
  editDriver: exports.editDriver,
  receiveWallet: exports.receiveWallet,
  getInvoices: exports.getInvoices,
  getDriverInvoices: exports.getDriverInvoices,
  createDriver: exports.createDriver,
  updateDriverNews: exports.updateDriverNews,
  getDriverNews: exports.getDriverNews,
  getCompanyProfitPercent: exports.getCompanyProfitPercent,
  updateCompanyProfitPercent: exports.updateCompanyProfitPercent,
  searchDrivers: exports.searchDrivers,
  toggleDriverActivation: exports.toggleDriverActivation,
  getAvailableOrders: exports.getAvailableOrders,
  getMyOrders: exports.getMyOrders,
  acceptOrder: exports.acceptOrder,
  getActiveOrder: exports.getActiveOrder,
  updateDeliveryStatus: exports.updateDeliveryStatus,
  getDriverStats: exports.getDriverStats,
  getDriverWallet: exports.getDriverWallet,
  getDriverModerate: exports.getDriverModerate,
  updateOrderStatus: exports.updateOrderStatus,
};

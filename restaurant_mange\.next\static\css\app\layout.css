/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/globals.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/*
! tailwindcss v3.4.4 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #E5E7EB; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: Cairo, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9CA3AF; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before{
  border-style: solid;
  border-color: #e5e7eb;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before{
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before{
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before{
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before{
  border-top-width: 1px;
  border-right-width: 1px;
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow{
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow{
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow{
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow{
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before{
  visibility: hidden;
}

[data-popper-arrow],[data-popper-arrow]:before{
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

[data-popper-arrow]{
  visibility: hidden;
}

[data-popper-arrow]:before{
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-popper-arrow]:after{
  content: "";
  visibility: visible;
  transform: rotate(45deg);
  position: absolute;
  width: 9px;
  height: 9px;
  background: inherit;
}

[role="tooltip"] > [data-popper-arrow]:before{
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:before{
  border-style: solid;
  border-color: #4b5563;
}

[role="tooltip"] > [data-popper-arrow]:after{
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:after{
  border-style: solid;
  border-color: #4b5563;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before{
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after{
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before{
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after{
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before{
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after{
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before{
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after{
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]{
  bottom: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]{
  top: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]{
  right: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]{
  left: -5px;
}

[role="tooltip"].invisible > [data-popper-arrow]:before{
  visibility: hidden;
}

[role="tooltip"].invisible > [data-popper-arrow]:after{
  visibility: hidden;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select{
  appearance: none;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::placeholder,textarea::placeholder{
  color: #6B7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper{
  padding: 0;
}

::-webkit-date-and-time-value{
  min-height: 1.5em;
}

select:not([size]){
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/%3e %3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 0.75em 0.75em;
  padding-right: 2.5rem;
  print-color-adjust: exact;
}

:is([dir=rtl]) select:not([size]){
  background-position: left 0.75rem center;
  padding-right: 0.75rem;
  padding-left: 0;
}

[multiple]{
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  print-color-adjust: unset;
}

[type='checkbox'],[type='radio']{
  appearance: none;
  padding: 0;
  print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox']{
  border-radius: 0px;
}

[type='radio']{
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked{
  border-color: transparent;
  background-color: currentColor;
  background-size: 0.55em 0.55em;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked{
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  print-color-adjust: exact;
}

[type='radio']:checked{
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

.dark [type='radio']:checked{
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

[type='checkbox']:indeterminate{
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M0.5 6h14'/%3e %3c/svg%3e");
  background-color: currentColor;
  border-color: transparent;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  print-color-adjust: exact;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus{
  border-color: transparent;
  background-color: currentColor;
}

[type='file']{
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus{
  outline: 1px auto inherit;
}

input[type=file]::file-selector-button{
  color: white;
  background: #1F2937;
  border: 0;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 2rem;
  padding-right: 1rem;
  margin-inline-start: -1rem;
  margin-inline-end: 1rem;
}

input[type=file]::file-selector-button:hover{
  background: #374151;
}

:is([dir=rtl]) input[type=file]::file-selector-button{
  padding-right: 2rem;
  padding-left: 1rem;
}

.dark input[type=file]::file-selector-button{
  color: white;
  background: #4B5563;
}

.dark input[type=file]::file-selector-button:hover{
  background: #6B7280;
}

input[type="range"]::-webkit-slider-thumb{
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-webkit-slider-thumb{
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-webkit-slider-thumb{
  background: #6B7280;
}

input[type="range"]:focus::-webkit-slider-thumb{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1px;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

input[type="range"]::-moz-range-thumb{
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-moz-range-thumb{
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-moz-range-thumb{
  background: #6B7280;
}

input[type="range"]::-moz-range-progress{
  background: #3F83F8;
}

input[type="range"]::-ms-fill-lower{
  background: #3F83F8;
}

html {
    font-family: 'Cairo', sans-serif;
  }

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 30px;
  padding-left: 30px;
}
@media (min-width: 640px){

  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){

  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){

  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){

  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){

  .container{
    max-width: 1536px;
  }
}
.container{
  max-width: 1200px;
}
.landingpage .container{
  max-width: 1320px;
}
body{
  overflow-x: hidden;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
body:is(.dark *){
  background-color: var(--color-dark);
}
.dropdown{
  position: relative;
  border-radius: 7px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0px 1px 4px 0px rgba(133, 146, 173, 0.2);
  --tw-shadow-colored: 0px 1px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dropdown:is(.dark *){
  background-color: var(--color-dark);
  --tw-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px, rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
  --tw-shadow-colored: 0px 0px 2px 0px var(--tw-shadow-color), 0px 12px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.card-title{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: var(--color-dark);
}
.card-title:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.card-subtitle{
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}
.card-subtitle:is(.dark *){
  color: var(--color-darklink);
}
body{
  color: var(--color-darklink);
}
/*heading text color*/
h1,
  h2,
  h3,
  h4,
  h5,
  h6{
  font-weight: 600;
  color: var(--color-dark);
}
h1:is(.dark *),
  h2:is(.dark *),
  h3:is(.dark *),
  h4:is(.dark *),
  h5:is(.dark *),
  h6:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.border-ld{
  border-color: var(--color-border);
}
.border-ld:is(.dark *){
  border-color: var(--color-darkborder);
}
.bg-hover:hover{
  background-color: var(--color-lighthover);
}
.bg-hover:is(.dark *):hover{
  background-color: var( --color-darkmuted);
}
.form-control input{
  width: 100%;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-control input:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.form-control-chat input{
  width: 100%;
  border-radius: 9px;
  border-width: 0px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-control-chat input:is(.dark *){
  background-color: transparent;
}
.form-control-chat input:focus{
  width: 100%;
  border-width: 0px !important;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-ring-color: transparent;
}
.form-control-chat input:focus:is(.dark *){
  background-color: transparent;
  --tw-ring-color: transparent;
}
.form-control-rounded input{
  width: 100%;
  border-radius: 9999px;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-control-rounded input:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.form-control-rounded input:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.form-control-rounded input:focus:is(.dark *){
  border-color: var(--color-primary);
}
.form-control-textarea{
  width: 100%;
  border-radius: 9999px;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  padding: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-control-textarea:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.form-control-textarea:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.form-control-textarea:focus:is(.dark *){
  border-color: var(--color-primary);
}
.form-control input:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.form-control input:focus:is(.dark *){
  border-color: var(--color-primary);
}
.form-control-input{
  width: 100%;
  border-radius: 9px;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-control-input:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.form-control-input:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.form-control-input:focus:is(.dark *){
  border-color: var(--color-primary);
}
.form-rounded-md input{
  width: 100%;
  border-radius: 9px;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.form-rounded-md input:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.form-rounded-md input:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.form-rounded-md input:focus:is(.dark *){
  border-color: var(--color-primary);
}
.form-rounded input{
  border-radius: 9999px;
}
.elipse{
  height: 10px;
  width: 18px;
}
input::placeholder{
  color: var(--color-darklink);
}
input:is(.dark *)::placeholder{
  color: var(--color-darklink);
}
.select-option select{
  width: auto;
  border-width: 0px;
  background-color: var(--color-muted);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-inline-start: 1rem;
  padding-inline-end: 2.25rem;
  font-weight: 500;
  color: var(--color-darklink);
}
.select-option select:focus{
  border-width: 0px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.select-option select:is(.dark *){
  color: rgb(255 255 255 / 0.8);
}
.select-md select{
  border-color: var(--color-border);
}
.select-md select:is(.dark *){
  border-color: var(--color-darkborder);
}
.select-md select{
  width: 100%;
  border-radius: 9px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.select-md select:focus{
  border-color: var(--color-primary);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.select-md select:is(.dark *){
  background-color: var(--color-darkgray);
}
.select-md select:focus:is(.dark *){
  border-color: var(--color-primary);
}
.select-rounded select{
  border-color: var(--color-border);
}
.select-rounded select:is(.dark *){
  border-color: var(--color-darkborder);
}
.select-rounded select{
  width: 100%;
  border-radius: 9999px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.select-rounded select:focus{
  border-color: var(--color-primary);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.select-rounded select:is(.dark *){
  background-color: var(--color-darkgray);
}
.select-rounded select:focus:is(.dark *){
  border-color: var(--color-primary);
}
.select-rounded-transparent select{
  border-color: var(--color-border);
}
.select-rounded-transparent select:is(.dark *){
  border-color: var(--color-darkborder);
}
.select-rounded-transparent select{
  width: 100%;
  border-radius: 9999px;
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.select-rounded-transparent select:focus{
  border-color: var(--color-primary);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.select-rounded-transparent select:is(.dark *){
  background-color: rgb(31 41 55 / 0.7);
}
.select-rounded-transparent select:focus:is(.dark *){
  border-color: var(--color-primary);
}
.checkbox{
  height: 18px;
  width: 18px;
  border-width: 1px;
  border-color: var(--color-bordergray);
  background-color: transparent;
}
.checkbox:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}
.checkbox:is(.dark *){
  border-color: var(--color-darkborder);
}
.text-primary-ld:hover{
  color: var(--color-primary);
}
.text-primary-ld:hover:is(.dark *){
  color: var(--color-primary);
}
/* Apps */
.left-part{
  border-color: var(--color-border);
}
.left-part:is(.dark *){
  border-color: var(--color-darkborder);
}
.left-part{
  width: 20rem;
  border-inline-end-width: 1px;
  padding: 1.5rem;
}
.btn-circle{
  color: var(--color-dark);
}
.btn-circle:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-circle{
  display: flex;
  height: 2rem;
  width: 2rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  padding: 0px;
}
.btn-circle-hover{
  display: flex;
  height: 2.25rem;
  width: 2.25rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: transparent;
}
.btn-circle-hover:hover{
  background-color: var(--color-lightprimary);
  color: var(--color-primary);
}
.text-ld{
  color: var(--color-dark);
}
.text-ld:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.sorting button{
  background-color: transparent;
  padding: 0px;
  color: var(--color-dark);
}
.sorting button:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.sorting button:hover{
  background-color: transparent;
}
.sorting button span{
  padding: 0px;
}
.sorting ul li{
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.sorting ul li button:hover:hover{
  color: var(--color-primary);
}
/* Button Hover Color Set */
button.bg-primary:hover{
  background-color: var(--color-primary-emphasis);
}
button.bg-secondary:hover{
  background-color: var(--color-secondary-emphasis);
}
button.bg-info:hover{
  background-color: var(--color-info-emphasis);
}
button.bg-error:hover{
  background-color: var(--color-error-emphasis);
}
button.bg-success:hover{
  background-color: var(--color-success-emphasis);
}
button.bg-warning:hover{
  background-color: var(--color-warning-emphasis);
}
/* Headlessui */
.ui-button{
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  border-radius: 9999px;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  text-align: center;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ui-button-small{
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  border-radius: 9999px;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  text-align: center;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ui-dropdown{
  color: var(--color-dark);
}
.ui-dropdown:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ui-dropdown{
  width: 100%;
  max-width: 14rem !important;
  border-radius: 7px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  text-align: start;
  --tw-shadow: 0px 1px 4px 0px rgba(133, 146, 173, 0.2);
  --tw-shadow-colored: 0px 1px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.ui-dropdown:is(.dark *){
  background-color: var(--color-dark);
  --tw-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px, rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
  --tw-shadow-colored: 0px 0px 2px 0px var(--tw-shadow-color), 0px 12px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.ui-dropdown-item:hover{
  background-color: var(--color-lighthover);
}
.ui-dropdown-item:is(.dark *):hover{
  background-color: var( --color-darkmuted);
}
.ui-dropdown-item{
  color: var(--color-dark);
}
.ui-dropdown-item:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ui-dropdown-item{
  display: flex;
  width: 100%;
  cursor: pointer;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.ui-dropdown-item:focus:hover{
  background-color: var(--color-lighthover);
}
.ui-dropdown-item:focus:is(.dark *):hover{
  background-color: var( --color-darkmuted);
}
.ui-dropdown-item:hover{
  color: var(--color-primary);
}
.ui-dropdown-item:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ui-dropdown-animation{
  transform-origin: top right;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 100ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  --anchor-gap: var(--spacing-1);
}
.ui-dropdown-animation:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ui-dropdown-animation[data-closed]{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
}
.ui-form-control{
  width: 100%;
  border-width: 1px;
  border-color: var(--color-border);
  background-color: transparent;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.ui-form-control:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.ui-form-control:focus{
  border-color: var(--color-primary);
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0px;
}
.ui-form-control:focus:is(.dark *){
  border-color: var(--color-primary);
  --tw-ring-color: transparent;
}
.ui-form-control:focus:is(.dark *):focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ui-checkbox{
  height: 18px;
  width: 18px;
  cursor: pointer;
  border-radius: 0.25rem;
  border-width: 1px;
  border-color: var(--color-bordergray);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ui-checkbox[data-checked]button:hover{
  background-color: var(--color-primary-emphasis);
}
.ui-checkbox[data-checked]:is(.dark *)button:hover{
  background-color: var(--color-primary-emphasis);
}
.ui-checkbox:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 0px;
}
.ui-checkbox[data-checked]{
  background-color: var(--color-primary);
}
.ui-checkbox:is(.dark *){
  border-color: var(--color-darkborder);
  background-color: transparent;
}
.ui-checkbox[data-checked]:is(.dark *){
  background-color: var(--color-primary);
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.collapse{
  visibility: collapse;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-2{
  inset: 0.5rem;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-bottom-1{
  bottom: -0.25rem;
}
.-end-10{
  inset-inline-end: -2.5rem;
}
.-left-1{
  left: -0.25rem;
}
.-left-1\.5{
  left: -0.375rem;
}
.-left-3{
  left: -0.75rem;
}
.-left-9{
  left: -2.25rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-start-10{
  inset-inline-start: -2.5rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-2{
  top: -0.5rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1{
  bottom: 0.25rem;
}
.bottom-16{
  bottom: 4rem;
}
.bottom-3{
  bottom: 0.75rem;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-5{
  bottom: 1.25rem;
}
.bottom-6{
  bottom: 1.5rem;
}
.bottom-8{
  bottom: 2rem;
}
.end-0{
  inset-inline-end: 0px;
}
.end-1{
  inset-inline-end: 0.25rem;
}
.end-2{
  inset-inline-end: 0.5rem;
}
.end-2\.5{
  inset-inline-end: 0.625rem;
}
.end-3{
  inset-inline-end: 0.75rem;
}
.end-4{
  inset-inline-end: 1rem;
}
.end-5{
  inset-inline-end: 1.25rem;
}
.end-6{
  inset-inline-end: 1.5rem;
}
.left-0{
  left: 0px;
}
.left-1{
  left: 0.25rem;
}
.left-1\/2{
  left: 50%;
}
.left-1\/4{
  left: 25%;
}
.left-2{
  left: 0.5rem;
}
.left-2\.5{
  left: 0.625rem;
}
.left-3{
  left: 0.75rem;
}
.left-4{
  left: 1rem;
}
.left-5{
  left: 1.25rem;
}
.left-6{
  left: 1.5rem;
}
.left-\[64px\]{
  left: 64px;
}
.left-full{
  left: 100%;
}
.right-0{
  right: 0px;
}
.right-2{
  right: 0.5rem;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.right-6{
  right: 1.5rem;
}
.start-0{
  inset-inline-start: 0px;
}
.start-3{
  inset-inline-start: 0.75rem;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-1\/2{
  top: 50%;
}
.top-10{
  top: 2.5rem;
}
.top-2{
  top: 0.5rem;
}
.top-2\.5{
  top: 0.625rem;
}
.top-24{
  top: 6rem;
}
.top-3{
  top: 0.75rem;
}
.top-4{
  top: 1rem;
}
.top-6{
  top: 1.5rem;
}
.top-\[4px\]{
  top: 4px;
}
.top-\[5px\]{
  top: 5px;
}
.top-full{
  top: 100%;
}
.-z-10{
  z-index: -10;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[10\]{
  z-index: 10;
}
.z-\[20\]{
  z-index: 20;
}
.z-\[50\]{
  z-index: 50;
}
.z-\[5\]{
  z-index: 5;
}
.z-\[60\]{
  z-index: 60;
}
.z-auto{
  z-index: auto;
}
.order-1{
  order: 1;
}
.order-2{
  order: 2;
}
.order-3{
  order: 3;
}
.col-span-1{
  grid-column: span 1 / span 1;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-span-3{
  grid-column: span 3 / span 3;
}
.col-span-4{
  grid-column: span 4 / span 4;
}
.col-span-5{
  grid-column: span 5 / span 5;
}
.col-span-6{
  grid-column: span 6 / span 6;
}
.col-span-7{
  grid-column: span 7 / span 7;
}
.col-span-8{
  grid-column: span 8 / span 8;
}
.col-span-9{
  grid-column: span 9 / span 9;
}
.-m-1{
  margin: -0.25rem;
}
.-m-1\.5{
  margin: -0.375rem;
}
.m-4{
  margin: 1rem;
}
.-mx-6{
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-1\.5{
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-6{
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-0{
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5{
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-7{
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-mb-4{
  margin-bottom: -1rem;
}
.-mb-px{
  margin-bottom: -1px;
}
.-me-2{
  margin-inline-end: -0.5rem;
}
.-me-4{
  margin-inline-end: -1rem;
}
.-me-6{
  margin-inline-end: -1.5rem;
}
.-mr-1{
  margin-right: -0.25rem;
}
.-ms-2{
  margin-inline-start: -0.5rem;
}
.-ms-3{
  margin-inline-start: -0.75rem;
}
.-ms-4{
  margin-inline-start: -1rem;
}
.-ms-6{
  margin-inline-start: -1.5rem;
}
.-mt-1{
  margin-top: -0.25rem;
}
.-mt-2{
  margin-top: -0.5rem;
}
.-mt-20{
  margin-top: -5rem;
}
.-mt-4{
  margin-top: -1rem;
}
.-mt-5{
  margin-top: -1.25rem;
}
.-mt-6{
  margin-top: -1.5rem;
}
.-mt-7{
  margin-top: -1.75rem;
}
.-mt-8{
  margin-top: -2rem;
}
.mb-0{
  margin-bottom: 0px;
}
.mb-0\.5{
  margin-bottom: 0.125rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-14{
  margin-bottom: 3.5rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-30{
  margin-bottom: 30px;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-7{
  margin-bottom: 1.75rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.mb-\[0\.375rem\]{
  margin-bottom: 0.375rem;
}
.mb-\[30px\]{
  margin-bottom: 30px;
}
.me-0{
  margin-inline-end: 0px;
}
.me-0\.5{
  margin-inline-end: 0.125rem;
}
.me-1{
  margin-inline-end: 0.25rem;
}
.me-1\.5{
  margin-inline-end: 0.375rem;
}
.me-2{
  margin-inline-end: 0.5rem;
}
.me-2\.5{
  margin-inline-end: 0.625rem;
}
.me-3{
  margin-inline-end: 0.75rem;
}
.me-4{
  margin-inline-end: 1rem;
}
.me-5{
  margin-inline-end: 1.25rem;
}
.me-6{
  margin-inline-end: 1.5rem;
}
.ml-0{
  margin-left: 0px;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-1\.5{
  margin-left: 0.375rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-6{
  margin-left: 1.5rem;
}
.ml-64{
  margin-left: 16rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-4{
  margin-right: 1rem;
}
.mr-auto{
  margin-right: auto;
}
.ms-1{
  margin-inline-start: 0.25rem;
}
.ms-1\.5{
  margin-inline-start: 0.375rem;
}
.ms-2{
  margin-inline-start: 0.5rem;
}
.ms-3{
  margin-inline-start: 0.75rem;
}
.ms-3\.5{
  margin-inline-start: 0.875rem;
}
.ms-auto{
  margin-inline-start: auto;
}
.mt-0{
  margin-top: 0px;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-14{
  margin-top: 3.5rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-2\.5{
  margin-top: 0.625rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-30{
  margin-top: 30px;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-7{
  margin-top: 1.75rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-\[1\.875rem\]{
  margin-top: 1.875rem;
}
.mt-\[30px\]{
  margin-top: 30px;
}
.mt-auto{
  margin-top: auto;
}
.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.\!inline{
  display: inline !important;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.\!table{
  display: table !important;
}
.table{
  display: table;
}
.flow-root{
  display: flow-root;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-\[3\/2\]{
  aspect-ratio: 3/2;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.size-2{
  width: 0.5rem;
  height: 0.5rem;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.size-5{
  width: 1.25rem;
  height: 1.25rem;
}
.size-6{
  width: 1.5rem;
  height: 1.5rem;
}
.size-\[6\.25rem\]{
  width: 6.25rem;
  height: 6.25rem;
}
.size-full{
  width: 100%;
  height: 100%;
}
.h-0{
  height: 0px;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-32{
  height: 8rem;
}
.h-36{
  height: 9rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-56{
  height: 14rem;
}
.h-6{
  height: 1.5rem;
}
.h-60{
  height: 15rem;
}
.h-64{
  height: 16rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-80{
  height: 20rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-\[10px\]{
  height: 10px;
}
.h-\[150px\]{
  height: 150px;
}
.h-\[18px\]{
  height: 18px;
}
.h-\[220px\]{
  height: 220px;
}
.h-\[240px\]{
  height: 240px;
}
.h-\[265px\]{
  height: 265px;
}
.h-\[300px\]{
  height: 300px;
}
.h-\[30px\]{
  height: 30px;
}
.h-\[32px\]{
  height: 32px;
}
.h-\[360px\]{
  height: 360px;
}
.h-\[400px\]{
  height: 400px;
}
.h-\[48px\]{
  height: 48px;
}
.h-\[600px\]{
  height: 600px;
}
.h-\[60px\]{
  height: 60px;
}
.h-\[650px\]{
  height: 650px;
}
.h-\[6px\]{
  height: 6px;
}
.h-\[calc\(100vh_-_100px\)\]{
  height: calc(100vh - 100px);
}
.h-\[calc\(100vh_-_1rem\)\]{
  height: calc(100vh - 1rem);
}
.h-auto{
  height: auto;
}
.h-fit{
  height: fit-content;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-80{
  max-height: 20rem;
}
.max-h-\[378px\]{
  max-height: 378px;
}
.max-h-\[440px\]{
  max-height: 440px;
}
.max-h-\[450px\]{
  max-height: 450px;
}
.max-h-\[48px\]{
  max-height: 48px;
}
.max-h-\[500px\]{
  max-height: 500px;
}
.max-h-\[580px\]{
  max-height: 580px;
}
.max-h-\[600px\]{
  max-height: 600px;
}
.max-h-\[700px\]{
  max-height: 700px;
}
.max-h-\[70vh\]{
  max-height: 70vh;
}
.max-h-\[75vh\]{
  max-height: 75vh;
}
.max-h-\[800px\]{
  max-height: 800px;
}
.max-h-\[90dvh\]{
  max-height: 90dvh;
}
.min-h-\[200px\]{
  min-height: 200px;
}
.min-h-\[300px\]{
  min-height: 300px;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-\[40px\]{
  min-height: 40px;
}
.min-h-\[50vh\]{
  min-height: 50vh;
}
.min-h-\[60vh\]{
  min-height: 60vh;
}
.min-h-\[80vh\]{
  min-height: 80vh;
}
.min-h-\[900px\]{
  min-height: 900px;
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.\!w-80{
  width: 20rem !important;
}
.\!w-auto{
  width: auto !important;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-1{
  width: 0.25rem;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/2{
  width: 50%;
}
.w-1\/4{
  width: 25%;
}
.w-1\/6{
  width: 16.666667%;
}
.w-10{
  width: 2.5rem;
}
.w-11{
  width: 2.75rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\/4{
  width: 50%;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-32{
  width: 8rem;
}
.w-36{
  width: 9rem;
}
.w-4{
  width: 1rem;
}
.w-40{
  width: 10rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-52{
  width: 13rem;
}
.w-56{
  width: 14rem;
}
.w-6{
  width: 1.5rem;
}
.w-60{
  width: 15rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-96{
  width: 24rem;
}
.w-\[140px\]{
  width: 140px;
}
.w-\[150px\]{
  width: 150px;
}
.w-\[18px\]{
  width: 18px;
}
.w-\[30px\]{
  width: 30px;
}
.w-\[48px\]{
  width: 48px;
}
.w-\[60px\]{
  width: 60px;
}
.w-\[64px\]{
  width: 64px;
}
.w-\[6px\]{
  width: 6px;
}
.w-\[70\%\]{
  width: 70%;
}
.w-\[calc\(100\%-2rem\)\]{
  width: calc(100% - 2rem);
}
.w-\[var\(--input-width\)\]{
  width: var(--input-width);
}
.w-auto{
  width: auto;
}
.w-fit{
  width: fit-content;
}
.w-full{
  width: 100%;
}
.w-max{
  width: max-content;
}
.w-screen{
  width: 100vw;
}
.min-w-0{
  min-width: 0px;
}
.min-w-10{
  min-width: 2.5rem;
}
.min-w-11{
  min-width: 2.75rem;
}
.min-w-12{
  min-width: 3rem;
}
.min-w-14{
  min-width: 3.5rem;
}
.min-w-32{
  min-width: 8rem;
}
.min-w-44{
  min-width: 11rem;
}
.min-w-9{
  min-width: 2.25rem;
}
.min-w-\[100px\]{
  min-width: 100px;
}
.min-w-\[1300px\]{
  min-width: 1300px;
}
.min-w-\[200px\]{
  min-width: 200px;
}
.min-w-\[250px\]{
  min-width: 250px;
}
.min-w-\[300px\]{
  min-width: 300px;
}
.min-w-\[50\%\]{
  min-width: 50%;
}
.min-w-\[80px\]{
  min-width: 80px;
}
.min-w-\[90px\]{
  min-width: 90px;
}
.min-w-full{
  min-width: 100%;
}
.\!max-w-80{
  max-width: 20rem !important;
}
.\!max-w-full{
  max-width: 100% !important;
}
.max-w-0{
  max-width: 0px;
}
.max-w-12{
  max-width: 3rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-32{
  max-width: 8rem;
}
.max-w-36{
  max-width: 9rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-52{
  max-width: 13rem;
}
.max-w-56{
  max-width: 14rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-6xl{
  max-width: 72rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-\[0\]{
  max-width: 0;
}
.max-w-\[100vw\]{
  max-width: 100vw;
}
.max-w-\[200px\]{
  max-width: 200px;
}
.max-w-\[235px\]{
  max-width: 235px;
}
.max-w-\[250px\]{
  max-width: 250px;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-\[350px\]{
  max-width: 350px;
}
.max-w-\[48px\]{
  max-width: 48px;
}
.max-w-\[800px\]{
  max-width: 800px;
}
.max-w-fit{
  max-width: fit-content;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-screen-md{
  max-width: 768px;
}
.max-w-screen-xl{
  max-width: 1280px;
}
.max-w-sm{
  max-width: 24rem;
}
.max-w-xl{
  max-width: 36rem;
}
.max-w-xs{
  max-width: 20rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.grow{
  flex-grow: 1;
}
.basis-1\/4{
  flex-basis: 25%;
}
.basis-3\/4{
  flex-basis: 75%;
}
.table-auto{
  table-layout: auto;
}
.origin-\[0\]{
  transform-origin: 0;
}
.origin-top{
  transform-origin: top;
}
.-translate-x-1{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-16{
  --tw-translate-x: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-12{
  --tw-translate-y: -3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-16{
  --tw-translate-y: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4{
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-6{
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full{
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-12{
  --tw-translate-x: 3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full{
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[-8deg\]{
  --tw-rotate: -8deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-125{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-75{
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none{
  transform: none;
}
@keyframes bounce{

  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce{
  animation: bounce 1s infinite;
}
@keyframes ping{

  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-grab{
  cursor: grab;
}
.cursor-help{
  cursor: help;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.cursor-wait{
  cursor: wait;
}
.cursor-zoom-in{
  cursor: zoom-in;
}
.cursor-zoom-out{
  cursor: zoom-out;
}
.snap-x{
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}
.snap-mandatory{
  --tw-scroll-snap-strictness: mandatory;
}
.snap-center{
  scroll-snap-align: center;
}
.list-inside{
  list-style-position: inside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.list-none{
  list-style-type: none;
}
.appearance-none{
  appearance: none;
}
.grid-flow-col{
  grid-auto-flow: column;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5{
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6{
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7{
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-9{
  grid-template-columns: repeat(9, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.items-stretch{
  align-items: stretch;
}
.\!justify-start{
  justify-content: flex-start !important;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.justify-around{
  justify-content: space-around;
}
.gap-0{
  gap: 0px;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-10{
  gap: 2.5rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-3\.5{
  gap: 0.875rem;
}
.gap-30{
  gap: 30px;
}
.gap-4{
  gap: 1rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-7{
  gap: 1.75rem;
}
.gap-8{
  gap: 2rem;
}
.gap-\[1\.25rem\]{
  gap: 1.25rem;
}
.gap-\[1\.875rem\]{
  gap: 1.875rem;
}
.gap-\[10px\]{
  gap: 10px;
}
.gap-\[15px\]{
  gap: 15px;
}
.gap-\[1rem\]{
  gap: 1rem;
}
.gap-\[24px\]{
  gap: 24px;
}
.gap-\[30px\]{
  gap: 30px;
}
.gap-x-3{
  column-gap: 0.75rem;
}
.-space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-1rem * var(--tw-space-x-reverse));
  margin-left: calc(-1rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-px > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-12 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-2\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
.divide-x > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(2px * var(--tw-divide-x-reverse));
  border-left-width: calc(2px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
}
.divide-border > :not([hidden]) ~ :not([hidden]){
  border-color: var(--color-border);
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}
.divide-gray-500 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-divide-opacity));
}
.self-start{
  align-self: flex-start;
}
.self-end{
  align-self: flex-end;
}
.self-center{
  align-self: center;
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.\!overflow-x-hidden{
  overflow-x: hidden !important;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.overflow-y-hidden{
  overflow-y: hidden;
}
.overflow-x-scroll{
  overflow-x: scroll;
}
.\!scroll-auto{
  scroll-behavior: auto !important;
}
.scroll-smooth{
  scroll-behavior: smooth;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.text-wrap{
  text-wrap: wrap;
}
.break-words{
  overflow-wrap: break-word;
}
.\!rounded-none{
  border-radius: 0px !important;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-\[16px\]{
  border-radius: 16px;
}
.rounded-\[7px\]{
  border-radius: 7px;
}
.rounded-\[8px\]{
  border-radius: 8px;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 24px;
}
.rounded-md{
  border-radius: 9px;
}
.rounded-none{
  border-radius: 0px;
}
.rounded-sm{
  border-radius: 7px;
}
.rounded-tw{
  border-radius: 12px;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.\!rounded-e-none{
  border-start-end-radius: 0px !important;
  border-end-end-radius: 0px !important;
}
.\!rounded-s-none{
  border-start-start-radius: 0px !important;
  border-end-start-radius: 0px !important;
}
.rounded-b{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-b-2xl{
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-b-md{
  border-bottom-right-radius: 9px;
  border-bottom-left-radius: 9px;
}
.rounded-e-lg{
  border-start-end-radius: 24px;
  border-end-end-radius: 24px;
}
.rounded-e-md{
  border-start-end-radius: 9px;
  border-end-end-radius: 9px;
}
.rounded-e-none{
  border-start-end-radius: 0px;
  border-end-end-radius: 0px;
}
.rounded-l-lg{
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.rounded-l-md{
  border-top-left-radius: 9px;
  border-bottom-left-radius: 9px;
}
.rounded-l-none{
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-lg{
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.rounded-r-md{
  border-top-right-radius: 9px;
  border-bottom-right-radius: 9px;
}
.rounded-r-none{
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rounded-s-lg{
  border-start-start-radius: 24px;
  border-end-start-radius: 24px;
}
.rounded-s-md{
  border-start-start-radius: 9px;
  border-end-start-radius: 9px;
}
.rounded-s-none{
  border-start-start-radius: 0px;
  border-end-start-radius: 0px;
}
.rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-2xl{
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-3xl{
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}
.rounded-t-lg{
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}
.rounded-t-md{
  border-top-left-radius: 9px;
  border-top-right-radius: 9px;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-8{
  border-width: 8px;
}
.border-\[1px\]{
  border-width: 1px;
}
.\!border-x-0{
  border-left-width: 0px !important;
  border-right-width: 0px !important;
}
.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-0{
  border-bottom-width: 0px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-e{
  border-inline-end-width: 1px;
}
.border-e-0{
  border-inline-end-width: 0px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-0{
  border-left-width: 0px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-0{
  border-right-width: 0px;
}
.border-r-4{
  border-right-width: 4px;
}
.border-s{
  border-inline-start-width: 1px;
}
.border-s-2{
  border-inline-start-width: 2px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-t-4{
  border-top-width: 4px;
}
.border-solid{
  border-style: solid;
}
.border-dashed{
  border-style: dashed;
}
.border-none{
  border-style: none;
}
.\!border-gray-200{
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}
.border-amber-100{
  --tw-border-opacity: 1;
  border-color: rgb(254 243 199 / var(--tw-border-opacity));
}
.border-amber-200{
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity));
}
.border-amber-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity));
}
.border-amber-400\/30{
  border-color: rgb(251 191 36 / 0.3);
}
.border-amber-500{
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity));
}
.border-amber-600{
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity));
}
.border-black\/10{
  border-color: rgb(0 0 0 / 0.1);
}
.border-black\/5{
  border-color: rgb(0 0 0 / 0.05);
}
.border-blue-100{
  --tw-border-opacity: 1;
  border-color: rgb(225 239 254 / var(--tw-border-opacity));
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(195 221 253 / var(--tw-border-opacity));
}
.border-blue-400{
  --tw-border-opacity: 1;
  border-color: rgb(118 169 250 / var(--tw-border-opacity));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}
.border-blue-600{
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity));
}
.border-border{
  border-color: var(--color-border);
}
.border-bordergray{
  border-color: var(--color-bordergray);
}
.border-cyan-300{
  --tw-border-opacity: 1;
  border-color: rgb(103 232 249 / var(--tw-border-opacity));
}
.border-cyan-500{
  border-color: var(--color-primary);
}
.border-cyan-600{
  border-color: var(--color-primary);
}
.border-cyan-700{
  border-color: var(--color-primary);
}
.border-dark{
  border-color: var(--color-dark);
}
.border-darkborder{
  border-color: var(--color-darkborder);
}
.border-error{
  border-color: var(--color-error);
}
.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-gray-400{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}
.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
.border-gray-600{
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
.border-gray-900{
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity));
}
.border-green-100{
  --tw-border-opacity: 1;
  border-color: rgb(222 247 236 / var(--tw-border-opacity));
}
.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(188 240 218 / var(--tw-border-opacity));
}
.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(132 225 188 / var(--tw-border-opacity));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(14 159 110 / var(--tw-border-opacity));
}
.border-green-600{
  --tw-border-opacity: 1;
  border-color: rgb(5 122 85 / var(--tw-border-opacity));
}
.border-green-700{
  --tw-border-opacity: 1;
  border-color: rgb(4 108 78 / var(--tw-border-opacity));
}
.border-indigo-200{
  --tw-border-opacity: 1;
  border-color: rgb(205 219 254 / var(--tw-border-opacity));
}
.border-indigo-300{
  --tw-border-opacity: 1;
  border-color: rgb(180 198 252 / var(--tw-border-opacity));
}
.border-indigo-400{
  --tw-border-opacity: 1;
  border-color: rgb(141 162 251 / var(--tw-border-opacity));
}
.border-indigo-500{
  --tw-border-opacity: 1;
  border-color: rgb(104 117 245 / var(--tw-border-opacity));
}
.border-indigo-600{
  --tw-border-opacity: 1;
  border-color: rgb(88 80 236 / var(--tw-border-opacity));
}
.border-info{
  border-color: var(--color-info);
}
.border-lime-300{
  --tw-border-opacity: 1;
  border-color: rgb(190 242 100 / var(--tw-border-opacity));
}
.border-lime-400{
  --tw-border-opacity: 1;
  border-color: rgb(163 230 53 / var(--tw-border-opacity));
}
.border-lime-500{
  --tw-border-opacity: 1;
  border-color: rgb(132 204 22 / var(--tw-border-opacity));
}
.border-orange-100{
  --tw-border-opacity: 1;
  border-color: rgb(254 236 220 / var(--tw-border-opacity));
}
.border-orange-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 186 140 / var(--tw-border-opacity));
}
.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(255 90 31 / var(--tw-border-opacity));
}
.border-pink-300{
  --tw-border-opacity: 1;
  border-color: rgb(248 180 217 / var(--tw-border-opacity));
}
.border-pink-500{
  --tw-border-opacity: 1;
  border-color: rgb(231 70 148 / var(--tw-border-opacity));
}
.border-pink-600{
  --tw-border-opacity: 1;
  border-color: rgb(214 31 105 / var(--tw-border-opacity));
}
.border-primary{
  border-color: var(--color-primary);
}
.border-purple-400{
  --tw-border-opacity: 1;
  border-color: rgb(172 148 250 / var(--tw-border-opacity));
}
.border-purple-500{
  --tw-border-opacity: 1;
  border-color: rgb(144 97 249 / var(--tw-border-opacity));
}
.border-purple-900{
  --tw-border-opacity: 1;
  border-color: rgb(74 29 150 / var(--tw-border-opacity));
}
.border-red-100{
  --tw-border-opacity: 1;
  border-color: rgb(253 232 232 / var(--tw-border-opacity));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(251 213 213 / var(--tw-border-opacity));
}
.border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(248 180 180 / var(--tw-border-opacity));
}
.border-red-400{
  --tw-border-opacity: 1;
  border-color: rgb(249 128 128 / var(--tw-border-opacity));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}
.border-red-600{
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}
.border-red-900{
  --tw-border-opacity: 1;
  border-color: rgb(119 29 29 / var(--tw-border-opacity));
}
.border-secondary{
  border-color: var(--color-secondary);
}
.border-success{
  border-color: var(--color-success);
}
.border-teal-300{
  --tw-border-opacity: 1;
  border-color: rgb(126 220 226 / var(--tw-border-opacity));
}
.border-teal-500{
  --tw-border-opacity: 1;
  border-color: rgb(6 148 162 / var(--tw-border-opacity));
}
.border-transparent{
  border-color: transparent;
}
.border-warning{
  border-color: var(--color-warning);
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-white\/10{
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/20{
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30{
  border-color: rgb(255 255 255 / 0.3);
}
.border-white\/40{
  border-color: rgb(255 255 255 / 0.4);
}
.border-white\/50{
  border-color: rgb(255 255 255 / 0.5);
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(252 233 106 / var(--tw-border-opacity));
}
.border-yellow-300{
  --tw-border-opacity: 1;
  border-color: rgb(250 202 21 / var(--tw-border-opacity));
}
.border-yellow-400{
  --tw-border-opacity: 1;
  border-color: rgb(227 160 8 / var(--tw-border-opacity));
}
.border-yellow-500{
  --tw-border-opacity: 1;
  border-color: rgb(194 120 3 / var(--tw-border-opacity));
}
.border-yellow-600{
  --tw-border-opacity: 1;
  border-color: rgb(159 88 10 / var(--tw-border-opacity));
}
.border-t-amber-500{
  --tw-border-opacity: 1;
  border-top-color: rgb(245 158 11 / var(--tw-border-opacity));
}
.border-t-gray-600{
  --tw-border-opacity: 1;
  border-top-color: rgb(75 85 99 / var(--tw-border-opacity));
}
.border-t-transparent{
  border-top-color: transparent;
}
.\!bg-transparent{
  background-color: transparent !important;
}
.bg-amber-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity));
}
.bg-amber-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity));
}
.bg-amber-50\/50{
  background-color: rgb(255 251 235 / 0.5);
}
.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
.bg-amber-500\/90{
  background-color: rgb(245 158 11 / 0.9);
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-black\/10{
  background-color: rgb(0 0 0 / 0.1);
}
.bg-black\/15{
  background-color: rgb(0 0 0 / 0.15);
}
.bg-black\/30{
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60{
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/70{
  background-color: rgb(0 0 0 / 0.7);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(225 239 254 / var(--tw-bg-opacity));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(235 245 255 / var(--tw-bg-opacity));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(63 131 248 / var(--tw-bg-opacity));
}
.bg-blue-500\/10{
  background-color: rgb(63 131 248 / 0.1);
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}
.bg-blue-700{
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}
.bg-border{
  background-color: var(--color-border);
}
.bg-cyan-100{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity));
}
.bg-cyan-200{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
}
.bg-cyan-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity));
}
.bg-cyan-500{
  background-color: var(--color-primary);
}
.bg-cyan-600{
  background-color: var(--color-primary);
}
.bg-cyan-700{
  background-color: var(--color-primary);
}
.bg-dark{
  background-color: var(--color-dark);
}
.bg-emerald-100{
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity));
}
.bg-error{
  background-color: var(--color-error);
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}
.bg-gray-600{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
.bg-gray-700{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.bg-gray-900\/50{
  background-color: rgb(17 24 39 / 0.5);
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity));
}
.bg-green-300{
  --tw-bg-opacity: 1;
  background-color: rgb(132 225 188 / var(--tw-bg-opacity));
}
.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(49 196 141 / var(--tw-bg-opacity));
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(243 250 247 / var(--tw-bg-opacity));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}
.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(5 122 85 / var(--tw-bg-opacity));
}
.bg-green-700{
  --tw-bg-opacity: 1;
  background-color: rgb(4 108 78 / var(--tw-bg-opacity));
}
.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(229 237 255 / var(--tw-bg-opacity));
}
.bg-indigo-100\/90{
  background-color: rgb(229 237 255 / 0.9);
}
.bg-indigo-400{
  --tw-bg-opacity: 1;
  background-color: rgb(141 162 251 / var(--tw-bg-opacity));
}
.bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(88 80 236 / var(--tw-bg-opacity));
}
.bg-info{
  background-color: var(--color-info);
}
.bg-lighterror{
  background-color: var(--color-lighterror);
}
.bg-lightgray{
  background-color: var( --color-lightgray);
}
.bg-lighthover{
  background-color: var(--color-lighthover);
}
.bg-lightinfo{
  background-color: var(--color-lightinfo);
}
.bg-lightprimary{
  background-color: var(--color-lightprimary);
}
.bg-lightsecondary{
  background-color: var(--color-lightsecondary);
}
.bg-lightsuccess{
  background-color: var( --color-lightsuccess);
}
.bg-lightwarning{
  background-color: var(--color-lightwarning);
}
.bg-lime-100{
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity));
}
.bg-lime-400{
  --tw-bg-opacity: 1;
  background-color: rgb(163 230 53 / var(--tw-bg-opacity));
}
.bg-lime-600{
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity));
}
.bg-muted{
  background-color: var(--color-muted);
}
.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 236 220 / var(--tw-bg-opacity));
}
.bg-orange-300{
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 140 / var(--tw-bg-opacity));
}
.bg-orange-400{
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 76 / var(--tw-bg-opacity));
}
.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 248 241 / var(--tw-bg-opacity));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(255 90 31 / var(--tw-bg-opacity));
}
.bg-pink-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 232 243 / var(--tw-bg-opacity));
}
.bg-pink-400{
  --tw-bg-opacity: 1;
  background-color: rgb(241 126 184 / var(--tw-bg-opacity));
}
.bg-pink-500{
  --tw-bg-opacity: 1;
  background-color: rgb(231 70 148 / var(--tw-bg-opacity));
}
.bg-pink-600{
  --tw-bg-opacity: 1;
  background-color: rgb(214 31 105 / var(--tw-bg-opacity));
}
.bg-primary{
  background-color: var(--color-primary);
}
.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(237 235 254 / var(--tw-bg-opacity));
}
.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(246 245 255 / var(--tw-bg-opacity));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(144 97 249 / var(--tw-bg-opacity));
}
.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(126 58 242 / var(--tw-bg-opacity));
}
.bg-purple-700{
  --tw-bg-opacity: 1;
  background-color: rgb(108 43 217 / var(--tw-bg-opacity));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}
.bg-red-400{
  --tw-bg-opacity: 1;
  background-color: rgb(249 128 128 / var(--tw-bg-opacity));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 242 / var(--tw-bg-opacity));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}
.bg-red-700{
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}
.bg-secondary{
  background-color: var(--color-secondary);
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.bg-success{
  background-color: var(--color-success);
}
.bg-teal-100{
  --tw-bg-opacity: 1;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity));
}
.bg-teal-600{
  --tw-bg-opacity: 1;
  background-color: rgb(4 116 129 / var(--tw-bg-opacity));
}
.bg-transparent{
  background-color: transparent;
}
.bg-warning{
  background-color: var(--color-warning);
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20{
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/30{
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/40{
  background-color: rgb(255 255 255 / 0.4);
}
.bg-white\/5{
  background-color: rgb(255 255 255 / 0.05);
}
.bg-white\/50{
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/80{
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95{
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity));
}
.bg-yellow-300{
  --tw-bg-opacity: 1;
  background-color: rgb(250 202 21 / var(--tw-bg-opacity));
}
.bg-yellow-400{
  --tw-bg-opacity: 1;
  background-color: rgb(227 160 8 / var(--tw-bg-opacity));
}
.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 253 234 / var(--tw-bg-opacity));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(194 120 3 / var(--tw-bg-opacity));
}
.bg-yellow-600{
  --tw-bg-opacity: 1;
  background-color: rgb(159 88 10 / var(--tw-bg-opacity));
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-amber-100{
  --tw-gradient-from: #fef3c7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 243 199 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-50{
  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500{
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-600{
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/40{
  --tw-gradient-from: rgb(0 0 0 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/60{
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/70{
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/80{
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100{
  --tw-gradient-from: #E1EFFE var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(225 239 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400{
  --tw-gradient-from: #76A9FA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(118 169 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50{
  --tw-gradient-from: #EBF5FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(235 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500{
  --tw-gradient-from: #3F83F8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(63 131 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/5{
  --tw-gradient-from: rgb(63 131 248 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(63 131 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600{
  --tw-gradient-from: #1C64F2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(28 100 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-400{
  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500{
  --tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-400{
  --tw-gradient-from: #34d399 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(52 211 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-500{
  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-400{
  --tw-gradient-from: #9CA3AF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 163 175 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50{
  --tw-gradient-from: #F9FAFB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-100{
  --tw-gradient-from: #DEF7EC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(222 247 236 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-400{
  --tw-gradient-from: #31C48D var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 196 141 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500{
  --tw-gradient-from: #0E9F6E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 159 110 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600{
  --tw-gradient-from: #057A55 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 122 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500{
  --tw-gradient-from: #6875F5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(104 117 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600{
  --tw-gradient-from: #5850EC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 80 236 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-lime-200{
  --tw-gradient-from: #d9f99d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 249 157 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-200{
  --tw-gradient-from: #FCD9BD var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(252 217 189 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-400{
  --tw-gradient-from: #FF8A4C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 138 76 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50{
  --tw-gradient-from: #FFF8F1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 248 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500{
  --tw-gradient-from: #FF5A1F var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 90 31 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-400{
  --tw-gradient-from: #F17EB8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(241 126 184 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500{
  --tw-gradient-from: #E74694 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(231 70 148 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-100{
  --tw-gradient-from: #EDEBFE var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(237 235 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-400{
  --tw-gradient-from: #AC94FA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(172 148 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500{
  --tw-gradient-from: #9061F9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(144 97 249 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600{
  --tw-gradient-from: #7E3AF2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 58 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-200{
  --tw-gradient-from: #FBD5D5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(251 213 213 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-400{
  --tw-gradient-from: #F98080 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 128 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-50{
  --tw-gradient-from: #FDF2F2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 242 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500{
  --tw-gradient-from: #F05252 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 82 82 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-600{
  --tw-gradient-from: #E02424 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(224 36 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50{
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-200{
  --tw-gradient-from: #AFECEF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(175 236 239 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-400{
  --tw-gradient-from: #16BDCA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 189 202 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white{
  --tw-gradient-from: #ffffff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-200{
  --tw-gradient-from: #FCE96A var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(252 233 106 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-500{
  --tw-gradient-from: #C27803 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(194 120 3 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-50\/30{
  --tw-gradient-to: rgb(235 245 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(235 245 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-500{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--color-primary) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-600{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--color-primary) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-50{
  --tw-gradient-to: rgb(249 250 251 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F9FAFB var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-green-400{
  --tw-gradient-to: rgb(49 196 141 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #31C48D var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-green-500{
  --tw-gradient-to: rgb(14 159 110 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0E9F6E var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-50{
  --tw-gradient-to: rgb(240 245 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F0F5FF var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-lime-400{
  --tw-gradient-to: rgb(163 230 53 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a3e635 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-50{
  --tw-gradient-to: rgb(255 248 241 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #FFF8F1 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-500{
  --tw-gradient-to: rgb(231 70 148 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #E74694 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-600{
  --tw-gradient-to: rgb(126 58 242 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #7E3AF2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-300{
  --tw-gradient-to: rgb(248 180 180 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F8B4B4 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-400{
  --tw-gradient-to: rgb(249 128 128 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F98080 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-500{
  --tw-gradient-to: rgb(240 82 82 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F05252 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-rose-400{
  --tw-gradient-to: rgb(251 113 133 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fb7185 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-rose-500{
  --tw-gradient-to: rgb(244 63 94 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f43f5e var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-400{
  --tw-gradient-to: rgb(22 189 202 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #16BDCA var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-500{
  --tw-gradient-to: rgb(6 148 162 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0694A2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-amber-100{
  --tw-gradient-to: #fef3c7 var(--tw-gradient-to-position);
}
.to-amber-400{
  --tw-gradient-to: #fbbf24 var(--tw-gradient-to-position);
}
.to-amber-50{
  --tw-gradient-to: #fffbeb var(--tw-gradient-to-position);
}
.to-amber-500{
  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);
}
.to-amber-600{
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.to-blue-200{
  --tw-gradient-to: #C3DDFD var(--tw-gradient-to-position);
}
.to-blue-50{
  --tw-gradient-to: #EBF5FF var(--tw-gradient-to-position);
}
.to-blue-600{
  --tw-gradient-to: #1C64F2 var(--tw-gradient-to-position);
}
.to-blue-700{
  --tw-gradient-to: #1A56DB var(--tw-gradient-to-position);
}
.to-blue-800{
  --tw-gradient-to: #1E429F var(--tw-gradient-to-position);
}
.to-cyan-500{
  --tw-gradient-to: var(--color-primary) var(--tw-gradient-to-position);
}
.to-cyan-600{
  --tw-gradient-to: var(--color-primary) var(--tw-gradient-to-position);
}
.to-cyan-700{
  --tw-gradient-to: var(--color-primary) var(--tw-gradient-to-position);
}
.to-emerald-500{
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}
.to-emerald-600{
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.to-gray-100{
  --tw-gradient-to: #F3F4F6 var(--tw-gradient-to-position);
}
.to-gray-500{
  --tw-gradient-to: #6B7280 var(--tw-gradient-to-position);
}
.to-green-200{
  --tw-gradient-to: #BCF0DA var(--tw-gradient-to-position);
}
.to-green-500{
  --tw-gradient-to: #0E9F6E var(--tw-gradient-to-position);
}
.to-green-600{
  --tw-gradient-to: #057A55 var(--tw-gradient-to-position);
}
.to-green-800{
  --tw-gradient-to: #03543F var(--tw-gradient-to-position);
}
.to-indigo-50{
  --tw-gradient-to: #F0F5FF var(--tw-gradient-to-position);
}
.to-indigo-500{
  --tw-gradient-to: #6875F5 var(--tw-gradient-to-position);
}
.to-indigo-600{
  --tw-gradient-to: #5850EC var(--tw-gradient-to-position);
}
.to-lime-200{
  --tw-gradient-to: #d9f99d var(--tw-gradient-to-position);
}
.to-lime-500{
  --tw-gradient-to: #84cc16 var(--tw-gradient-to-position);
}
.to-orange-200{
  --tw-gradient-to: #FCD9BD var(--tw-gradient-to-position);
}
.to-orange-400{
  --tw-gradient-to: #FF8A4C var(--tw-gradient-to-position);
}
.to-orange-50{
  --tw-gradient-to: #FFF8F1 var(--tw-gradient-to-position);
}
.to-orange-500{
  --tw-gradient-to: #FF5A1F var(--tw-gradient-to-position);
}
.to-orange-600{
  --tw-gradient-to: #D03801 var(--tw-gradient-to-position);
}
.to-pink-500{
  --tw-gradient-to: #E74694 var(--tw-gradient-to-position);
}
.to-pink-600{
  --tw-gradient-to: #D61F69 var(--tw-gradient-to-position);
}
.to-purple-200{
  --tw-gradient-to: #DCD7FE var(--tw-gradient-to-position);
}
.to-purple-50{
  --tw-gradient-to: #F6F5FF var(--tw-gradient-to-position);
}
.to-purple-50\/30{
  --tw-gradient-to: rgb(246 245 255 / 0.3) var(--tw-gradient-to-position);
}
.to-purple-500\/5{
  --tw-gradient-to: rgb(144 97 249 / 0.05) var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #7E3AF2 var(--tw-gradient-to-position);
}
.to-purple-700{
  --tw-gradient-to: #6C2BD9 var(--tw-gradient-to-position);
}
.to-purple-800{
  --tw-gradient-to: #5521B5 var(--tw-gradient-to-position);
}
.to-red-100{
  --tw-gradient-to: #FDE8E8 var(--tw-gradient-to-position);
}
.to-red-200{
  --tw-gradient-to: #FBD5D5 var(--tw-gradient-to-position);
}
.to-red-50{
  --tw-gradient-to: #FDF2F2 var(--tw-gradient-to-position);
}
.to-red-600{
  --tw-gradient-to: #E02424 var(--tw-gradient-to-position);
}
.to-red-700{
  --tw-gradient-to: #C81E1E var(--tw-gradient-to-position);
}
.to-rose-600{
  --tw-gradient-to: #e11d48 var(--tw-gradient-to-position);
}
.to-sky-50{
  --tw-gradient-to: #f0f9ff var(--tw-gradient-to-position);
}
.to-teal-500{
  --tw-gradient-to: #0694A2 var(--tw-gradient-to-position);
}
.to-teal-600{
  --tw-gradient-to: #047481 var(--tw-gradient-to-position);
}
.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white{
  --tw-gradient-to: #ffffff var(--tw-gradient-to-position);
}
.to-yellow-200{
  --tw-gradient-to: #FCE96A var(--tw-gradient-to-position);
}
.bg-clip-text{
  background-clip: text;
}
.bg-right-bottom{
  background-position: right bottom;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.fill-current{
  fill: currentColor;
}
.fill-cyan-600{
  fill: var(--color-primary);
}
.fill-gray-600{
  fill: #4B5563;
}
.fill-green-500{
  fill: #0E9F6E;
}
.fill-pink-600{
  fill: #D61F69;
}
.fill-purple-600{
  fill: #7E3AF2;
}
.fill-red-600{
  fill: #E02424;
}
.fill-white{
  fill: #ffffff;
}
.fill-white\/60{
  fill: rgb(255 255 255 / 0.6);
}
.fill-yellow-400{
  fill: #E3A008;
}
.stroke-white{
  stroke: #ffffff;
}
.object-contain{
  object-fit: contain;
}
.object-cover{
  object-fit: cover;
}
.object-center{
  object-position: center;
}
.object-top{
  object-position: top;
}
.\!p-0{
  padding: 0px !important;
}
.\!p-6{
  padding: 1.5rem !important;
}
.p-0{
  padding: 0px;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-10{
  padding: 2.5rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-30{
  padding: 30px;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.p-\[30px\]{
  padding: 30px;
}
.\!py-3{
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}
.px-0{
  padding-left: 0px;
  padding-right: 0px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-9{
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}
.px-\[10px\]{
  padding-left: 10px;
  padding-right: 10px;
}
.px-\[18px\]{
  padding-left: 18px;
  padding-right: 18px;
}
.py-0{
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7{
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[1\.875rem\]{
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}
.py-\[10px\]{
  padding-top: 10px;
  padding-bottom: 10px;
}
.py-\[18px\]{
  padding-top: 18px;
  padding-bottom: 18px;
}
.py-\[30px\]{
  padding-top: 30px;
  padding-bottom: 30px;
}
.pb-0{
  padding-bottom: 0px;
}
.pb-10{
  padding-bottom: 2.5rem;
}
.pb-12{
  padding-bottom: 3rem;
}
.pb-14{
  padding-bottom: 3.5rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-2\.5{
  padding-bottom: 0.625rem;
}
.pb-20{
  padding-bottom: 5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pb-7{
  padding-bottom: 1.75rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pb-9{
  padding-bottom: 2.25rem;
}
.pb-\[0\.625rem\]{
  padding-bottom: 0.625rem;
}
.pb-\[1\.875rem\]{
  padding-bottom: 1.875rem;
}
.pb-\[56\.25\%\]{
  padding-bottom: 56.25%;
}
.pb-\[6\.625rem\]{
  padding-bottom: 6.625rem;
}
.pe-0{
  padding-inline-end: 0px;
}
.pe-1{
  padding-inline-end: 0.25rem;
}
.pl-0{
  padding-left: 0px;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-12{
  padding-left: 3rem;
}
.pl-16{
  padding-left: 4rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-2\.5{
  padding-left: 0.625rem;
}
.pl-20{
  padding-left: 5rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-8{
  padding-left: 2rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-9{
  padding-right: 2.25rem;
}
.pr-\[1\.875rem\]{
  padding-right: 1.875rem;
}
.ps-0{
  padding-inline-start: 0px;
}
.ps-2{
  padding-inline-start: 0.5rem;
}
.ps-3{
  padding-inline-start: 0.75rem;
}
.ps-4{
  padding-inline-start: 1rem;
}
.ps-5{
  padding-inline-start: 1.25rem;
}
.ps-6{
  padding-inline-start: 1.5rem;
}
.ps-8{
  padding-inline-start: 2rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-0\.5{
  padding-top: 0.125rem;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-1\.5{
  padding-top: 0.375rem;
}
.pt-10{
  padding-top: 2.5rem;
}
.pt-12{
  padding-top: 3rem;
}
.pt-14{
  padding-top: 3.5rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-7{
  padding-top: 1.75rem;
}
.pt-8{
  padding-top: 2rem;
}
.pt-\[1\.875rem\]{
  padding-top: 1.875rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.text-start{
  text-align: start;
}
.text-end{
  text-align: end;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans{
  font-family: Cairo, sans-serif;
}
.\!text-xs{
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.text-13{
  font-size: 13px;
}
.text-15{
  font-size: 15px;
}
.text-17{
  font-size: 17px;
}
.text-22{
  font-size: 22px;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-40{
  font-size: 40px;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[13px\]{
  font-size: 13px;
}
.text-\[15px\]{
  font-size: 15px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-base\/7{
  font-size: 1rem;
  line-height: 1.75rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-black{
  font-weight: 900;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-light{
  font-weight: 300;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.\!leading-\[48px\]{
  line-height: 48px !important;
}
.leading-6{
  line-height: 1.5rem;
}
.leading-7{
  line-height: 1.75rem;
}
.leading-8{
  line-height: 2rem;
}
.leading-9{
  line-height: 2.25rem;
}
.leading-\[24px\]{
  line-height: 24px;
}
.leading-\[32px\]{
  line-height: 32px;
}
.leading-\[45px\]{
  line-height: 45px;
}
.leading-\[50px\]{
  line-height: 50px;
}
.leading-\[normal\]{
  line-height: normal;
}
.leading-none{
  line-height: 1;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-tight{
  letter-spacing: -.025em;
}
.tracking-wide{
  letter-spacing: .025em;
}
.tracking-wider{
  letter-spacing: .05em;
}
.\!text-amber-500{
  --tw-text-opacity: 1 !important;
  color: rgb(245 158 11 / var(--tw-text-opacity)) !important;
}
.text-amber-200{
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity));
}
.text-amber-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}
.text-amber-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity));
}
.text-amber-700{
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity));
}
.text-amber-800{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(225 239 254 / var(--tw-text-opacity));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(26 86 219 / var(--tw-text-opacity));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 66 159 / var(--tw-text-opacity));
}
.text-cyan-300{
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity));
}
.text-cyan-500{
  color: var(--color-primary);
}
.text-cyan-600{
  color: var(--color-primary);
}
.text-cyan-700{
  color: var(--color-primary);
}
.text-cyan-800{
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity));
}
.text-cyan-900{
  --tw-text-opacity: 1;
  color: rgb(22 78 99 / var(--tw-text-opacity));
}
.text-dark{
  color: var(--color-dark);
}
.text-darklink{
  color: var(--color-darklink);
}
.text-emerald-500{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity));
}
.text-emerald-600{
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity));
}
.text-error{
  color: var(--color-error);
}
.text-gray-100{
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}
.text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(49 196 141 / var(--tw-text-opacity));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(5 122 85 / var(--tw-text-opacity));
}
.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(4 108 78 / var(--tw-text-opacity));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(3 84 63 / var(--tw-text-opacity));
}
.text-green-900{
  --tw-text-opacity: 1;
  color: rgb(1 71 55 / var(--tw-text-opacity));
}
.text-indigo-500{
  --tw-text-opacity: 1;
  color: rgb(104 117 245 / var(--tw-text-opacity));
}
.text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(88 80 236 / var(--tw-text-opacity));
}
.text-indigo-700{
  --tw-text-opacity: 1;
  color: rgb(81 69 205 / var(--tw-text-opacity));
}
.text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(66 56 157 / var(--tw-text-opacity));
}
.text-indigo-900{
  --tw-text-opacity: 1;
  color: rgb(54 47 120 / var(--tw-text-opacity));
}
.text-info{
  color: var(--color-info);
}
.text-lime-500{
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity));
}
.text-lime-700{
  --tw-text-opacity: 1;
  color: rgb(77 124 15 / var(--tw-text-opacity));
}
.text-lime-800{
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity));
}
.text-lime-900{
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity));
}
.text-link{
  color: var(--color-link);
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(255 90 31 / var(--tw-text-opacity));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(208 56 1 / var(--tw-text-opacity));
}
.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(138 44 13 / var(--tw-text-opacity));
}
.text-pink-500{
  --tw-text-opacity: 1;
  color: rgb(231 70 148 / var(--tw-text-opacity));
}
.text-pink-600{
  --tw-text-opacity: 1;
  color: rgb(214 31 105 / var(--tw-text-opacity));
}
.text-pink-700{
  --tw-text-opacity: 1;
  color: rgb(191 18 93 / var(--tw-text-opacity));
}
.text-pink-800{
  --tw-text-opacity: 1;
  color: rgb(153 21 75 / var(--tw-text-opacity));
}
.text-pink-900{
  --tw-text-opacity: 1;
  color: rgb(117 26 61 / var(--tw-text-opacity));
}
.text-primary{
  color: var(--color-primary);
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(144 97 249 / var(--tw-text-opacity));
}
.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(126 58 242 / var(--tw-text-opacity));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(108 43 217 / var(--tw-text-opacity));
}
.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(85 33 181 / var(--tw-text-opacity));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(249 128 128 / var(--tw-text-opacity));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(200 30 30 / var(--tw-text-opacity));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(155 28 28 / var(--tw-text-opacity));
}
.text-red-900{
  --tw-text-opacity: 1;
  color: rgb(119 29 29 / var(--tw-text-opacity));
}
.text-secondary{
  color: var(--color-secondary);
}
.text-success{
  color: var(--color-success);
}
.text-teal-500{
  --tw-text-opacity: 1;
  color: rgb(6 148 162 / var(--tw-text-opacity));
}
.text-teal-600{
  --tw-text-opacity: 1;
  color: rgb(4 116 129 / var(--tw-text-opacity));
}
.text-teal-700{
  --tw-text-opacity: 1;
  color: rgb(3 102 114 / var(--tw-text-opacity));
}
.text-teal-800{
  --tw-text-opacity: 1;
  color: rgb(5 80 92 / var(--tw-text-opacity));
}
.text-teal-900{
  --tw-text-opacity: 1;
  color: rgb(1 68 81 / var(--tw-text-opacity));
}
.text-transparent{
  color: transparent;
}
.text-warning{
  color: var(--color-warning);
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90{
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(227 160 8 / var(--tw-text-opacity));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(194 120 3 / var(--tw-text-opacity));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(159 88 10 / var(--tw-text-opacity));
}
.text-yellow-700{
  --tw-text-opacity: 1;
  color: rgb(142 75 16 / var(--tw-text-opacity));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(114 59 19 / var(--tw-text-opacity));
}
.text-yellow-900{
  --tw-text-opacity: 1;
  color: rgb(99 49 18 / var(--tw-text-opacity));
}
.text-opacity-65{
  --tw-text-opacity: 0.65;
}
.underline{
  text-decoration-line: underline;
}
.line-through{
  text-decoration-line: line-through;
}
.decoration-gray-500{
  text-decoration-color: #6B7280;
}
.decoration-solid{
  text-decoration-style: solid;
}
.decoration-2{
  text-decoration-thickness: 2px;
}
.underline-offset-2{
  text-underline-offset: 2px;
}
.underline-offset-\[6px\]{
  text-underline-offset: 6px;
}
.placeholder-cyan-700::placeholder{
  color: var(--color-primary);
}
.placeholder-green-700::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(4 108 78 / var(--tw-placeholder-opacity));
}
.placeholder-red-700::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(200 30 30 / var(--tw-placeholder-opacity));
}
.placeholder-yellow-700::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(142 75 16 / var(--tw-placeholder-opacity));
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-15{
  opacity: 0.15;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-40{
  opacity: 0.4;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-80{
  opacity: 0.8;
}
.opacity-90{
  opacity: 0.9;
}
.mix-blend-multiply{
  mix-blend-mode: multiply;
}
.mix-blend-lighten{
  mix-blend-mode: lighten;
}
.\!shadow-lg{
  --tw-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
  --tw-shadow-colored: 0 1rem 3rem var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.\!shadow-none{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-elevation1{
  --tw-shadow: 0px 12px 30px -2px rgba(58,75,116,0.14);;
  --tw-shadow-colored: 0px 12px 30px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-elevation2{
  --tw-shadow: 0px 24px 24px -12px rgba(0,0,0,0.05);;
  --tw-shadow-colored: 0px 24px 24px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-elevation3{
  --tw-shadow: 0px 24px 24px -12px rgba(99,91,255,0.15);;
  --tw-shadow-colored: 0px 24px 24px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-elevation4{
  --tw-shadow: 0px 12px 12px -6px rgba(0,0,0,0.15);;
  --tw-shadow-colored: 0px 12px 12px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner{
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --tw-shadow-colored: 0 1rem 3rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0px 1px 4px 0px rgba(133, 146, 173, 0.2);
  --tw-shadow-colored: 0px 1px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --tw-shadow-colored: 0 0.125rem 0.25rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.outline-0{
  outline-width: 0px;
}
.ring{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-8{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-black{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}
.ring-blue-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 131 248 / var(--tw-ring-opacity));
}
.ring-cyan-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 211 238 / var(--tw-ring-opacity));
}
.ring-cyan-700{
  --tw-ring-color: var(--color-primary);
}
.ring-gray-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}
.ring-gray-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity));
}
.ring-gray-800{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}
.ring-green-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 159 110 / var(--tw-ring-opacity));
}
.ring-pink-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(231 70 148 / var(--tw-ring-opacity));
}
.ring-purple-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(144 97 249 / var(--tw-ring-opacity));
}
.ring-red-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 82 82 / var(--tw-ring-opacity));
}
.ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.ring-yellow-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 202 21 / var(--tw-ring-opacity));
}
.ring-opacity-5{
  --tw-ring-opacity: 0.05;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg{
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl{
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-110{
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-50{
  --tw-brightness: brightness(.5);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow{
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-lg{
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-md{
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.saturate-150{
  --tw-saturate: saturate(1.5);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-2xl{
  --tw-backdrop-blur: blur(40px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[1px\]{
  --tw-backdrop-blur: blur(1px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[0\.5s\]{
  transition-property: 0.5s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[color\2c background-color\2c border-color\2c text-decoration-color\2c fill\2c stroke\2c box-shadow\]{
  transition-property: color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-0{
  transition-delay: 0s;
}
.duration-100{
  transition-duration: 100ms;
}
.duration-1000{
  transition-duration: 1000ms;
}
.duration-150{
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.duration-700{
  transition-duration: 700ms;
}
.duration-75{
  transition-duration: 75ms;
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[--anchor-gap\:var\(--spacing-5\)\]{
  --anchor-gap: var(--spacing-5);
}
.\[overflow\:-moz-scrollbars-none\]{
  overflow: -moz-scrollbars-none;
}
.\[scrollbar-width\:none\]{
  scrollbar-width: none;
}

.empty\:invisible:empty.tooltip > .tooltip-arrow:before{
  visibility: hidden;
}

.empty\:invisible:empty[role="tooltip"] > [data-popper-arrow]:before{
  visibility: hidden;
}

.empty\:invisible:empty[role="tooltip"] > [data-popper-arrow]:after{
  visibility: hidden;
}

.hover\:bg-primary:hoverbutton:hover{
  background-color: var(--color-primary-emphasis);
}

.hover\:bg-secondary:hoverbutton:hover{
  background-color: var(--color-secondary-emphasis);
}

.hover\:bg-info:hoverbutton:hover{
  background-color: var(--color-info-emphasis);
}

.hover\:bg-error:hoverbutton:hover{
  background-color: var(--color-error-emphasis);
}

.hover\:bg-success:hoverbutton:hover{
  background-color: var(--color-success-emphasis);
}

.hover\:bg-warning:hoverbutton:hover{
  background-color: var(--color-warning-emphasis);
}

.focus\:border-ld:focus{
  border-color: var(--color-border);
}

.focus\:border-ld:focus:is(.dark *){
  border-color: var(--color-darkborder);
}

.data-\[focus\]\:bg-hover[data-focus]:hover{
  background-color: var(--color-lighthover);
}

.data-\[focus\]\:bg-hover[data-focus]:is(.dark *):hover{
  background-color: var( --color-darkmuted);
}

.data-\[checked\]\:bg-primary[data-checked]button:hover{
  background-color: var(--color-primary-emphasis);
}

.data-\[hover\]\:bg-primary[data-hover]button:hover{
  background-color: var(--color-primary-emphasis);
}

.data-\[selected\]\:bg-primary[data-selected]button:hover{
  background-color: var(--color-primary-emphasis);
}

.data-\[selected\]\:data-\[hover\]\:bg-primary[data-hover][data-selected]button:hover{
  background-color: var(--color-primary-emphasis);
}

.data-\[checked\]\:bg-secondary[data-checked]button:hover{
  background-color: var(--color-secondary-emphasis);
}

.data-\[checked\]\:bg-info[data-checked]button:hover{
  background-color: var(--color-info-emphasis);
}

.data-\[checked\]\:bg-error[data-checked]button:hover{
  background-color: var(--color-error-emphasis);
}

.data-\[checked\]\:bg-success[data-checked]button:hover{
  background-color: var(--color-success-emphasis);
}

.data-\[checked\]\:bg-warning[data-checked]button:hover{
  background-color: var(--color-warning-emphasis);
}

.dark\:bg-hover:is(.dark *):hover{
  background-color: var(--color-lighthover);
}

.dark\:bg-hover:is(.dark *):is(.dark *):hover{
  background-color: var( --color-darkmuted);
}

.dark\:bg-primary:is(.dark *)button:hover{
  background-color: var(--color-primary-emphasis);
}

.dark\:hover\:bg-primary:hover:is(.dark *)button:hover{
  background-color: var(--color-primary-emphasis);
}

.dark\:data-\[checked\]\:bg-primary[data-checked]:is(.dark *)button:hover{
  background-color: var(--color-primary-emphasis);
}

@media (min-width: 768px){

  .md\:border-ld{
    border-color: var(--color-border);
  }

  .md\:border-ld:is(.dark *){
    border-color: var(--color-darkborder);
  }
}

@media (min-width: 1024px){

  .lg\:border-ld{
    border-color: var(--color-border);
  }

  .lg\:border-ld:is(.dark *){
    border-color: var(--color-darkborder);
  }
}

@media (min-width: 1280px){

  .xl\:border-ld{
    border-color: var(--color-border);
  }

  .xl\:border-ld:is(.dark *){
    border-color: var(--color-darkborder);
  }
}

.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}

.before\:left-0::before{
  content: var(--tw-content);
  left: 0px;
}

.before\:top-0::before{
  content: var(--tw-content);
  top: 0px;
}

.before\:hidden::before{
  content: var(--tw-content);
  display: none;
}

.before\:h-\[50px\]::before{
  content: var(--tw-content);
  height: 50px;
}

.before\:w-\[325px\]::before{
  content: var(--tw-content);
  width: 325px;
}

.before\:bg-\[url\(\'\/images\/front-pages\/background\/left-shape\.png\'\)\]::before{
  content: var(--tw-content);
  background-image: url('/images/front-pages/background/left-shape.png');
}

.before\:bg-contain::before{
  content: var(--tw-content);
  background-size: contain;
}

.before\:bg-no-repeat::before{
  content: var(--tw-content);
  background-repeat: no-repeat;
}

.before\:content-\[\'\'\]::before{
  --tw-content: '';
  content: var(--tw-content);
}

.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}

.after\:end-1\/4::after{
  content: var(--tw-content);
  inset-inline-end: 25%;
}

.after\:left-1::after{
  content: var(--tw-content);
  left: 0.25rem;
}

.after\:left-px::after{
  content: var(--tw-content);
  left: 1px;
}

.after\:right-0::after{
  content: var(--tw-content);
  right: 0px;
}

.after\:right-\[2px\]::after{
  content: var(--tw-content);
  right: 2px;
}

.after\:top-0::after{
  content: var(--tw-content);
  top: 0px;
}

.after\:top-0\.5::after{
  content: var(--tw-content);
  top: 0.125rem;
}

.after\:top-\[2px\]::after{
  content: var(--tw-content);
  top: 2px;
}

.after\:top-px::after{
  content: var(--tw-content);
  top: 1px;
}

.after\:inline-block::after{
  content: var(--tw-content);
  display: inline-block;
}

.after\:hidden::after{
  content: var(--tw-content);
  display: none;
}

.after\:h-4::after{
  content: var(--tw-content);
  height: 1rem;
}

.after\:h-5::after{
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:h-6::after{
  content: var(--tw-content);
  height: 1.5rem;
}

.after\:h-\[2px\]::after{
  content: var(--tw-content);
  height: 2px;
}

.after\:h-\[325px\]::after{
  content: var(--tw-content);
  height: 325px;
}

.after\:h-\[50px\]::after{
  content: var(--tw-content);
  height: 50px;
}

.after\:w-4::after{
  content: var(--tw-content);
  width: 1rem;
}

.after\:w-5::after{
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:w-6::after{
  content: var(--tw-content);
  width: 1.5rem;
}

.after\:w-\[325px\]::after{
  content: var(--tw-content);
  width: 325px;
}

.after\:w-full::after{
  content: var(--tw-content);
  width: 100%;
}

.after\:translate-x-full::after{
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after{
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-2::after{
  content: var(--tw-content);
  border-width: 2px;
}

.after\:border-b::after{
  content: var(--tw-content);
  border-bottom-width: 1px;
}

.after\:border-border::after{
  content: var(--tw-content);
  border-color: var(--color-border);
}

.after\:border-gray-300::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.after\:border-primary::after{
  content: var(--tw-content);
  border-color: var(--color-primary);
}

.after\:border-white::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.after\:bg-white::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.after\:bg-\[url\(\'\/images\/front-pages\/background\/contact-icon\.png\'\)\]::after{
  content: var(--tw-content);
  background-image: url('/images/front-pages/background/contact-icon.png');
}

.after\:bg-\[url\(\'\/images\/front-pages\/background\/right-shape\.png\'\)\]::after{
  content: var(--tw-content);
  background-image: url('/images/front-pages/background/right-shape.png');
}

.after\:bg-contain::after{
  content: var(--tw-content);
  background-size: contain;
}

.after\:bg-right-top::after{
  content: var(--tw-content);
  background-position: right top;
}

.after\:bg-no-repeat::after{
  content: var(--tw-content);
  background-repeat: no-repeat;
}

.after\:transition-all::after{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after{
  --tw-content: '';
  content: var(--tw-content);
}

.first\:ml-0:first-child{
  margin-left: 0px;
}

.first\:mt-0:first-child{
  margin-top: 0px;
}

.first\:rounded-t-lg:first-child{
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

.first\:border-t-0:first-child{
  border-top-width: 0px;
}

.first\:pt-0:first-child{
  padding-top: 0px;
}

.last\:mr-0:last-child{
  margin-right: 0px;
}

.last\:rounded-b-lg:last-child{
  border-bottom-right-radius: 24px;
  border-bottom-left-radius: 24px;
}

.odd\:bg-transparent:nth-child(odd){
  background-color: transparent;
}

.odd\:bg-white:nth-child(odd){
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.even\:bg-gray-50:nth-child(even){
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.even\:bg-muted:nth-child(even){
  background-color: var(--color-muted);
}

.empty\:invisible:empty{
  visibility: hidden;
}

.hover\:-translate-y-0:hover{
  --tw-translate-y: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-0\.5:hover{
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-rotate-1:hover{
  --tw-rotate: -1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-1:hover{
  --tw-rotate: 1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.03\]:hover{
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-not-allowed:hover{
  cursor: not-allowed;
}

.hover\:cursor-pointer:hover{
  cursor: pointer;
}

.hover\:border-amber-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity));
}

.hover\:border-blue-200:hover{
  --tw-border-opacity: 1;
  border-color: rgb(195 221 253 / var(--tw-border-opacity));
}

.hover\:border-blue-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(164 202 254 / var(--tw-border-opacity));
}

.hover\:border-gray-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.hover\:border-gray-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.hover\:border-primary:hover{
  border-color: var(--color-primary);
}

.hover\:border-purple-200:hover{
  --tw-border-opacity: 1;
  border-color: rgb(220 215 254 / var(--tw-border-opacity));
}

.hover\:border-red-200:hover{
  --tw-border-opacity: 1;
  border-color: rgb(251 213 213 / var(--tw-border-opacity));
}

.hover\:bg-amber-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity));
}

.hover\:bg-amber-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity));
}

.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(225 239 254 / var(--tw-bg-opacity));
}

.hover\:bg-blue-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(195 221 253 / var(--tw-bg-opacity));
}

.hover\:bg-blue-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(235 245 255 / var(--tw-bg-opacity));
}

.hover\:bg-blue-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(63 131 248 / var(--tw-bg-opacity));
}

.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}

.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}

.hover\:bg-blue-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}

.hover\:bg-cyan-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity));
}

.hover\:bg-cyan-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
}

.hover\:bg-cyan-600:hover{
  background-color: var(--color-primary);
}

.hover\:bg-cyan-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity));
}

.hover\:bg-dark:hover{
  background-color: var(--color-dark);
}

.hover\:bg-error:hover{
  background-color: var(--color-error);
}

.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50\/50:hover{
  background-color: rgb(249 250 251 / 0.5);
}

.hover\:bg-gray-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-green-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(188 240 218 / var(--tw-bg-opacity));
}

.hover\:bg-indigo-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(205 219 254 / var(--tw-bg-opacity));
}

.hover\:bg-info:hover{
  background-color: var(--color-info);
}

.hover\:bg-lightgray:hover{
  background-color: var( --color-lightgray);
}

.hover\:bg-lightprimary:hover{
  background-color: var(--color-lightprimary);
}

.hover\:bg-lime-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity));
}

.hover\:bg-muted:hover{
  background-color: var(--color-muted);
}

.hover\:bg-orange-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 90 31 / var(--tw-bg-opacity));
}

.hover\:bg-pink-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 209 232 / var(--tw-bg-opacity));
}

.hover\:bg-primary:hover{
  background-color: var(--color-primary);
}

.hover\:bg-primaryemphasis:hover{
  background-color: var(--color-primary-emphasis);
}

.hover\:bg-purple-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 215 254 / var(--tw-bg-opacity));
}

.hover\:bg-red-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}

.hover\:bg-red-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(251 213 213 / var(--tw-bg-opacity));
}

.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 242 / var(--tw-bg-opacity));
}

.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}

.hover\:bg-red-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}

.hover\:bg-secondary:hover{
  background-color: var(--color-secondary);
}

.hover\:bg-success:hover{
  background-color: var(--color-success);
}

.hover\:bg-teal-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(175 236 239 / var(--tw-bg-opacity));
}

.hover\:bg-transparent:hover{
  background-color: transparent;
}

.hover\:bg-warning:hover{
  background-color: var(--color-warning);
}

.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:bg-white\/30:hover{
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-white\/5:hover{
  background-color: rgb(255 255 255 / 0.05);
}

.hover\:bg-yellow-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(252 233 106 / var(--tw-bg-opacity));
}

.hover\:bg-yellow-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(142 75 16 / var(--tw-bg-opacity));
}

.hover\:bg-green-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(5 122 85 / var(--tw-bg-opacity));
}

.hover\:bg-gradient-to-br:hover{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.hover\:from-amber-600:hover{
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-600:hover{
  --tw-gradient-from: #1C64F2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(28 100 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-700:hover{
  --tw-gradient-from: #1A56DB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 86 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-emerald-600:hover{
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-600:hover{
  --tw-gradient-from: #057A55 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 122 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-indigo-600:hover{
  --tw-gradient-from: #5850EC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 80 236 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-orange-600:hover{
  --tw-gradient-from: #D03801 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(208 56 1 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-600:hover{
  --tw-gradient-from: #7E3AF2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 58 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-600:hover{
  --tw-gradient-from: #E02424 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(224 36 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-green-600:hover{
  --tw-gradient-to: rgb(5 122 85 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #057A55 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:via-red-600:hover{
  --tw-gradient-to: rgb(224 36 36 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #E02424 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:via-rose-600:hover{
  --tw-gradient-to: rgb(225 29 72 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #e11d48 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:via-teal-600:hover{
  --tw-gradient-to: rgb(4 116 129 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #047481 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:to-blue-700:hover{
  --tw-gradient-to: #1A56DB var(--tw-gradient-to-position);
}

.hover\:to-blue-800:hover{
  --tw-gradient-to: #1E429F var(--tw-gradient-to-position);
}

.hover\:to-cyan-700:hover{
  --tw-gradient-to: var(--color-primary) var(--tw-gradient-to-position);
}

.hover\:to-emerald-700:hover{
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}

.hover\:to-indigo-700:hover{
  --tw-gradient-to: #5145CD var(--tw-gradient-to-position);
}

.hover\:to-orange-600:hover{
  --tw-gradient-to: #D03801 var(--tw-gradient-to-position);
}

.hover\:to-pink-700:hover{
  --tw-gradient-to: #BF125D var(--tw-gradient-to-position);
}

.hover\:to-purple-700:hover{
  --tw-gradient-to: #6C2BD9 var(--tw-gradient-to-position);
}

.hover\:to-red-700:hover{
  --tw-gradient-to: #C81E1E var(--tw-gradient-to-position);
}

.hover\:to-rose-700:hover{
  --tw-gradient-to: #be123c var(--tw-gradient-to-position);
}

.hover\:to-teal-700:hover{
  --tw-gradient-to: #036672 var(--tw-gradient-to-position);
}

.hover\:text-amber-500:hover{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}

.hover\:text-amber-600:hover{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity));
}

.hover\:text-blue-700:hover{
  --tw-text-opacity: 1;
  color: rgb(26 86 219 / var(--tw-text-opacity));
}

.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 66 159 / var(--tw-text-opacity));
}

.hover\:text-blue-900:hover{
  --tw-text-opacity: 1;
  color: rgb(35 56 118 / var(--tw-text-opacity));
}

.hover\:text-cyan-700:hover{
  color: var(--color-primary);
}

.hover\:text-cyan-800:hover{
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity));
}

.hover\:text-dark:hover{
  color: var(--color-dark);
}

.hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.hover\:text-green-700:hover{
  --tw-text-opacity: 1;
  color: rgb(4 108 78 / var(--tw-text-opacity));
}

.hover\:text-orange-400:hover{
  --tw-text-opacity: 1;
  color: rgb(255 138 76 / var(--tw-text-opacity));
}

.hover\:text-primary:hover{
  color: var(--color-primary);
}

.hover\:text-primaryemphasis:hover{
  color: var(--color-primary-emphasis);
}

.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(200 30 30 / var(--tw-text-opacity));
}

.hover\:text-red-900:hover{
  --tw-text-opacity: 1;
  color: rgb(119 29 29 / var(--tw-text-opacity));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-gray-800:hover{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:no-underline:hover{
  text-decoration-line: none;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:shadow:hover{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-2xl:hover{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover{
  --tw-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --tw-shadow-colored: 0 1rem 3rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover{
  --tw-shadow: 0px 1px 4px 0px rgba(133, 146, 173, 0.2);
  --tw-shadow-colored: 0px 1px 4px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:z-10:focus{
  z-index: 10;
}

.focus\:border-amber-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity));
}

.focus\:border-blue-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}

.focus\:border-blue-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity));
}

.focus\:border-cyan-500:focus{
  border-color: var(--color-primary);
}

.focus\:border-gray-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.focus\:border-green-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(14 159 110 / var(--tw-border-opacity));
}

.focus\:border-green-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(5 122 85 / var(--tw-border-opacity));
}

.focus\:border-orange-400:focus{
  --tw-border-opacity: 1;
  border-color: rgb(255 138 76 / var(--tw-border-opacity));
}

.focus\:border-red-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}

.focus\:border-red-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}

.focus\:border-transparent:focus{
  border-color: transparent;
}

.focus\:border-yellow-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(194 120 3 / var(--tw-border-opacity));
}

.focus\:bg-gray-100:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.focus\:text-cyan-700:focus{
  color: var(--color-primary);
}

.focus\:text-gray-900:focus{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-amber-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity));
}

.focus\:ring-blue-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

.focus\:ring-blue-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(118 169 250 / var(--tw-ring-opacity));
}

.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 131 248 / var(--tw-ring-opacity));
}

.focus\:ring-blue-500\/50:focus{
  --tw-ring-color: rgb(63 131 248 / 0.5);
}

.focus\:ring-blue-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(28 100 242 / var(--tw-ring-opacity));
}

.focus\:ring-cyan-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 243 252 / var(--tw-ring-opacity));
}

.focus\:ring-cyan-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(103 232 249 / var(--tw-ring-opacity));
}

.focus\:ring-cyan-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 211 238 / var(--tw-ring-opacity));
}

.focus\:ring-cyan-500:focus{
  --tw-ring-color: var(--color-primary);
}

.focus\:ring-cyan-600:focus{
  --tw-ring-color: var(--color-primary);
}

.focus\:ring-cyan-700:focus{
  --tw-ring-color: var(--color-primary);
}

.focus\:ring-cyan-800:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 94 117 / var(--tw-ring-opacity));
}

.focus\:ring-gray-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}

.focus\:ring-gray-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.focus\:ring-gray-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity));
}

.focus\:ring-gray-700:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity));
}

.focus\:ring-gray-800:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}

.focus\:ring-gray-900:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity));
}

.focus\:ring-green-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(188 240 218 / var(--tw-ring-opacity));
}

.focus\:ring-green-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(132 225 188 / var(--tw-ring-opacity));
}

.focus\:ring-green-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(49 196 141 / var(--tw-ring-opacity));
}

.focus\:ring-green-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 159 110 / var(--tw-ring-opacity));
}

.focus\:ring-green-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(5 122 85 / var(--tw-ring-opacity));
}

.focus\:ring-green-800:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 84 63 / var(--tw-ring-opacity));
}

.focus\:ring-indigo-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(180 198 252 / var(--tw-ring-opacity));
}

.focus\:ring-indigo-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(141 162 251 / var(--tw-ring-opacity));
}

.focus\:ring-indigo-700:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(81 69 205 / var(--tw-ring-opacity));
}

.focus\:ring-lime-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(217 249 157 / var(--tw-ring-opacity));
}

.focus\:ring-lime-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(190 242 100 / var(--tw-ring-opacity));
}

.focus\:ring-lime-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(163 230 53 / var(--tw-ring-opacity));
}

.focus\:ring-lime-700:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(77 124 15 / var(--tw-ring-opacity));
}

.focus\:ring-orange-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 138 76 / var(--tw-ring-opacity));
}

.focus\:ring-pink-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 209 232 / var(--tw-ring-opacity));
}

.focus\:ring-pink-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 180 217 / var(--tw-ring-opacity));
}

.focus\:ring-pink-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 126 184 / var(--tw-ring-opacity));
}

.focus\:ring-pink-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(214 31 105 / var(--tw-ring-opacity));
}

.focus\:ring-purple-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(220 215 254 / var(--tw-ring-opacity));
}

.focus\:ring-purple-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(202 191 253 / var(--tw-ring-opacity));
}

.focus\:ring-purple-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(172 148 250 / var(--tw-ring-opacity));
}

.focus\:ring-purple-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 58 242 / var(--tw-ring-opacity));
}

.focus\:ring-red-100:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 232 232 / var(--tw-ring-opacity));
}

.focus\:ring-red-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 180 180 / var(--tw-ring-opacity));
}

.focus\:ring-red-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 128 128 / var(--tw-ring-opacity));
}

.focus\:ring-red-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 82 82 / var(--tw-ring-opacity));
}

.focus\:ring-red-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 36 36 / var(--tw-ring-opacity));
}

.focus\:ring-red-900:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(119 29 29 / var(--tw-ring-opacity));
}

.focus\:ring-teal-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 220 226 / var(--tw-ring-opacity));
}

.focus\:ring-teal-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 189 202 / var(--tw-ring-opacity));
}

.focus\:ring-teal-600:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 116 129 / var(--tw-ring-opacity));
}

.focus\:ring-transparent:focus{
  --tw-ring-color: transparent;
}

.focus\:ring-yellow-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 202 21 / var(--tw-ring-opacity));
}

.focus\:ring-yellow-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(227 160 8 / var(--tw-ring-opacity));
}

.focus\:ring-yellow-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(194 120 3 / var(--tw-ring-opacity));
}

.focus\:ring-offset-0:focus{
  --tw-ring-offset-width: 0px;
}

.enabled\:hover\:bg-blue-800:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-cyan-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-cyan-800:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-gray-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-gray-900:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-green-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-green-800:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(3 84 63 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-indigo-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(229 237 255 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-lime-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-pink-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(252 232 243 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-purple-800:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(85 33 181 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-red-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-red-800:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(155 28 28 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-teal-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-yellow-100:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-yellow-500:hover:enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(194 120 3 / var(--tw-bg-opacity));
}

.enabled\:hover\:bg-gradient-to-bl:hover:enabled{
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.enabled\:hover\:bg-gradient-to-br:hover:enabled{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.enabled\:hover\:bg-gradient-to-l:hover:enabled{
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.enabled\:hover\:from-teal-200:hover:enabled{
  --tw-gradient-from: #AFECEF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(175 236 239 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.enabled\:hover\:to-lime-200:hover:enabled{
  --tw-gradient-to: #d9f99d var(--tw-gradient-to-position);
}

.enabled\:hover\:text-cyan-700:hover:enabled{
  color: var(--color-primary);
}

.enabled\:hover\:text-gray-700:hover:enabled{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.enabled\:hover\:text-gray-900:hover:enabled{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.disabled\:transform-none:disabled{
  transform: none;
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:text-gray-400:disabled{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.disabled\:opacity-70:disabled{
  opacity: 0.7;
}

.group:first-child .group-first\:hidden{
  display: none;
}

.group\/body:first-child .group\/row:first-child .group-first\/body\:group-first\/row\:first\:rounded-tl-lg:first-child{
  border-top-left-radius: 24px;
}

.group\/head:first-child .group-first\/head\:first\:rounded-tl-lg:first-child{
  border-top-left-radius: 24px;
}

.group\/body:first-child .group\/row:first-child .group-first\/body\:group-first\/row\:last\:rounded-tr-lg:last-child{
  border-top-right-radius: 24px;
}

.group\/head:first-child .group-first\/head\:last\:rounded-tr-lg:last-child{
  border-top-right-radius: 24px;
}

.group\/body:last-child .group\/row:last-child .group-last\/body\:group-last\/row\:first\:rounded-bl-lg:first-child{
  border-bottom-left-radius: 24px;
}

.group\/body:last-child .group\/row:last-child .group-last\/body\:group-last\/row\:last\:rounded-br-lg:last-child{
  border-bottom-right-radius: 24px;
}

.group:hover .group-hover\:visible{
  visibility: visible;
}

.group:hover .group-hover\:block{
  display: block;
}

.group:hover .group-hover\:w-full{
  width: 100%;
}

.group:hover .group-hover\:translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-12{
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/link:hover .group-hover\/link\:bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}

.group\/menu:hover .group-hover\/menu\:bg-lightprimary{
  background-color: var(--color-lightprimary);
}

.group:hover .group-hover\:bg-blue-200{
  --tw-bg-opacity: 1;
  background-color: rgb(195 221 253 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-cyan-200{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-gray-300{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-green-200{
  --tw-bg-opacity: 1;
  background-color: rgb(188 240 218 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-indigo-200{
  --tw-bg-opacity: 1;
  background-color: rgb(205 219 254 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-lime-200{
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-pink-200{
  --tw-bg-opacity: 1;
  background-color: rgb(250 209 232 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-purple-200{
  --tw-bg-opacity: 1;
  background-color: rgb(220 215 254 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-red-200{
  --tw-bg-opacity: 1;
  background-color: rgb(251 213 213 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-teal-200{
  --tw-bg-opacity: 1;
  background-color: rgb(175 236 239 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-white\/30{
  background-color: rgb(255 255 255 / 0.3);
}

.group:hover .group-hover\:bg-white\/50{
  background-color: rgb(255 255 255 / 0.5);
}

.group:hover .group-hover\:bg-yellow-200{
  --tw-bg-opacity: 1;
  background-color: rgb(252 233 106 / var(--tw-bg-opacity));
}

.group\/link:hover .group-hover\/link\:text-primary{
  color: var(--color-primary);
}

.group\/menu:hover .group-hover\/menu\:text-primary{
  color: var(--color-primary);
}

.group:hover .group-hover\:text-amber-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary{
  color: var(--color-primary);
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group:hover .group-hover\:opacity-25{
  opacity: 0.25;
}

.group:hover .group-hover\:opacity-30{
  opacity: 0.3;
}

.group:hover .group-hover\:opacity-40{
  opacity: 0.4;
}

.group:hover .group-hover\:opacity-90{
  opacity: 0.9;
}

.group:focus .group-focus\:outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.group:focus .group-focus\:ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.group:focus .group-focus\:ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.group:enabled:hover .group-enabled\:group-hover\:bg-opacity-0{
  --tw-bg-opacity: 0;
}

.group:enabled:hover .group-enabled\:group-hover\:text-inherit{
  color: inherit;
}

.peer:checked ~ .peer-checked\:bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after{
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:top-1\/2{
  top: 50%;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:left-0{
  left: 0px;
}

.peer:focus ~ .peer-focus\:top-2{
  top: 0.5rem;
}

.peer:focus ~ .peer-focus\:-translate-y-4{
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:-translate-y-6{
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:scale-75{
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.peer:focus ~ .peer-focus\:text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}

.peer:focus ~ .peer-focus\:outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-blue-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

.data-\[closed\]\:-translate-x-full[data-closed]{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:-translate-y-1[data-closed]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:-translate-y-6[data-closed]{
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[enter\]\:data-\[closed\]\:-translate-x-full[data-closed][data-enter]{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[leave\]\:data-\[closed\]\:translate-x-full[data-closed][data-leave]{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:rotate-\[-120deg\][data-closed]{
  --tw-rotate: -120deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[leave\]\:data-\[closed\]\:rotate-\[0deg\][data-closed][data-leave]{
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:scale-50[data-closed]{
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:scale-95[data-closed]{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[hover\]\:scale-105[data-hover]{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[leave\]\:data-\[closed\]\:scale-95[data-closed][data-leave]{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[disabled\]\:cursor-not-allowed[data-disabled]{
  cursor: not-allowed;
}

.data-\[checked\]\:bg-blue-500[data-checked]{
  --tw-bg-opacity: 1;
  background-color: rgb(63 131 248 / var(--tw-bg-opacity));
}

.data-\[checked\]\:bg-error[data-checked]{
  background-color: var(--color-error);
}

.data-\[checked\]\:bg-info[data-checked]{
  background-color: var(--color-info);
}

.data-\[checked\]\:bg-primary[data-checked]{
  background-color: var(--color-primary);
}

.data-\[checked\]\:bg-secondary[data-checked]{
  background-color: var(--color-secondary);
}

.data-\[checked\]\:bg-success[data-checked]{
  background-color: var(--color-success);
}

.data-\[checked\]\:bg-warning[data-checked]{
  background-color: var(--color-warning);
}

.data-\[checked\]\:data-\[disabled\]\:bg-gray-500[data-disabled][data-checked]{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.data-\[disabled\]\:bg-gray-100[data-disabled]{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.data-\[hover\]\:bg-primary[data-hover]{
  background-color: var(--color-primary);
}

.data-\[selected\]\:bg-primary[data-selected]{
  background-color: var(--color-primary);
}

.data-\[selected\]\:data-\[hover\]\:bg-primary[data-hover][data-selected]{
  background-color: var(--color-primary);
}

.data-\[active\]\:text-primary[data-active]{
  color: var(--color-primary);
}

.data-\[focus\]\:text-primary[data-focus]{
  color: var(--color-primary);
}

.data-\[hover\]\:text-primary[data-hover]{
  color: var(--color-primary);
}

.data-\[hover\]\:text-white[data-hover]{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.data-\[selected\]\:text-white[data-selected]{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.data-\[closed\]\:opacity-0[data-closed]{
  opacity: 0;
}

.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}

.data-\[leave\]\:data-\[closed\]\:opacity-0[data-closed][data-leave]{
  opacity: 0;
}

.data-\[focus\]\:outline-1[data-focus]{
  outline-width: 1px;
}

.data-\[focus\]\:outline-primary[data-focus]{
  outline-color: var(--color-primary);
}

.data-\[focus\]\:outline-white[data-focus]{
  outline-color: #ffffff;
}

.data-\[enter\]\:duration-100[data-enter]{
  transition-duration: 100ms;
}

.data-\[leave\]\:duration-200[data-leave]{
  transition-duration: 200ms;
}

.data-\[leave\]\:duration-300[data-leave]{
  transition-duration: 300ms;
}

.data-\[leave\]\:ease-in-out[data-leave]{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.data-\[disabled\]\:hover\:text-darklink:hover[data-disabled]{
  color: var(--color-darklink);
}

.group[data-checked] .group-data-\[checked\]\:visible{
  visibility: visible;
}

.group[data-selected] .group-data-\[selected\]\:visible{
  visibility: visible;
}

.group[data-focus] .group-data-\[focus\]\:inline{
  display: inline;
}

.group[data-checked] .group-data-\[checked\]\:translate-x-6{
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-open] .group-data-\[open\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-hover] .group-data-\[hover\]\:fill-white\/50{
  fill: rgb(255 255 255 / 0.5);
}

.group[data-checked] .group-data-\[checked\]\:text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group[data-checked] .group-data-\[checked\]\:text-white\/50{
  color: rgb(255 255 255 / 0.5);
}

.group[data-hover] .group-data-\[hover\]\:text-primary{
  color: var(--color-primary);
}

.group[data-checked] .group-data-\[checked\]\:opacity-100{
  opacity: 1;
}

.dark\:block:is(.dark *){
  display: block;
}

.dark\:hidden:is(.dark *){
  display: none;
}

.dark\:divide-darkborder:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  border-color: var(--color-darkborder);
}

.dark\:divide-gray-700:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-divide-opacity));
}

.dark\:border-none:is(.dark *){
  border-style: none;
}

.dark\:\!border-gray-700:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(55 65 81 / var(--tw-border-opacity)) !important;
}

.dark\:border-amber-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity));
}

.dark\:border-amber-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(180 83 9 / var(--tw-border-opacity));
}

.dark\:border-amber-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(146 64 14 / var(--tw-border-opacity));
}

.dark\:border-amber-900\/20:is(.dark *){
  border-color: rgb(120 53 15 / 0.2);
}

.dark\:border-blue-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(118 169 250 / var(--tw-border-opacity));
}

.dark\:border-blue-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 66 159 / var(--tw-border-opacity));
}

.dark\:border-blue-800\/30:is(.dark *){
  border-color: rgb(30 66 159 / 0.3);
}

.dark\:border-blue-900\/20:is(.dark *){
  border-color: rgb(35 56 118 / 0.2);
}

.dark\:border-cyan-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity));
}

.dark\:border-cyan-500:is(.dark *){
  border-color: var(--color-primary);
}

.dark\:border-cyan-600:is(.dark *){
  border-color: var(--color-primary);
}

.dark\:border-darkborder:is(.dark *){
  border-color: var(--color-darkborder);
}

.dark\:border-gray-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:border-gray-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}

.dark\:border-gray-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:border-gray-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}

.dark\:border-gray-900:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity));
}

.dark\:border-green-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(49 196 141 / var(--tw-border-opacity));
}

.dark\:border-green-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(14 159 110 / var(--tw-border-opacity));
}

.dark\:border-green-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(5 122 85 / var(--tw-border-opacity));
}

.dark\:border-green-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(3 84 63 / var(--tw-border-opacity));
}

.dark\:border-indigo-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(88 80 236 / var(--tw-border-opacity));
}

.dark\:border-indigo-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(66 56 157 / var(--tw-border-opacity));
}

.dark\:border-lime-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(101 163 13 / var(--tw-border-opacity));
}

.dark\:border-pink-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(214 31 105 / var(--tw-border-opacity));
}

.dark\:border-red-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 128 128 / var(--tw-border-opacity));
}

.dark\:border-red-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}

.dark\:border-red-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}

.dark\:border-red-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(155 28 28 / var(--tw-border-opacity));
}

.dark\:border-teal-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(4 116 129 / var(--tw-border-opacity));
}

.dark\:border-white:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.dark\:border-white\/10:is(.dark *){
  border-color: rgb(255 255 255 / 0.1);
}

.dark\:border-yellow-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(227 160 8 / var(--tw-border-opacity));
}

.dark\:border-yellow-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(159 88 10 / var(--tw-border-opacity));
}

.dark\:border-yellow-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(114 59 19 / var(--tw-border-opacity));
}

.dark\:border-t-amber-400:is(.dark *){
  --tw-border-opacity: 1;
  border-top-color: rgb(251 191 36 / var(--tw-border-opacity));
}

.dark\:bg-amber-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity));
}

.dark\:bg-amber-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity));
}

.dark\:bg-amber-900\/10:is(.dark *){
  background-color: rgb(120 53 15 / 0.1);
}

.dark\:bg-amber-900\/20:is(.dark *){
  background-color: rgb(120 53 15 / 0.2);
}

.dark\:bg-amber-900\/30:is(.dark *){
  background-color: rgb(120 53 15 / 0.3);
}

.dark\:bg-amber-900\/40:is(.dark *){
  background-color: rgb(120 53 15 / 0.4);
}

.dark\:bg-black:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.dark\:bg-blue-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(195 221 253 / var(--tw-bg-opacity));
}

.dark\:bg-blue-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}

.dark\:bg-blue-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}

.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(35 56 118 / 0.2);
}

.dark\:bg-blue-900\/30:is(.dark *){
  background-color: rgb(35 56 118 / 0.3);
}

.dark\:bg-cyan-100:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity));
}

.dark\:bg-cyan-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
}

.dark\:bg-cyan-600:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:bg-cyan-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity));
}

.dark\:bg-cyan-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity));
}

.dark\:bg-dark:is(.dark *){
  background-color: var(--color-dark);
}

.dark\:bg-darkborder:is(.dark *){
  background-color: var(--color-darkborder);
}

.dark\:bg-darkgray:is(.dark *){
  background-color: var(--color-darkgray);
}

.dark\:bg-darkmuted:is(.dark *){
  background-color: var( --color-darkmuted);
}

.dark\:bg-gray-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.dark\:bg-gray-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.dark\:bg-gray-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.dark\:bg-gray-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.dark\:bg-gray-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:bg-gray-600\/50:is(.dark *){
  background-color: rgb(75 85 99 / 0.5);
}

.dark\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:bg-gray-700\/50:is(.dark *){
  background-color: rgb(55 65 81 / 0.5);
}

.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.dark\:bg-gray-800\/30:is(.dark *){
  background-color: rgb(31 41 55 / 0.3);
}

.dark\:bg-gray-800\/50:is(.dark *){
  background-color: rgb(31 41 55 / 0.5);
}

.dark\:bg-gray-800\/70:is(.dark *){
  background-color: rgb(31 41 55 / 0.7);
}

.dark\:bg-gray-800\/80:is(.dark *){
  background-color: rgb(31 41 55 / 0.8);
}

.dark\:bg-gray-800\/90:is(.dark *){
  background-color: rgb(31 41 55 / 0.9);
}

.dark\:bg-gray-800\/95:is(.dark *){
  background-color: rgb(31 41 55 / 0.95);
}

.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.dark\:bg-gray-900\/80:is(.dark *){
  background-color: rgb(17 24 39 / 0.8);
}

.dark\:bg-gray-900\/95:is(.dark *){
  background-color: rgb(17 24 39 / 0.95);
}

.dark\:bg-green-100:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity));
}

.dark\:bg-green-100\/10:is(.dark *){
  background-color: rgb(222 247 236 / 0.1);
}

.dark\:bg-green-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(188 240 218 / var(--tw-bg-opacity));
}

.dark\:bg-green-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}

.dark\:bg-green-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(5 122 85 / var(--tw-bg-opacity));
}

.dark\:bg-green-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(3 84 63 / var(--tw-bg-opacity));
}

.dark\:bg-green-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(1 71 55 / var(--tw-bg-opacity));
}

.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(1 71 55 / 0.2);
}

.dark\:bg-green-900\/30:is(.dark *){
  background-color: rgb(1 71 55 / 0.3);
}

.dark\:bg-green-900\/40:is(.dark *){
  background-color: rgb(1 71 55 / 0.4);
}

.dark\:bg-indigo-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(205 219 254 / var(--tw-bg-opacity));
}

.dark\:bg-indigo-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(104 117 245 / var(--tw-bg-opacity));
}

.dark\:bg-indigo-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 80 236 / var(--tw-bg-opacity));
}

.dark\:bg-indigo-900\/90:is(.dark *){
  background-color: rgb(54 47 120 / 0.9);
}

.dark\:bg-lighterror:is(.dark *){
  background-color: var(--color-lighterror);
}

.dark\:bg-lightinfo:is(.dark *){
  background-color: var(--color-lightinfo);
}

.dark\:bg-lightprimary:is(.dark *){
  background-color: var(--color-lightprimary);
}

.dark\:bg-lightsecondary:is(.dark *){
  background-color: var(--color-lightsecondary);
}

.dark\:bg-lightsuccess:is(.dark *){
  background-color: var( --color-lightsuccess);
}

.dark\:bg-lightwarning:is(.dark *){
  background-color: var(--color-lightwarning);
}

.dark\:bg-lime-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity));
}

.dark\:bg-lime-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity));
}

.dark\:bg-orange-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 76 / var(--tw-bg-opacity));
}

.dark\:bg-orange-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 90 31 / var(--tw-bg-opacity));
}

.dark\:bg-orange-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(180 52 3 / var(--tw-bg-opacity));
}

.dark\:bg-orange-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(119 29 29 / var(--tw-bg-opacity));
}

.dark\:bg-pink-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(250 209 232 / var(--tw-bg-opacity));
}

.dark\:bg-pink-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(214 31 105 / var(--tw-bg-opacity));
}

.dark\:bg-primary:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:bg-purple-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 215 254 / var(--tw-bg-opacity));
}

.dark\:bg-purple-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(144 97 249 / var(--tw-bg-opacity));
}

.dark\:bg-purple-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(126 58 242 / var(--tw-bg-opacity));
}

.dark\:bg-purple-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(74 29 150 / var(--tw-bg-opacity));
}

.dark\:bg-purple-900\/20:is(.dark *){
  background-color: rgb(74 29 150 / 0.2);
}

.dark\:bg-purple-900\/30:is(.dark *){
  background-color: rgb(74 29 150 / 0.3);
}

.dark\:bg-red-100:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}

.dark\:bg-red-100\/10:is(.dark *){
  background-color: rgb(253 232 232 / 0.1);
}

.dark\:bg-red-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(251 213 213 / var(--tw-bg-opacity));
}

.dark\:bg-red-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}

.dark\:bg-red-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}

.dark\:bg-red-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(155 28 28 / var(--tw-bg-opacity));
}

.dark\:bg-red-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(119 29 29 / var(--tw-bg-opacity));
}

.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(119 29 29 / 0.2);
}

.dark\:bg-red-900\/30:is(.dark *){
  background-color: rgb(119 29 29 / 0.3);
}

.dark\:bg-red-900\/40:is(.dark *){
  background-color: rgb(119 29 29 / 0.4);
}

.dark\:bg-teal-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(175 236 239 / var(--tw-bg-opacity));
}

.dark\:bg-teal-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(4 116 129 / var(--tw-bg-opacity));
}

.dark\:bg-transparent:is(.dark *){
  background-color: transparent;
}

.dark\:bg-white:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-100:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-200:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(252 233 106 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(159 88 10 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(99 49 18 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-900\/20:is(.dark *){
  background-color: rgb(99 49 18 / 0.2);
}

.dark\:bg-opacity-80:is(.dark *){
  --tw-bg-opacity: 0.8;
}

.dark\:from-blue-400:is(.dark *){
  --tw-gradient-from: #76A9FA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(118 169 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-blue-500:is(.dark *){
  --tw-gradient-from: #3F83F8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(63 131 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-blue-900\/20:is(.dark *){
  --tw-gradient-from: rgb(35 56 118 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(35 56 118 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-blue-900\/30:is(.dark *){
  --tw-gradient-from: rgb(35 56 118 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(35 56 118 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-700\/50:is(.dark *){
  --tw-gradient-from: rgb(55 65 81 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-800:is(.dark *){
  --tw-gradient-from: #1F2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-900:is(.dark *){
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-green-400:is(.dark *){
  --tw-gradient-from: #31C48D var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 196 141 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-green-500:is(.dark *){
  --tw-gradient-from: #0E9F6E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 159 110 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-green-900\/30:is(.dark *){
  --tw-gradient-from: rgb(1 71 55 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(1 71 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-purple-400:is(.dark *){
  --tw-gradient-from: #AC94FA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(172 148 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-purple-500:is(.dark *){
  --tw-gradient-from: #9061F9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(144 97 249 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-purple-900\/30:is(.dark *){
  --tw-gradient-from: rgb(74 29 150 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 29 150 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-red-500:is(.dark *){
  --tw-gradient-from: #F05252 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 82 82 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-red-700:is(.dark *){
  --tw-gradient-from: #C81E1E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(200 30 30 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-blue-900\/10:is(.dark *){
  --tw-gradient-to: rgb(35 56 118 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(35 56 118 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-gray-800:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1F2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-blue-500:is(.dark *){
  --tw-gradient-to: #3F83F8 var(--tw-gradient-to-position);
}

.dark\:to-blue-600:is(.dark *){
  --tw-gradient-to: #1C64F2 var(--tw-gradient-to-position);
}

.dark\:to-blue-700:is(.dark *){
  --tw-gradient-to: #1A56DB var(--tw-gradient-to-position);
}

.dark\:to-blue-800\/30:is(.dark *){
  --tw-gradient-to: rgb(30 66 159 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-emerald-700:is(.dark *){
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}

.dark\:to-gray-700:is(.dark *){
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}

.dark\:to-gray-700\/30:is(.dark *){
  --tw-gradient-to: rgb(55 65 81 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-gray-800:is(.dark *){
  --tw-gradient-to: #1F2937 var(--tw-gradient-to-position);
}

.dark\:to-gray-900:is(.dark *){
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}

.dark\:to-green-500:is(.dark *){
  --tw-gradient-to: #0E9F6E var(--tw-gradient-to-position);
}

.dark\:to-green-700:is(.dark *){
  --tw-gradient-to: #046C4E var(--tw-gradient-to-position);
}

.dark\:to-green-800\/30:is(.dark *){
  --tw-gradient-to: rgb(3 84 63 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-indigo-700:is(.dark *){
  --tw-gradient-to: #5145CD var(--tw-gradient-to-position);
}

.dark\:to-indigo-900\/20:is(.dark *){
  --tw-gradient-to: rgb(54 47 120 / 0.2) var(--tw-gradient-to-position);
}

.dark\:to-purple-400:is(.dark *){
  --tw-gradient-to: #AC94FA var(--tw-gradient-to-position);
}

.dark\:to-purple-500:is(.dark *){
  --tw-gradient-to: #9061F9 var(--tw-gradient-to-position);
}

.dark\:to-purple-700:is(.dark *){
  --tw-gradient-to: #6C2BD9 var(--tw-gradient-to-position);
}

.dark\:to-purple-800\/30:is(.dark *){
  --tw-gradient-to: rgb(85 33 181 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-purple-900\/10:is(.dark *){
  --tw-gradient-to: rgb(74 29 150 / 0.1) var(--tw-gradient-to-position);
}

.dark\:to-purple-900\/20:is(.dark *){
  --tw-gradient-to: rgb(74 29 150 / 0.2) var(--tw-gradient-to-position);
}

.dark\:to-red-700:is(.dark *){
  --tw-gradient-to: #C81E1E var(--tw-gradient-to-position);
}

.dark\:to-red-800:is(.dark *){
  --tw-gradient-to: #9B1C1C var(--tw-gradient-to-position);
}

.dark\:fill-gray-300:is(.dark *){
  fill: #D1D5DB;
}

.dark\:\!text-amber-400:is(.dark *){
  --tw-text-opacity: 1 !important;
  color: rgb(251 191 36 / var(--tw-text-opacity)) !important;
}

.dark\:text-amber-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity));
}

.dark\:text-amber-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity));
}

.dark\:text-amber-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity));
}

.dark\:text-blue-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(195 221 253 / var(--tw-text-opacity));
}

.dark\:text-blue-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(164 202 254 / var(--tw-text-opacity));
}

.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(118 169 250 / var(--tw-text-opacity));
}

.dark\:text-blue-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}

.dark\:text-blue-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}

.dark\:text-blue-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(30 66 159 / var(--tw-text-opacity));
}

.dark\:text-blue-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(35 56 118 / var(--tw-text-opacity));
}

.dark\:text-cyan-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(207 250 254 / var(--tw-text-opacity));
}

.dark\:text-cyan-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(165 243 252 / var(--tw-text-opacity));
}

.dark\:text-cyan-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity));
}

.dark\:text-cyan-500:is(.dark *){
  color: var(--color-primary);
}

.dark\:text-cyan-600:is(.dark *){
  color: var(--color-primary);
}

.dark\:text-cyan-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity));
}

.dark\:text-cyan-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(22 78 99 / var(--tw-text-opacity));
}

.dark\:text-darklink:is(.dark *){
  color: var(--color-darklink);
}

.dark\:text-emerald-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity));
}

.dark\:text-gray-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:text-gray-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:text-gray-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.dark\:text-gray-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.dark\:text-gray-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.dark\:text-gray-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(188 240 218 / var(--tw-text-opacity));
}

.dark\:text-green-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(132 225 188 / var(--tw-text-opacity));
}

.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(49 196 141 / var(--tw-text-opacity));
}

.dark\:text-green-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}

.dark\:text-green-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(5 122 85 / var(--tw-text-opacity));
}

.dark\:text-green-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(3 84 63 / var(--tw-text-opacity));
}

.dark\:text-green-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(1 71 55 / var(--tw-text-opacity));
}

.dark\:text-indigo-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(180 198 252 / var(--tw-text-opacity));
}

.dark\:text-indigo-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(88 80 236 / var(--tw-text-opacity));
}

.dark\:text-indigo-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(66 56 157 / var(--tw-text-opacity));
}

.dark\:text-indigo-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(54 47 120 / var(--tw-text-opacity));
}

.dark\:text-lightgray:is(.dark *){
  color: var( --color-lightgray);
}

.dark\:text-lime-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity));
}

.dark\:text-lime-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity));
}

.dark\:text-lime-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity));
}

.dark\:text-orange-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 217 189 / var(--tw-text-opacity));
}

.dark\:text-orange-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 140 / var(--tw-text-opacity));
}

.dark\:text-orange-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 138 76 / var(--tw-text-opacity));
}

.dark\:text-pink-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(214 31 105 / var(--tw-text-opacity));
}

.dark\:text-pink-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(153 21 75 / var(--tw-text-opacity));
}

.dark\:text-pink-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(117 26 61 / var(--tw-text-opacity));
}

.dark\:text-primary:is(.dark *){
  color: var(--color-primary);
}

.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(172 148 250 / var(--tw-text-opacity));
}

.dark\:text-purple-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(126 58 242 / var(--tw-text-opacity));
}

.dark\:text-purple-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(85 33 181 / var(--tw-text-opacity));
}

.dark\:text-purple-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 29 150 / var(--tw-text-opacity));
}

.dark\:text-red-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 213 213 / var(--tw-text-opacity));
}

.dark\:text-red-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 180 180 / var(--tw-text-opacity));
}

.dark\:text-red-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(249 128 128 / var(--tw-text-opacity));
}

.dark\:text-red-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}

.dark\:text-red-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity));
}

.dark\:text-red-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(155 28 28 / var(--tw-text-opacity));
}

.dark\:text-red-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(119 29 29 / var(--tw-text-opacity));
}

.dark\:text-teal-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(4 116 129 / var(--tw-text-opacity));
}

.dark\:text-teal-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(5 80 92 / var(--tw-text-opacity));
}

.dark\:text-teal-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(1 68 81 / var(--tw-text-opacity));
}

.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:text-yellow-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(250 202 21 / var(--tw-text-opacity));
}

.dark\:text-yellow-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(159 88 10 / var(--tw-text-opacity));
}

.dark\:text-yellow-800:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(114 59 19 / var(--tw-text-opacity));
}

.dark\:text-yellow-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(99 49 18 / var(--tw-text-opacity));
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.dark\:mix-blend-color:is(.dark *){
  mix-blend-mode: color;
}

.dark\:shadow-dark-md:is(.dark *){
  --tw-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px, rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
  --tw-shadow-colored: 0px 0px 2px 0px var(--tw-shadow-color), 0px 12px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:shadow-none:is(.dark *){
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:shadow-sm-light:is(.dark *){
  --tw-shadow: 0 2px 5px 0px rgba(255, 255, 255, 0.08);
  --tw-shadow-colored: 0 2px 5px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:ring-cyan-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 94 117 / var(--tw-ring-opacity));
}

.dark\:ring-gray-400:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity));
}

.dark\:ring-gray-500:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity));
}

.dark\:ring-gray-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}

.dark\:ring-gray-900:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity));
}

.dark\:ring-green-500:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 159 110 / var(--tw-ring-opacity));
}

.dark\:ring-pink-500:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(231 70 148 / var(--tw-ring-opacity));
}

.dark\:ring-purple-600:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 58 242 / var(--tw-ring-opacity));
}

.dark\:ring-red-700:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(200 30 30 / var(--tw-ring-opacity));
}

.dark\:ring-yellow-500:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(194 120 3 / var(--tw-ring-opacity));
}

.dark\:ring-offset-blue-700:is(.dark *){
  --tw-ring-offset-color: #1A56DB;
}

.dark\:ring-offset-cyan-600:is(.dark *){
  --tw-ring-offset-color: var(--color-primary);
}

.dark\:ring-offset-gray-800:is(.dark *){
  --tw-ring-offset-color: #1F2937;
}

.dark\:ring-offset-gray-900:is(.dark *){
  --tw-ring-offset-color: #111827;
}

.dark\:ring-offset-green-600:is(.dark *){
  --tw-ring-offset-color: #057A55;
}

.dark\:ring-offset-green-800:is(.dark *){
  --tw-ring-offset-color: #03543F;
}

.dark\:ring-offset-indigo-700:is(.dark *){
  --tw-ring-offset-color: #5145CD;
}

.dark\:ring-offset-lime-700:is(.dark *){
  --tw-ring-offset-color: #4d7c0f;
}

.dark\:ring-offset-pink-600:is(.dark *){
  --tw-ring-offset-color: #D61F69;
}

.dark\:ring-offset-purple-600:is(.dark *){
  --tw-ring-offset-color: #7E3AF2;
}

.dark\:ring-offset-red-600:is(.dark *){
  --tw-ring-offset-color: #E02424;
}

.dark\:ring-offset-red-900:is(.dark *){
  --tw-ring-offset-color: #771D1D;
}

.dark\:ring-offset-teal-600:is(.dark *){
  --tw-ring-offset-color: #047481;
}

.dark\:ring-offset-yellow-400:is(.dark *){
  --tw-ring-offset-color: #E3A008;
}

.dark\:after\:border-darkborder:is(.dark *)::after{
  content: var(--tw-content);
  border-color: var(--color-darkborder);
}

.dark\:after\:border-primary:is(.dark *)::after{
  content: var(--tw-content);
  border-color: var(--color-primary);
}

.odd\:dark\:bg-gray-800:is(.dark *):nth-child(odd){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.dark\:even\:bg-darkmuted:nth-child(even):is(.dark *){
  background-color: var( --color-darkmuted);
}

.even\:dark\:bg-gray-700:is(.dark *):nth-child(even){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:hover\:border-gray-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:hover\:border-gray-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:hover\:bg-blue-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(164 202 254 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-blue-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-blue-900\/10:hover:is(.dark *){
  background-color: rgb(35 56 118 / 0.1);
}

.dark\:hover\:bg-blue-900\/20:hover:is(.dark *){
  background-color: rgb(35 56 118 / 0.2);
}

.dark\:hover\:bg-cyan-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-cyan-700:hover:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:hover\:bg-darkmuted:hover:is(.dark *){
  background-color: var( --color-darkmuted);
}

.dark\:hover\:bg-gray-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-700\/30:hover:is(.dark *){
  background-color: rgb(55 65 81 / 0.3);
}

.dark\:hover\:bg-gray-700\/50:hover:is(.dark *){
  background-color: rgb(55 65 81 / 0.5);
}

.dark\:hover\:bg-gray-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-green-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(132 225 188 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-indigo-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(180 198 252 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-lightprimary:hover:is(.dark *){
  background-color: var(--color-lightprimary);
}

.dark\:hover\:bg-lime-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(190 242 100 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-orange-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(208 56 1 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-pink-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(248 180 217 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary:hover:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:hover\:bg-primaryemphasis:hover:is(.dark *){
  background-color: var(--color-primary-emphasis);
}

.dark\:hover\:bg-purple-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(202 191 253 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-red-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(248 180 180 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-red-900\/20:hover:is(.dark *){
  background-color: rgb(119 29 29 / 0.2);
}

.dark\:hover\:bg-slate-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-teal-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(126 220 226 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-yellow-300:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(250 202 21 / var(--tw-bg-opacity));
}

.hover\:dark\:bg-darkmuted:is(.dark *):hover{
  background-color: var( --color-darkmuted);
}

.dark\:hover\:from-blue-600:hover:is(.dark *){
  --tw-gradient-from: #1C64F2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(28 100 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:hover\:to-blue-700:hover:is(.dark *){
  --tw-gradient-to: #1A56DB var(--tw-gradient-to-position);
}

.dark\:hover\:text-amber-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity));
}

.dark\:hover\:text-blue-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(164 202 254 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:hover\:text-green-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(132 225 188 / var(--tw-text-opacity));
}

.dark\:hover\:text-orange-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 140 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary:hover:is(.dark *){
  color: var(--color-primary);
}

.dark\:hover\:text-primaryemphasis:hover:is(.dark *){
  color: var(--color-primary-emphasis);
}

.dark\:hover\:text-red-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 180 180 / var(--tw-text-opacity));
}

.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:focus\:border-blue-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}

.dark\:focus\:border-cyan-500:focus:is(.dark *){
  border-color: var(--color-primary);
}

.dark\:focus\:border-gray-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:focus\:border-green-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(14 159 110 / var(--tw-border-opacity));
}

.dark\:focus\:border-orange-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 90 31 / var(--tw-border-opacity));
}

.dark\:focus\:border-red-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}

.dark\:focus\:border-yellow-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(194 120 3 / var(--tw-border-opacity));
}

.dark\:focus\:bg-cyan-600:focus:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:focus\:bg-gray-600:focus:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:focus\:text-white:focus:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.focus\:dark\:ring-2:is(.dark *):focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.dark\:focus\:ring-blue-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(118 169 250 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-blue-500\/50:focus:is(.dark *){
  --tw-ring-color: rgb(63 131 248 / 0.5);
}

.dark\:focus\:ring-blue-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(28 100 242 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-blue-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(26 86 219 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-blue-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 66 159 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-cyan-500:focus:is(.dark *){
  --tw-ring-color: var(--color-primary);
}

.dark\:focus\:ring-cyan-600:focus:is(.dark *){
  --tw-ring-color: var(--color-primary);
}

.dark\:focus\:ring-cyan-700:focus:is(.dark *){
  --tw-ring-color: var(--color-primary);
}

.dark\:focus\:ring-cyan-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 94 117 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-cyan-900:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 78 99 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-900:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-green-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 159 110 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-green-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(5 122 85 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-green-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 108 78 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-green-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 84 63 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-indigo-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(81 69 205 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-lime-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(77 124 15 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-lime-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 98 18 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-orange-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 90 31 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-pink-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(214 31 105 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-pink-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 18 93 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-pink-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(153 21 75 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-purple-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 58 242 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-purple-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(85 33 181 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-purple-900:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(74 29 150 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 128 128 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 82 82 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 36 36 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(200 30 30 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(155 28 28 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-900:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(119 29 29 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-teal-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 116 129 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-teal-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 102 114 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-teal-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(5 80 92 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-yellow-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(227 160 8 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-yellow-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(194 120 3 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-yellow-700:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(142 75 16 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-yellow-900:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 49 18 / var(--tw-ring-opacity));
}

.focus\:dark\:ring-white\/25:is(.dark *):focus{
  --tw-ring-color: rgb(255 255 255 / 0.25);
}

.dark\:enabled\:hover\:border-cyan-700:hover:enabled:is(.dark *){
  border-color: var(--color-primary);
}

.dark\:enabled\:hover\:border-gray-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-green-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(4 108 78 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-indigo-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(81 69 205 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-lime-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(77 124 15 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-pink-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(191 18 93 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-red-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(200 30 30 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-teal-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(3 102 114 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:border-yellow-700:hover:enabled:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(142 75 16 / var(--tw-border-opacity));
}

.dark\:enabled\:hover\:bg-cyan-700:hover:enabled:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:enabled\:hover\:bg-gray-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-green-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(4 108 78 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-indigo-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(81 69 205 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-lime-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(77 124 15 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-pink-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(191 18 93 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-purple-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(108 43 217 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-red-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-teal-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(3 102 114 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:bg-yellow-700:hover:enabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(142 75 16 / var(--tw-bg-opacity));
}

.enabled\:dark\:hover\:bg-gray-700:hover:is(.dark *):enabled{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:enabled\:hover\:text-white:hover:enabled:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.enabled\:dark\:hover\:text-white:hover:is(.dark *):enabled{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.disabled\:dark\:text-gray-500:is(.dark *):disabled{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.group\/link:hover .dark\:group-hover\/link\:bg-amber-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-blue-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(164 202 254 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-cyan-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-800\/60:is(.dark *){
  background-color: rgb(31 41 55 / 0.6);
}

.group:hover .dark\:group-hover\:bg-green-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(132 225 188 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-indigo-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(180 198 252 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-lime-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(190 242 100 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-pink-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(248 180 217 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-purple-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(202 191 253 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-red-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(248 180 180 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-teal-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(126 220 226 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-yellow-300:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(250 202 21 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:text-amber-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-amber-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group:focus .dark\:group-focus\:ring-gray-800\/70:is(.dark *){
  --tw-ring-color: rgb(31 41 55 / 0.7);
}

.peer:focus ~ .peer-focus\:dark\:text-blue-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}

.peer:focus ~ .dark\:peer-focus\:ring-blue-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 66 159 / var(--tw-ring-opacity));
}

.dark\:data-\[checked\]\:bg-primary[data-checked]:is(.dark *){
  background-color: var(--color-primary);
}

.dark\:data-\[disabled\]\:bg-dark[data-disabled]:is(.dark *){
  background-color: var(--color-dark);
}

@media (min-width: 640px){

  .sm\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .sm\:col-span-6{
    grid-column: span 6 / span 6;
  }

  .sm\:mx-auto{
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-0{
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .sm\:mb-0{
    margin-bottom: 0px;
  }

  .sm\:mb-1{
    margin-bottom: 0.25rem;
  }

  .sm\:mb-4{
    margin-bottom: 1rem;
  }

  .sm\:mt-0{
    margin-top: 0px;
  }

  .sm\:mt-4{
    margin-top: 1rem;
  }

  .sm\:block{
    display: block;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:h-10{
    height: 2.5rem;
  }

  .sm\:h-12{
    height: 3rem;
  }

  .sm\:h-6{
    height: 1.5rem;
  }

  .sm\:h-64{
    height: 16rem;
  }

  .sm\:h-7{
    height: 1.75rem;
  }

  .sm\:w-10{
    width: 2.5rem;
  }

  .sm\:w-12{
    width: 3rem;
  }

  .sm\:w-3\/12{
    width: 25%;
  }

  .sm\:w-6{
    width: 1.5rem;
  }

  .sm\:w-\[360px\]{
    width: 360px;
  }

  .sm\:w-auto{
    width: auto;
  }

  .sm\:w-fit{
    width: fit-content;
  }

  .sm\:w-full{
    width: 100%;
  }

  .sm\:min-w-12{
    min-width: 3rem;
  }

  .sm\:max-w-56{
    max-width: 14rem;
  }

  .sm\:max-w-60{
    max-width: 15rem;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-start{
    justify-content: flex-start;
  }

  .sm\:justify-center{
    justify-content: center;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:gap-6{
    gap: 1.5rem;
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:text-wrap{
    text-wrap: wrap;
  }

  .sm\:p-5{
    padding: 1.25rem;
  }

  .sm\:p-6{
    padding: 1.5rem;
  }

  .sm\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-30{
    padding-left: 30px;
    padding-right: 30px;
  }

  .sm\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:py-6{
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pb-14{
    padding-bottom: 3.5rem;
  }

  .sm\:pr-8{
    padding-right: 2rem;
  }

  .sm\:ps-2{
    padding-inline-start: 0.5rem;
  }

  .sm\:pt-10{
    padding-top: 2.5rem;
  }

  .sm\:text-left{
    text-align: left;
  }

  .sm\:text-center{
    text-align: center;
  }

  .sm\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-44{
    font-size: 44px;
  }

  .sm\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xs{
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

@media (min-width: 768px){

  .md\:inset-0{
    inset: 0px;
  }

  .md\:order-2{
    order: 2;
  }

  .md\:col-span-12{
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4{
    grid-column: span 4 / span 4;
  }

  .md\:col-span-5{
    grid-column: span 5 / span 5;
  }

  .md\:col-span-6{
    grid-column: span 6 / span 6;
  }

  .md\:col-span-7{
    grid-column: span 7 / span 7;
  }

  .md\:col-span-8{
    grid-column: span 8 / span 8;
  }

  .md\:m-0{
    margin: 0px;
  }

  .md\:mx-2{
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }

  .md\:my-10{
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .md\:my-12{
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:ml-1{
    margin-left: 0.25rem;
  }

  .md\:mr-2{
    margin-right: 0.5rem;
  }

  .md\:mr-4{
    margin-right: 1rem;
  }

  .md\:mr-6{
    margin-right: 1.5rem;
  }

  .md\:mt-0{
    margin-top: 0px;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:inline-flex{
    display: inline-flex;
  }

  .md\:hidden{
    display: none;
  }

  .md\:h-14{
    height: 3.5rem;
  }

  .md\:h-7{
    height: 1.75rem;
  }

  .md\:h-auto{
    height: auto;
  }

  .md\:h-full{
    height: 100%;
  }

  .md\:w-14{
    width: 3.5rem;
  }

  .md\:w-48{
    width: 12rem;
  }

  .md\:w-64{
    width: 16rem;
  }

  .md\:w-96{
    width: 24rem;
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:w-full{
    width: 100%;
  }

  .md\:min-w-\[100px\]{
    min-width: 100px;
  }

  .md\:max-w-xl{
    max-width: 36rem;
  }

  .md\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-center{
    align-items: center;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:justify-between{
    justify-content: space-between;
  }

  .md\:gap-12{
    gap: 3rem;
  }

  .md\:gap-14{
    gap: 3.5rem;
  }

  .md\:gap-2{
    gap: 0.5rem;
  }

  .md\:gap-30{
    gap: 30px;
  }

  .md\:gap-6{
    gap: 1.5rem;
  }

  .md\:gap-x-3{
    column-gap: 0.75rem;
  }

  .md\:space-x-8 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:rounded-none{
    border-radius: 0px;
  }

  .md\:rounded-l-lg{
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
  }

  .md\:border-0{
    border-width: 0px;
  }

  .md\:border-e{
    border-inline-end-width: 1px;
  }

  .md\:bg-transparent{
    background-color: transparent;
  }

  .md\:p-0{
    padding: 0px;
  }

  .md\:p-6{
    padding: 1.5rem;
  }

  .md\:p-8{
    padding: 2rem;
  }

  .md\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-5{
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-\[30px\]{
    padding-left: 30px;
    padding-right: 30px;
  }

  .md\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-10{
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-14{
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .md\:py-2{
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-2\.5{
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .md\:pb-14{
    padding-bottom: 3.5rem;
  }

  .md\:ps-7{
    padding-inline-start: 1.75rem;
  }

  .md\:pt-0{
    padding-top: 0px;
  }

  .md\:pt-14{
    padding-top: 3.5rem;
  }

  .md\:pt-2{
    padding-top: 0.5rem;
  }

  .md\:pt-4{
    padding-top: 1rem;
  }

  .md\:text-right{
    text-align: right;
  }

  .md\:text-end{
    text-align: end;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:font-medium{
    font-weight: 500;
  }

  .md\:text-cyan-700{
    color: var(--color-primary);
  }

  .md\:hover\:bg-lightprimary:hover{
    background-color: var(--color-lightprimary);
  }

  .md\:hover\:bg-transparent:hover{
    background-color: transparent;
  }

  .md\:hover\:text-cyan-700:hover{
    color: var(--color-primary);
  }

  .md\:hover\:text-primary:hover{
    color: var(--color-primary);
  }

  .md\:dark\:hover\:bg-lightprimary:hover:is(.dark *){
    background-color: var(--color-lightprimary);
  }

  .md\:dark\:hover\:bg-transparent:hover:is(.dark *){
    background-color: transparent;
  }

  .md\:dark\:hover\:text-white:hover:is(.dark *){
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }
}

@media (min-width: 1024px){

  .lg\:relative{
    position: relative;
  }

  .lg\:z-\[0\]{
    z-index: 0;
  }

  .lg\:order-1{
    order: 1;
  }

  .lg\:order-2{
    order: 2;
  }

  .lg\:order-3{
    order: 3;
  }

  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-12{
    grid-column: span 12 / span 12;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5{
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6{
    grid-column: span 6 / span 6;
  }

  .lg\:col-span-7{
    grid-column: span 7 / span 7;
  }

  .lg\:col-span-8{
    grid-column: span 8 / span 8;
  }

  .lg\:my-8{
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .lg\:mt-0{
    margin-top: 0px;
  }

  .lg\:mt-6{
    margin-top: 1.5rem;
  }

  .lg\:block{
    display: block;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:h-auto{
    height: auto;
  }

  .lg\:w-1\/3{
    width: 33.333333%;
  }

  .lg\:w-1\/5{
    width: 20%;
  }

  .lg\:w-2\/3{
    width: 66.666667%;
  }

  .lg\:w-3\/4{
    width: 75%;
  }

  .lg\:w-\[calc\(100\%_-_0px\)\]{
    width: calc(100% - 0px);
  }

  .lg\:max-w-7xl{
    max-width: 80rem;
  }

  .lg\:max-w-\[340px\]{
    max-width: 340px;
  }

  .lg\:transform-none{
    transform: none;
  }

  .lg\:grid-cols-12{
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:items-center{
    align-items: center;
  }

  .lg\:justify-start{
    justify-content: flex-start;
  }

  .lg\:justify-end{
    justify-content: flex-end;
  }

  .lg\:gap-30{
    gap: 30px;
  }

  .lg\:gap-8{
    gap: 2rem;
  }

  .lg\:border-e{
    border-inline-end-width: 1px;
  }

  .lg\:bg-transparent{
    background-color: transparent;
  }

  .lg\:p-6{
    padding: 1.5rem;
  }

  .lg\:p-8{
    padding: 2rem;
  }

  .lg\:px-20{
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .lg\:px-64{
    padding-left: 16rem;
    padding-right: 16rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-9{
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
  }

  .lg\:ps-5{
    padding-inline-start: 1.25rem;
  }

  .lg\:ps-6{
    padding-inline-start: 1.5rem;
  }

  .lg\:ps-7{
    padding-inline-start: 1.75rem;
  }

  .lg\:pt-14{
    padding-top: 3.5rem;
  }

  .lg\:pt-24{
    padding-top: 6rem;
  }

  .lg\:text-left{
    text-align: left;
  }

  .lg\:text-start{
    text-align: start;
  }

  .lg\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-56{
    font-size: 56px;
  }

  .lg\:text-64{
    font-size: 64px;
  }

  .lg\:leading-\[64px\]{
    line-height: 64px;
  }

  .lg\:opacity-0{
    opacity: 0;
  }

  .before\:lg\:inline-block::before{
    content: var(--tw-content);
    display: inline-block;
  }

  .after\:lg\:inline-block::after{
    content: var(--tw-content);
    display: inline-block;
  }
}

@media (min-width: 1280px){

  .xl\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .xl\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .xl\:col-span-5{
    grid-column: span 5 / span 5;
  }

  .xl\:col-span-6{
    grid-column: span 6 / span 6;
  }

  .xl\:col-span-7{
    grid-column: span 7 / span 7;
  }

  .xl\:col-span-8{
    grid-column: span 8 / span 8;
  }

  .xl\:block{
    display: block;
  }

  .xl\:flex{
    display: flex;
  }

  .xl\:hidden{
    display: none;
  }

  .xl\:h-60{
    height: 15rem;
  }

  .xl\:h-80{
    height: 20rem;
  }

  .xl\:w-\[calc\(100\%_-_300px\)\]{
    width: calc(100% - 300px);
  }

  .xl\:max-w-\[300px\]{
    max-width: 300px;
  }

  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:flex-row{
    flex-direction: row;
  }

  .xl\:items-center{
    align-items: center;
  }

  .xl\:gap-9{
    gap: 2.25rem;
  }

  .xl\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .xl\:border-y{
    border-top-width: 1px;
    border-bottom-width: 1px;
  }

  .xl\:py-2{
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .xl\:py-2\.5{
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .xl\:pb-0{
    padding-bottom: 0px;
  }

  .xl\:pe-4{
    padding-inline-end: 1rem;
  }

  .xl\:ps-20{
    padding-inline-start: 5rem;
  }

  .xl\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .xl\:leading-\[64px\]{
    line-height: 64px;
  }
}

@media (min-width: 1536px){

  .\32xl\:h-96{
    height: 24rem;
  }
}

.rtl\:-right-10:where([dir="rtl"], [dir="rtl"] *){
  right: -2.5rem;
}

.rtl\:left-\[unset\]:where([dir="rtl"], [dir="rtl"] *){
  left: unset;
}

.rtl\:right-0:where([dir="rtl"], [dir="rtl"] *){
  right: 0px;
}

.rtl\:right-full:where([dir="rtl"], [dir="rtl"] *){
  right: 100%;
}

.rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *){
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:scale-x-\[-1\]:where([dir="rtl"], [dir="rtl"] *){
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}

.rtl\:after\:right-1:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 0.25rem;
}

.rtl\:after\:right-px:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 1px;
}

.rtl\:after\:-translate-x-full:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\:\:-webkit-scrollbar\]\:\!hidden::-webkit-scrollbar{
  display: none !important;
}

.\[\&\:\:-webkit-scrollbar\]\:\!h-0::-webkit-scrollbar{
  height: 0px !important;
}

.\[\&\:\:-webkit-scrollbar\]\:\!w-0::-webkit-scrollbar{
  width: 0px !important;
}

.\[\&\:\:-webkit-scrollbar\]\:\!bg-transparent::-webkit-scrollbar{
  background-color: transparent !important;
}

.\[\&\>\*\]\:pointer-events-none>*{
  pointer-events: none;
}

.\[\&\>\*\]\:cursor-grab>*{
  cursor: grab;
}

.\[\&\>\*\]\:first\:rounded-t-lg:first-child>*{
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

.\[\&\>\*\]\:last\:rounded-b-lg:last-child>*{
  border-bottom-right-radius: 24px;
  border-bottom-left-radius: 24px;
}

.\[\&\>\*\]\:last\:border-b-0:last-child>*{
  border-bottom-width: 0px;
}

.\[\&\>div\]\:bg-transparent>div{
  background-color: transparent;
}

.\[\&\>div\]\:p-0>div{
  padding: 0px;
}

.\[\&\>span\]\:items-center>span{
  align-items: center;
}

.\[\&\>span\]\:px-2>span{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&\>span\]\:py-0>span{
  padding-top: 0px;
  padding-bottom: 0px;
}

.\[\&_\*\]\:cursor-pointer *{
  cursor: pointer;
}

.\[\&_p\]\:inline p{
  display: inline;
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/6d93bde91c0c2823-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/a34f9d1faa5f3315-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_d65c78 {font-family: 'Inter', 'Inter Fallback';font-style: normal
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/layouts/container.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
.page-wrapper {
    margin-left: calc(80px + 240px);
    transition: .2s ease-in;
}

@media screen and (max-width:1280px) {
    .page-wrapper{
        margin-left: 0;
    }
}


/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/layouts/sidebar.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
.minisidebar-icon {
    position: fixed;
    left: 0;
    width: 80px;
    height: 100%;
    top: 0;
}

.menu-sidebar {
    height: 100%;
    width: 240px !important;
    background: var(--bs-white);
    top: 0;
    box-shadow: 7px 7px 10px rgba(0, 0, 0, .03);
    left: 80px;
    transition: left .4s ease-in-out;
}

.logo img {
    margin: 0 auto;
}

.miniicons {
    height: calc(100vh - 150px);
}

.sidebar-nav .caption {
    padding-top: 20px !important;
    margin-top: 20px !important;
    border-top: 1px dashed var(--color-border);
}

.sidebar-nav .caption:first-child {
    padding-top: 0 !important;
    margin-top: 0px !important;
    border-top: 0 !important;
}

.sidebar-dropdown li a {
    opacity: 0.9;
    padding: 8px 16px;
    background-color: transparent !important;
}

.sidebar-dropdown li a:hover {
    background-color: transparent !important;
    opacity: 1;
}

.sidebar-dropdown li button:hover {
    background-color: transparent !important;
}

.sidebar-dropdown li .iconify--fad {
    height: 9px;
    width: 9px;
    margin: 0 4px;
}

[data-sidebar-type="mini-sidebar"] .page-wrapper {
    margin-left: 80px;
}

/* For Minisidebar Tooltip Fix */
.miniicons [data-testid="flowbite-tooltip"] {
    width: max-content;
    background-color: var(--color-primary);
}

.miniicons [data-testid="flowbite-tooltip-arrow"] {
    background-color: var(--color-primary);
}
.miniicons .flowbite-tooltip{
  position: fixed !important;
}



@media (min-width: 1199px) {
    [data-sidebar-type="mini-sidebar"] .menu-sidebar {
        width: 0px !important;
        overflow: hidden;
        left: -240px;
        padding: 0 !important;
    }

    [data-sidebar-type="mini-sidebar"] .menu-sidebar .hide-menu {
        display: none;
    }

}
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/layouts/horizontal.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
.horizontal-nav ul li .active{
    background-color:var(--color-lightprimary);
    color: var(--color-primary);
}

[data-layout='horizontal'] .page-wrapper {
    margin-left: 0;
    transition: none;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/layouts/header.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/* Base Layout */
.mobile-header-menu {
    height: 0;
    overflow: hidden;
    transition: all .3s ease-in-out;
}

.mobile-header-menu.active {
    min-height: 70px;
    overflow: visible;
}

/* Fixed Layout Structure */
.dashboard-layout {
    min-height: 100vh;
    width: 100%;
    position: relative;
    overflow-x: hidden;
}

/* Sidebar Styles */
.minisidebar-icon {
    width: 5rem;
    min-width: 5rem;
    position: fixed;
    height: 100vh;
    z-index: 40;
    transition: width 0.3s ease-in-out;
    will-change: width;
}

.menu-sidebar {
    width: calc(280px - 5rem);
    min-width: calc(280px - 5rem);
    position: fixed;
    height: 100vh;
    margin-left: 5rem;
    z-index: 30;
    transition: transform 0.3s ease-in-out;
    will-change: transform;
}

/* Content Area */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    width: calc(100% - 280px);
    transition: margin-left 0.3s ease-in-out;
    will-change: margin-left;
}

/* Dark Mode Support */
:root {
    --sidebar-bg: #ffffff;
    --sidebar-border: #e5e7eb;
}

.dark {
    --sidebar-bg: #1f2937;
    --sidebar-border: #374151;
}

/* Prevent Layout Shift */
.sidebar-container {
    width: 280px;
    min-width: 280px;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    z-index: 20;
}

/* Performance Optimizations */
.sidebar-container,
.menu-sidebar,
.main-content {
    backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-font-smoothing: subpixel-antialiased;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/dashboards.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
.rounded-bars .apexcharts-bar-series.apexcharts-plot-series .apexcharts-series path {
    clip-path: inset(0 0 5% 0 round 20px);
}

.rounded-circle .apexcharts-pie-area {
    clip-path: inset(0 13% 33% 11% round 61px);
}

body .apexcharts-tooltip {
    border-radius: 16px;
}

body .apexcharts-tooltip-marker {
    border-radius: 4px;
    width: 12px;
    height: 4px;
}

body .apexcharts-tooltip.apexcharts-theme-dark {
    background: rgba(17, 28, 45, 0.8);

    .apexcharts-tooltip-title {
        border-bottom: 0;
        background: rgba(17, 28, 45, 0.7);
    }
}

body .apexcharts-tooltip-series-group {
    padding: 0 14px;
}

body .apexcharts-tooltip-title {
    padding: 10px 14px;
}

.timeline-tabs .timeline .timeline-icon {
    position: relative;
}
.timeline-tabs .timeline .timeline-icon:after {
    content: "";
    position: absolute;
    top: 40px;
    height: 20px;
    width: 1px;
    background-color: var(--color-border);
    left: 0;
    right: 0;
    margin: 0 auto;
}
.timeline-tabs .timeline:last-child .timeline-icon:after {
    height: 0;
}
.timeline-tabs .timeline-list {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 8px;
}
.timeline-tabs .timeline-list:last-child {
    border-bottom: 0 !important;
}

#vector-map svg .dxm-background{
    stroke: transparent !important;
}
#vector-map svg path{
    fill: #C9D6DE !important;
    stroke-width: 0.2px;
}
.dxm-tooltip svg path{
    fill: var(--color-dark) !important;
    stroke-width: 0;
}
.dxm-tooltip div{
    color: #fff !important;
    font-family: inherit !important;
}
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/widgets.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
.bg-primary-gt {
    background: linear-gradient(261.23deg, var(--color-primary), #5a52ff 100%) !important;
}
.revenuechart .apexcharts-bar-series.apexcharts-plot-series .apexcharts-series path {
    clip-path: inset(0 0 5% 0 round 20px);
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/landingpage.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
.animted-img {
    position: absolute;
    z-index: 9;
    top: 35%;
    left: 10%;
    animation: mover 5s infinite alternate;
    border-radius: 30px;
  }
  
  .animted-img-2 {
    position: absolute;
    z-index: 9;
    top: 25%;
    right: 10%;
    animation: mover 5s infinite alternate;
    border-radius: 30px;
  }
  
  @keyframes mover {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-10px);
    }
  }
  
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/auth.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
.circle-top {
    position: absolute;
    top: -33%;
    left: -14%;
    border: 120px solid #0b2947;
    height: 700px;
    width: 700px;
    display: block;
    background: 0 0;
    border-radius: 100%;
    z-index: 1;
}
.circle-bottom {
    position: absolute;
    bottom: -4%;
    right: 1%;
    height: 450px;
    width: 450px;
    display: block;
    background: 0 0;
    z-index: 1;
    opacity: .5;
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/app.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
.feature-card img {
  width: 100%;
  object-fit: cover;
}

.card-hover {
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.card-hover:hover img {
  transform: scale(1.09);
  transition: transform .2s;
}

/* Calendar */
.rbc-event.event-azure {
  background-color: var(--color-primary);
}

.rbc-event.event-primary {
  background-color: var(--color-primary);
  ;
}

.rbc-event.event-default {
  background-color: var(--color-secondary);
}

.rbc-event.event-red {
  background-color: var(--color-error);
  ;
}

.rbc-event.event-green {
  background-color: var(--color-success);
}

.rbc-event.event-warning {
  background-color: var(--color-warning);
  ;
}

.rbc-off-range-bg {
  background: transparent !important;
}

.rbc-today {
  background-color: var(--color-muted);
}

.rbc-month-row+.rbc-month-row,
.rbc-day-bg+.rbc-day-bg,
.rbc-header,
.rbc-month-view,
.rbc-toolbar button,
.rbc-time-content>*+*>* {
  border-color: var(--color-border) !important;
}

.rbc-header {
  padding: 10px 3px !important;
}

.rbc-day-bg+.rbc-day-bg {
  border-left: 1px solid var(--color-border) !important;
}

.rbc-event:focus,
.rbc-day-slot .rbc-background-event:focus {
  outline: none;
}

.rbc-toolbar {
  margin-bottom: 30px !important;
}

.rbc-toolbar button {
  background-color: var(--color-muted) !important;
  border: 0 !important;
  padding: 10px 15px !important;
  font-size: 14px;
}

.rbc-toolbar button:focus {
  color: var(--color-primary) !important;
}

.rbc-toolbar button:active,
.rbc-toolbar button.rbc-active {
  background-color: var(--color-lightprimary) !important;
  color: var(--color-primary) !important;
  box-shadow: none !important;
  border-radius: 0px;
}

.rbc-toolbar button:hover {
  color: var(--color-primary) !important;
}

.rbc-btn-group {
  border-radius: 30px !important;
  overflow: hidden;
  border-color: 1px solid var(--color-border) !important;
}
.calendar > .absolute{
  top: 51% !important;
}
.calendarSec > .absolute{
  top: 67% !important;
}

.btn-primary {
  background-color: #2b2b2b;
}

.btn-secondary {
  background-color: #1a97f5;
}

@media (max-width: 767px) {
  .rbc-btn-group {
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
  }

  .rbc-toolbar .rbc-toolbar-label {
    margin-bottom: 15px;
    display: block;
  }

  .rbc-calendar {
    height: 100vh !important;
  }
}

.rbc-calendar {
  min-height: 600px;
}

.email-content p {
  margin: 12px 0;
}

.product-thumb .slick-current img {
  border: 2px solid var(--color-primary) !important;
}

.product-thumb .slick-slide img {
  border: 2px solid transparent;
}

.slick-prev:before,
.slick-next:before {
  color: #2b2b2b
}

.slick-next {
  right: 15px
}

.slick-prev {
  left: 15px;
  z-index: 1;
}


.ql-container {
  min-height: 200px;
  border-radius: 0 0 9px 9px;
  font-family: inherit !important;

}

.ql-toolbar.ql-snow {
  border-radius: 9px 9px 0 0;
  border: 1px solid var(--color-border) !important;
  font-family: inherit !important;
}

.ql-toolbar.ql-snow+.ql-container.ql-snow {
  border-top: 0 !important;
}

.darkbg .ql-container.ql-snow,
.darkbg .ql-toolbar.ql-snow,
.darkbg .ql-snow .ql-tooltip input[type='text'] {
  border-color: var(--color-border) !important;
}

.darkbg .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg,
.darkbg .ql-snow.ql-toolbar button svg,
.ql-snow .ql-toolbar button svg {
  filter: brightness(0) invert(1);
}

.darkbg .ql-snow .ql-picker,
.darkbg .ql-snow .ql-tooltip input[type='text'] {
  color: white;
}

.darkbg .ql-snow .ql-picker-options,
.darkbg .ql-snow .ql-tooltip,
.darkbg .ql-snow .ql-tooltip input[type='text'] {
  background-color: #484b52;
}

.darkbg .ql-snow .ql-tooltip[data-mode='link']::before {
  color: white;
}

.ql-editor,
.ql-snow * {
  font-size: 16px;
  font-family: inherit !important;
}

.ql-editor.ql-blank::before {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  color: #777e89;
}

.ql-container.ql-snow {
  border: 0;
}

.ql-toolbar.ql-snow {
  border-width: 0 0 1px 0;
}

.ql-toolbar.ql-snow .ql-picker-label,
.ql-toolbar.ql-snow .ql-picker-options {
  border: 0;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  top: 33px;
}

.ql-snow .ql-picker-options {
  padding: 5px 19px;
  box-shadow: none;
  border-radius: 9px;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: inherit;
}

.ql-snow .ql-tooltip {
  left: 0 !important;
  border: 0;
  box-shadow: 0 5px 15px rgb(0 0 0 / 12%);
  border-radius: 5px;
}

.ql-snow .ql-tooltip input[type='text'] {
  height: auto;
  padding: 5px 8px;
  border-radius: 5px;
  outline: 0;
}

.ql-container.ql-snow {
  border: 1px solid var(--color-border) !important;
}

.react-datepicker-wrapper {
  display: block !important;
}

/* For Range Slider Dot Color */
#default-range::-webkit-slider-thumb {
  background-color: var(--color-primary);
}

#default-range::-moz-range-thumb {
  background-color: var(--color-primary);
}

#default-range::-ms-thumb {
  background-color: var(--color-primary);
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/theme/default-colors.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
:root{
    --color-info:#46caeb;
    --color-success:#00ceb6;
    --color-warning:#FFB900;
    --color-error:#FF6692;
    --color-lightsuccess:#00ceb625;
    --color-lighterror:#FF669230;
    --color-lightinfo:#46caeb25;
    --color-lightwarning:#FFB90025;
    --color-white:#fff ;
	--color-dark:#111c2d;
    --color-border:#e0e6eb;
    --color-bordergray:#cfd6db;
    --color-darkborder:#333F55;
    --color-link:#2a3547;
    --color-muted:#EFF4FA;
    --color-darkmuted:#2A3851;
    --color-darklink:#7b8893;
    --color-lightgray:#F8FAFD;
    --color-lighthover:#F6F9FC;
    --color-darkgray:#1A2537;
    --color-error-emphasis:#d9577c;
    --color-warning-emphasis:#d9941a;
    --color-success-emphasis:#10bd9d;
    --color-info-emphasis:#3cacc8;
    --color-surface-ld:#ffffff;
}

[data-color-theme="BLUE_THEME"]{
    --color-primary: #00A1FF;
    --color-secondary: #8965E5;
    --color-primary-emphasis:#0091E5;   
    --color-secondary-emphasis:#7c57d9;
    --color-lightprimary:#00A1FF20;
    --color-lightsecondary:#8965E520; 
}
[data-color-theme="AQUA_THEME"]{
    --color-primary: #0074BA;
    --color-bghover:#00639e ; 
    --color-primary-emphasis:#00639e;   
    --color-secondary-emphasis:#3cb7a0;
    --color-secondary: #47D7BC;
    --color-lightprimary:#0074BA20;
    --color-lightsecondary:#00639e20;
}
[data-color-theme="PURPLE_THEME"]{
    --color-primary: #763EBD;
    --color-primary-emphasis:#6435a1;   
    --color-secondary-emphasis:#7fb0b5;
    --color-secondary: #49BEFF;
    --color-lightprimary:#763EBD20;
    --color-lightsecondary:#49BEFF20;
}
[data-color-theme="GREEN_THEME"]{
    --color-primary: #0A7EA4;
    --color-primary-emphasis:#096b8b;   
    --color-secondary-emphasis:#d4e069;
    --color-secondary: #CCDA4E;
    --color-lightprimary:#0A7EA420;
    --color-lightsecondary:#CCDA4E20;
    
}
[data-color-theme="CYAN_THEME"]{
    --color-primary: #01C0C8;
    --color-primary-emphasis:#01a3aa;   
    --color-secondary-emphasis:#d58066;
    --color-secondary: #FB9678;
    --color-lightprimary:#01C0C820;
    --color-lightsecondary:#FB967820;
}
[data-color-theme="ORANGE_THEME"]{
    --color-primary: #FA896B;
    --color-primary-emphasis:#d5745b;   
    --color-secondary-emphasis:#00639e;
    --color-secondary: #0074BA;
    --color-lightprimary:#FA896B20;
    --color-lightsecondary:#0074BA20;
}



/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/theme/dark.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
.dark .sidebar-nav .caption {
    border-top: 1px dashed var(--color-darkborder);
}

.dark:root{
    --color-surface-ld: #1A2537;
}

.dark .timeline-tabs .timeline .timeline-icon:after {
    background-color: var(--color-darkborder);
}

.dark .timeline-tabs .timeline-list {
    border-bottom: 1px solid var(--color-darkborder);
}

.dark #vector-map svg .dxm-background {
    fill: transparent !important;
}

.dark #vector-map svg path {
    stroke-width: 0;
}

.dark .rbc-today {
    background-color: var(--color-darkmuted);
}

.dark .rbc-month-row+.rbc-month-row,
.dark .rbc-day-bg+.rbc-day-bg,
.dark .rbc-header,
.dark .rbc-month-view,
.dark .rbc-toolbar button,
.dark .rbc-time-content>*+*>* {
    border-color: var(--color-darkborder) !important;
}

.dark .rbc-day-bg+.rbc-day-bg {
    border-left: 1px solid var(--color-darkborder) !important;
}

.dark .rbc-toolbar button {
    background-color: var(--color-darkmuted) !important;
    color: var(--color-white);
}


.dark .rbc-toolbar button:active,
.dark .rbc-toolbar button.rbc-active {
    background-color: var(--color-lightprimary) !important;
}

.dark .ql-toolbar.ql-snow {
    border: 1px solid var(--color-darkborder) !important;
}

.dark .ql-container.ql-snow {
    border: 1px solid var(--color-darkborder) !important;
}
.dark .code-modal pre{
    background-color: var(--color-darkmuted) !important;
    color: inherit !important;
}
/* For input Date */
.dark ::-webkit-calendar-picker-indicator {
    filter: invert(1);
  }

  .dark .miniicons [data-testid="flowbite-tooltip"] {
    width: max-content;
    background-color: var(--color-primary);
}

.dark .miniicons [data-testid="flowbite-tooltip-arrow"] {
    background-color: var(--color-primary);
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/override/reboot.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
.megamenu {
  transform: translateY(38px) !important;
}

.simplebar-scrollbar:before {
  background: var(--color-border) !important;
}

.translate {
  transform: translate(-88%, 40px) !important;
  transition: 0.5s;
}

.translate-lng {
  transform: translate(-81%, 40px) !important;
  transition: 0.5s;
}

.h-n80 {
  height: calc(100vh - 80px);
}

/* styles/globals.css */
.dropdown-enter {
  opacity: 0;
  transform: scale(0.95);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translate(0);
  transition: opacity 150ms, transform 150ms;
}

.dropdown-leave {
  opacity: 1;
  transform: scale(1);
}

.dropdown-leave-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 150ms, transform 150ms;
}

.two-cols {
  -webkit-columns: 4;
  -moz-columns: 4;
  columns: 4;
}
[data-simplebar], .simplebar-offset, .simplebar-content,.simplebar-wrapper,.simplebar-mask,.simplebar-content-wrapper{
  outline:none !important;
  border:none !important
}

[type='checkbox'], [type='radio']{
  color: var(--color-primary) !important;
}


/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/layouts/rtl.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
html[dir="rtl"] .page-wrapper {
    margin-right: calc(240px + 80px);
    margin-left: 0;
}

html[dir="rtl"] .menu-sidebar {
    left: unset;
    right: 80px;
}

html[dir="rtl"][data-sidebar-type="mini-sidebar"] .page-wrapper {
    margin-left: unset;
    margin-right: 80px;
}

html[dir="rtl"][data-layout='horizontal'] .page-wrapper{
    margin-right:0 !important
}

@media (max-width: 1199px) {
    html[dir="rtl"][data-sidebar-type="mini-sidebar"] .menu-sidebar{
        left:unset;
        right:-240px 
    }
    html[dir="rtl"] .page-wrapper {
        margin-right: 0 !important;
    }
}
html[dir="rtl"] select:not([size]){
    padding-left: 35px;
}


html[dir="rtl"] .megamenu {
    transform: translate(-95%, 40px) !important;
}

html[dir="rtl"] .translate {
    transform: translate(0px, 40px) !important;
}

html[dir="rtl"] .translate-lng{
    transform: translate(0px, 40px) !important;
}

html[dir="rtl"] table tr th,html[dir="rtl"] table tr td{
    text-align: right;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/css/pages/frontpages.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
.bg-lightbtn {
    background-color: rgba(255, 255, 255, 0.15);
}

.container-1218 {
    max-width: 1218px;
    padding: 0 20px;
}


.marquee1-group {
    animation: marquee 45s linear infinite;
}

.marquee2-group {
    animation: marquee2 45s linear infinite;
}


@keyframes marquee {
    0% {
        transform: translate3d(0, 0, 0);
    }

    100% {
        transform: translate3d(-2086px, 0, 0);
    }
}

@keyframes marquee2 {
    0% {
        transform: translate3d(-2086px, 0, 0)
    }

    100% {
        transform: translate3d(0, 0, 0)
    }
}

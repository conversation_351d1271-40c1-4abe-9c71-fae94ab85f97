"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-i18next";
exports.ids = ["vendor-chunks/react-i18next"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/I18nextProvider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nextProvider: () => (/* binding */ I18nextProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext.Provider, {\n    value\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL0kxOG5leHRQcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDSjtBQUNwQztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxnQkFBZ0IsOENBQU87QUFDdkI7QUFDQTtBQUNBLEdBQUc7QUFDSCxTQUFTLG9EQUFhLENBQUMsb0RBQVc7QUFDbEM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRGVza3RvcFxcUmVzdCBEZWxpdmVyeVxccmVzdGF1cmFudF9tYW5nZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcSTE4bmV4dFByb3ZpZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBJMThuQ29udGV4dCB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gSTE4bmV4dFByb3ZpZGVyKHtcbiAgaTE4bixcbiAgZGVmYXVsdE5TLFxuICBjaGlsZHJlblxufSkge1xuICBjb25zdCB2YWx1ZSA9IHVzZU1lbW8oKCkgPT4gKHtcbiAgICBpMThuLFxuICAgIGRlZmF1bHROU1xuICB9KSwgW2kxOG4sIGRlZmF1bHROU10pO1xuICByZXR1cm4gY3JlYXRlRWxlbWVudChJMThuQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlXG4gIH0sIGNoaWxkcmVuKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Trans.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Trans.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_2__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return (0,_TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans)({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Trans.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/TransWithoutContext.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* binding */ nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html-parse-stringify */ \"(ssr)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\n\n\n\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(react__WEBPACK_IMPORTED_MODULE_0__.isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nconst nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...react__WEBPACK_IMPORTED_MODULE_0__.Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(c.type, {\n          ...props,\n          key: i,\n          ref: c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && child.dummy && !isElement;\n        const isKnownComponent = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(children) && Object.hasOwnProperty.call(children, node.name);\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, comp);\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Componentized);\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__.getI18n)();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...(0,_defaults_js__WEBPACK_IMPORTED_MODULE_3__.getDefaults)(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  const content = renderNodes(generatedComponents || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(useAsParent, additionalProps, content) : content;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Translation.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Translation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translation: () => (/* binding */ Translation)\n/* harmony export */ });\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n\nconst Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_0__.useTranslation)(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL1RyYW5zbGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELDJCQUEyQixrRUFBYztBQUN6QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRGVza3RvcFxcUmVzdCBEZWxpdmVyeVxccmVzdGF1cmFudF9tYW5nZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcVHJhbnNsYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICcuL3VzZVRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCBjb25zdCBUcmFuc2xhdGlvbiA9ICh7XG4gIG5zLFxuICBjaGlsZHJlbixcbiAgLi4ub3B0aW9uc1xufSkgPT4ge1xuICBjb25zdCBbdCwgaTE4biwgcmVhZHldID0gdXNlVHJhbnNsYXRpb24obnMsIG9wdGlvbnMpO1xuICByZXR1cm4gY2hpbGRyZW4odCwge1xuICAgIGkxOG4sXG4gICAgbG5nOiBpMThuLmxhbmd1YWdlXG4gIH0sIHJlYWR5KTtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Translation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   ReportNamespaces: () => (/* binding */ ReportNamespaces),\n/* harmony export */   composeInitialProps: () => (/* binding */ composeInitialProps),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n),\n/* harmony export */   getInitialProps: () => (/* binding */ getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__.initReactI18next),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.setI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n\n\n\n\n\nconst I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\nclass ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nconst composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nconst getInitialProps = () => {\n  const i18n = (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(ssr)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nconst setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nconst getDefaults = () => defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ08saUNBQWlDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEZXNrdG9wXFxSZXN0IERlbGl2ZXJ5XFxyZXN0YXVyYW50X21hbmdlXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxkZWZhdWx0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1bmVzY2FwZSB9IGZyb20gJy4vdW5lc2NhcGUuanMnO1xubGV0IGRlZmF1bHRPcHRpb25zID0ge1xuICBiaW5kSTE4bjogJ2xhbmd1YWdlQ2hhbmdlZCcsXG4gIGJpbmRJMThuU3RvcmU6ICcnLFxuICB0cmFuc0VtcHR5Tm9kZVZhbHVlOiAnJyxcbiAgdHJhbnNTdXBwb3J0QmFzaWNIdG1sTm9kZXM6IHRydWUsXG4gIHRyYW5zV3JhcFRleHROb2RlczogJycsXG4gIHRyYW5zS2VlcEJhc2ljSHRtbE5vZGVzRm9yOiBbJ2JyJywgJ3N0cm9uZycsICdpJywgJ3AnXSxcbiAgdXNlU3VzcGVuc2U6IHRydWUsXG4gIHVuZXNjYXBlXG59O1xuZXhwb3J0IGNvbnN0IHNldERlZmF1bHRzID0gKG9wdGlvbnMgPSB7fSkgPT4ge1xuICBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgICAuLi5kZWZhdWx0T3B0aW9ucyxcbiAgICAuLi5vcHRpb25zXG4gIH07XG59O1xuZXhwb3J0IGNvbnN0IGdldERlZmF1bHRzID0gKCkgPT4gZGVmYXVsdE9wdGlvbnM7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nconst setI18n = instance => {\n  i18nInstance = instance;\n};\nconst getI18n = () => i18nInstance;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2kxOG5JbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRGVza3RvcFxcUmVzdCBEZWxpdmVyeVxccmVzdGF1cmFudF9tYW5nZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcaTE4bkluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBpMThuSW5zdGFuY2U7XG5leHBvcnQgY29uc3Qgc2V0STE4biA9IGluc3RhbmNlID0+IHtcbiAgaTE4bkluc3RhbmNlID0gaW5zdGFuY2U7XG59O1xuZXhwb3J0IGNvbnN0IGdldEkxOG4gPSAoKSA9PiBpMThuSW5zdGFuY2U7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.I18nContext),\n/* harmony export */   I18nextProvider: () => (/* reexport safe */ _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__.I18nextProvider),\n/* harmony export */   Trans: () => (/* reexport safe */ _Trans_js__WEBPACK_IMPORTED_MODULE_0__.Trans),\n/* harmony export */   TransWithoutContext: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans),\n/* harmony export */   Translation: () => (/* reexport safe */ _Translation_js__WEBPACK_IMPORTED_MODULE_4__.Translation),\n/* harmony export */   composeInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.composeInitialProps),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.getI18n),\n/* harmony export */   getInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__.initReactI18next),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   plural: () => (/* binding */ plural),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectOrdinal: () => (/* binding */ selectOrdinal),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.setI18n),\n/* harmony export */   time: () => (/* binding */ time),\n/* harmony export */   useSSR: () => (/* reexport safe */ _useSSR_js__WEBPACK_IMPORTED_MODULE_7__.useSSR),\n/* harmony export */   useTranslation: () => (/* reexport safe */ _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__.useTranslation),\n/* harmony export */   withSSR: () => (/* reexport safe */ _withSSR_js__WEBPACK_IMPORTED_MODULE_6__.withSSR),\n/* harmony export */   withTranslation: () => (/* reexport safe */ _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__.withTranslation)\n/* harmony export */ });\n/* harmony import */ var _Trans_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Trans.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Trans.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./withTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\");\n/* harmony import */ var _Translation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Translation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Translation.js\");\n/* harmony import */ var _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./I18nextProvider.js */ \"(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\");\n/* harmony import */ var _withSSR_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./withSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst date = () => '';\nconst time = () => '';\nconst number = () => '';\nconst select = () => '';\nconst plural = () => '';\nconst selectOrdinal = () => '';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUM7QUFDcUM7QUFDbkI7QUFDRTtBQUNSO0FBQ1E7QUFDaEI7QUFDRjtBQUNvQjtBQUNBO0FBQ0o7QUFDNEI7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERlc2t0b3BcXFJlc3QgRGVsaXZlcnlcXHJlc3RhdXJhbnRfbWFuZ2VcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IFRyYW5zIH0gZnJvbSAnLi9UcmFucy5qcyc7XG5leHBvcnQgeyBUcmFucyBhcyBUcmFuc1dpdGhvdXRDb250ZXh0IH0gZnJvbSAnLi9UcmFuc1dpdGhvdXRDb250ZXh0LmpzJztcbmV4cG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnLi91c2VUcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgeyB3aXRoVHJhbnNsYXRpb24gfSBmcm9tICcuL3dpdGhUcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgeyBUcmFuc2xhdGlvbiB9IGZyb20gJy4vVHJhbnNsYXRpb24uanMnO1xuZXhwb3J0IHsgSTE4bmV4dFByb3ZpZGVyIH0gZnJvbSAnLi9JMThuZXh0UHJvdmlkZXIuanMnO1xuZXhwb3J0IHsgd2l0aFNTUiB9IGZyb20gJy4vd2l0aFNTUi5qcyc7XG5leHBvcnQgeyB1c2VTU1IgfSBmcm9tICcuL3VzZVNTUi5qcyc7XG5leHBvcnQgeyBpbml0UmVhY3RJMThuZXh0IH0gZnJvbSAnLi9pbml0UmVhY3RJMThuZXh0LmpzJztcbmV4cG9ydCB7IHNldERlZmF1bHRzLCBnZXREZWZhdWx0cyB9IGZyb20gJy4vZGVmYXVsdHMuanMnO1xuZXhwb3J0IHsgc2V0STE4biwgZ2V0STE4biB9IGZyb20gJy4vaTE4bkluc3RhbmNlLmpzJztcbmV4cG9ydCB7IEkxOG5Db250ZXh0LCBjb21wb3NlSW5pdGlhbFByb3BzLCBnZXRJbml0aWFsUHJvcHMgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGNvbnN0IGRhdGUgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCB0aW1lID0gKCkgPT4gJyc7XG5leHBvcnQgY29uc3QgbnVtYmVyID0gKCkgPT4gJyc7XG5leHBvcnQgY29uc3Qgc2VsZWN0ID0gKCkgPT4gJyc7XG5leHBvcnQgY29uc3QgcGx1cmFsID0gKCkgPT4gJyc7XG5leHBvcnQgY29uc3Qgc2VsZWN0T3JkaW5hbCA9ICgpID0+ICcnOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luaXRSZWFjdEkxOG5leHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0E7QUFDckM7QUFDUDtBQUNBO0FBQ0EsSUFBSSx5REFBVztBQUNmLElBQUkseURBQU87QUFDWDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERlc2t0b3BcXFJlc3QgRGVsaXZlcnlcXHJlc3RhdXJhbnRfbWFuZ2VcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGluaXRSZWFjdEkxOG5leHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2V0RGVmYXVsdHMgfSBmcm9tICcuL2RlZmF1bHRzLmpzJztcbmltcG9ydCB7IHNldEkxOG4gfSBmcm9tICcuL2kxOG5JbnN0YW5jZS5qcyc7XG5leHBvcnQgY29uc3QgaW5pdFJlYWN0STE4bmV4dCA9IHtcbiAgdHlwZTogJzNyZFBhcnR5JyxcbiAgaW5pdChpbnN0YW5jZSkge1xuICAgIHNldERlZmF1bHRzKGluc3RhbmNlLm9wdGlvbnMucmVhY3QpO1xuICAgIHNldEkxOG4oaW5zdGFuY2UpO1xuICB9XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VuZXNjYXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx5SEFBeUg7QUFDekg7QUFDQSxRQUFRO0FBQ1IsUUFBUTtBQUNSLE9BQU87QUFDUCxRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsV0FBVztBQUNYLFVBQVU7QUFDVixTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEZXNrdG9wXFxSZXN0IERlbGl2ZXJ5XFxyZXN0YXVyYW50X21hbmdlXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFx1bmVzY2FwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtYXRjaEh0bWxFbnRpdHkgPSAvJig/OmFtcHwjMzh8bHR8IzYwfGd0fCM2MnxhcG9zfCMzOXxxdW90fCMzNHxuYnNwfCMxNjB8Y29weXwjMTY5fHJlZ3wjMTc0fGhlbGxpcHwjODIzMHwjeDJGfCM0Nyk7L2c7XG5jb25zdCBodG1sRW50aXRpZXMgPSB7XG4gICcmYW1wOyc6ICcmJyxcbiAgJyYjMzg7JzogJyYnLFxuICAnJmx0Oyc6ICc8JyxcbiAgJyYjNjA7JzogJzwnLFxuICAnJmd0Oyc6ICc+JyxcbiAgJyYjNjI7JzogJz4nLFxuICAnJmFwb3M7JzogXCInXCIsXG4gICcmIzM5Oyc6IFwiJ1wiLFxuICAnJnF1b3Q7JzogJ1wiJyxcbiAgJyYjMzQ7JzogJ1wiJyxcbiAgJyZuYnNwOyc6ICcgJyxcbiAgJyYjMTYwOyc6ICcgJyxcbiAgJyZjb3B5Oyc6ICfCqScsXG4gICcmIzE2OTsnOiAnwqknLFxuICAnJnJlZzsnOiAnwq4nLFxuICAnJiMxNzQ7JzogJ8KuJyxcbiAgJyZoZWxsaXA7JzogJ+KApicsXG4gICcmIzgyMzA7JzogJ+KApicsXG4gICcmI3gyRjsnOiAnLycsXG4gICcmIzQ3Oyc6ICcvJ1xufTtcbmNvbnN0IHVuZXNjYXBlSHRtbEVudGl0eSA9IG0gPT4gaHRtbEVudGl0aWVzW21dO1xuZXhwb3J0IGNvbnN0IHVuZXNjYXBlID0gdGV4dCA9PiB0ZXh0LnJlcGxhY2UobWF0Y2hIdG1sRW50aXR5LCB1bmVzY2FwZUh0bWxFbnRpdHkpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/unescape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useSSR.js":
/*!******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useSSR.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSSR: () => (/* binding */ useSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nconst useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VzZVNTUi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDaUI7QUFDN0MsNkRBQTZEO0FBQ3BFO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUksRUFBRSxpREFBVSxDQUFDLG9EQUFXO0FBQzVCLG1EQUFtRCxvREFBTztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pbmlzdHJhdG9yXFxEZXNrdG9wXFxSZXN0IERlbGl2ZXJ5XFxyZXN0YXVyYW50X21hbmdlXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFx1c2VTU1IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGdldEkxOG4sIEkxOG5Db250ZXh0IH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmV4cG9ydCBjb25zdCB1c2VTU1IgPSAoaW5pdGlhbEkxOG5TdG9yZSwgaW5pdGlhbExhbmd1YWdlLCBwcm9wcyA9IHt9KSA9PiB7XG4gIGNvbnN0IHtcbiAgICBpMThuOiBpMThuRnJvbVByb3BzXG4gIH0gPSBwcm9wcztcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tQ29udGV4dFxuICB9ID0gdXNlQ29udGV4dChJMThuQ29udGV4dCkgfHwge307XG4gIGNvbnN0IGkxOG4gPSBpMThuRnJvbVByb3BzIHx8IGkxOG5Gcm9tQ29udGV4dCB8fCBnZXRJMThuKCk7XG4gIGlmIChpMThuLm9wdGlvbnM/LmlzQ2xvbmUpIHJldHVybjtcbiAgaWYgKGluaXRpYWxJMThuU3RvcmUgJiYgIWkxOG4uaW5pdGlhbGl6ZWRTdG9yZU9uY2UpIHtcbiAgICBpMThuLnNlcnZpY2VzLnJlc291cmNlU3RvcmUuZGF0YSA9IGluaXRpYWxJMThuU3RvcmU7XG4gICAgaTE4bi5vcHRpb25zLm5zID0gT2JqZWN0LnZhbHVlcyhpbml0aWFsSTE4blN0b3JlKS5yZWR1Y2UoKG1lbSwgbG5nUmVzb3VyY2VzKSA9PiB7XG4gICAgICBPYmplY3Qua2V5cyhsbmdSZXNvdXJjZXMpLmZvckVhY2gobnMgPT4ge1xuICAgICAgICBpZiAobWVtLmluZGV4T2YobnMpIDwgMCkgbWVtLnB1c2gobnMpO1xuICAgICAgfSk7XG4gICAgICByZXR1cm4gbWVtO1xuICAgIH0sIGkxOG4ub3B0aW9ucy5ucyk7XG4gICAgaTE4bi5pbml0aWFsaXplZFN0b3JlT25jZSA9IHRydWU7XG4gICAgaTE4bi5pc0luaXRpYWxpemVkID0gdHJ1ZTtcbiAgfVxuICBpZiAoaW5pdGlhbExhbmd1YWdlICYmICFpMThuLmluaXRpYWxpemVkTGFuZ3VhZ2VPbmNlKSB7XG4gICAgaTE4bi5jaGFuZ2VMYW5ndWFnZShpbml0aWFsTGFuZ3VhZ2UpO1xuICAgIGkxOG4uaW5pdGlhbGl6ZWRMYW5ndWFnZU9uY2UgPSB0cnVlO1xuICB9XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useTranslation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst usePrevious = (value, ignore) => {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nconst useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new _context_js__WEBPACK_IMPORTED_MODULE_1__.ReportNamespaces();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue)) return optsOrDefaultValue;\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(optsOrDefaultValue) && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...(0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults)(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasLoadedNamespace)(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => resolve());\n    }\n  });\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VzZVRyYW5zbGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNkU7QUFDTTtBQUMwQjtBQUM3RztBQUNBLGNBQWMsNkNBQU07QUFDcEIsRUFBRSxnREFBUztBQUNYO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLCtEQUErRCxrREFBVztBQUNuRSxzQ0FBc0M7QUFDN0M7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxJQUFJLEVBQUUsaURBQVUsQ0FBQyxvREFBVztBQUM1QixtREFBbUQsb0RBQU87QUFDMUQsa0VBQWtFLHlEQUFnQjtBQUNsRjtBQUNBLElBQUksbURBQVE7QUFDWjtBQUNBLFVBQVUsbURBQVE7QUFDbEIsVUFBVSxtREFBUSx3QkFBd0IsbURBQVE7QUFDbEQ7QUFDQTtBQUNBLHNDQUFzQztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLG1EQUFRO0FBQ3hDO0FBQ0EsT0FBTyx3REFBVztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxlQUFlLG1EQUFRO0FBQ3ZCO0FBQ0EsMkZBQTJGLDZEQUFrQjtBQUM3RztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0NBQVE7QUFDNUI7QUFDQSwrQkFBK0IsVUFBVSxFQUFFLFNBQVM7QUFDcEQ7QUFDQSxvQkFBb0IsNkNBQU07QUFDMUIsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxRQUFRLHdEQUFhO0FBQ3JCO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUixRQUFRLHlEQUFjO0FBQ3RCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLHdEQUFhO0FBQ25CLE1BQU07QUFDTixNQUFNLHlEQUFjO0FBQ3BCO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERlc2t0b3BcXFJlc3QgRGVsaXZlcnlcXHJlc3RhdXJhbnRfbWFuZ2VcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHVzZVRyYW5zbGF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNvbnRleHQsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRJMThuLCBnZXREZWZhdWx0cywgUmVwb3J0TmFtZXNwYWNlcywgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuaW1wb3J0IHsgd2Fybk9uY2UsIGxvYWROYW1lc3BhY2VzLCBsb2FkTGFuZ3VhZ2VzLCBoYXNMb2FkZWROYW1lc3BhY2UsIGlzU3RyaW5nLCBpc09iamVjdCB9IGZyb20gJy4vdXRpbHMuanMnO1xuY29uc3QgdXNlUHJldmlvdXMgPSAodmFsdWUsIGlnbm9yZSkgPT4ge1xuICBjb25zdCByZWYgPSB1c2VSZWYoKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZWYuY3VycmVudCA9IGlnbm9yZSA/IHJlZi5jdXJyZW50IDogdmFsdWU7XG4gIH0sIFt2YWx1ZSwgaWdub3JlXSk7XG4gIHJldHVybiByZWYuY3VycmVudDtcbn07XG5jb25zdCBhbHdheXNOZXdUID0gKGkxOG4sIGxhbmd1YWdlLCBuYW1lc3BhY2UsIGtleVByZWZpeCkgPT4gaTE4bi5nZXRGaXhlZFQobGFuZ3VhZ2UsIG5hbWVzcGFjZSwga2V5UHJlZml4KTtcbmNvbnN0IHVzZU1lbW9pemVkVCA9IChpMThuLCBsYW5ndWFnZSwgbmFtZXNwYWNlLCBrZXlQcmVmaXgpID0+IHVzZUNhbGxiYWNrKGFsd2F5c05ld1QoaTE4biwgbGFuZ3VhZ2UsIG5hbWVzcGFjZSwga2V5UHJlZml4KSwgW2kxOG4sIGxhbmd1YWdlLCBuYW1lc3BhY2UsIGtleVByZWZpeF0pO1xuZXhwb3J0IGNvbnN0IHVzZVRyYW5zbGF0aW9uID0gKG5zLCBwcm9wcyA9IHt9KSA9PiB7XG4gIGNvbnN0IHtcbiAgICBpMThuOiBpMThuRnJvbVByb3BzXG4gIH0gPSBwcm9wcztcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tQ29udGV4dCxcbiAgICBkZWZhdWx0TlM6IGRlZmF1bHROU0Zyb21Db250ZXh0XG4gIH0gPSB1c2VDb250ZXh0KEkxOG5Db250ZXh0KSB8fCB7fTtcbiAgY29uc3QgaTE4biA9IGkxOG5Gcm9tUHJvcHMgfHwgaTE4bkZyb21Db250ZXh0IHx8IGdldEkxOG4oKTtcbiAgaWYgKGkxOG4gJiYgIWkxOG4ucmVwb3J0TmFtZXNwYWNlcykgaTE4bi5yZXBvcnROYW1lc3BhY2VzID0gbmV3IFJlcG9ydE5hbWVzcGFjZXMoKTtcbiAgaWYgKCFpMThuKSB7XG4gICAgd2Fybk9uY2UoaTE4biwgJ05PX0kxOE5FWFRfSU5TVEFOQ0UnLCAndXNlVHJhbnNsYXRpb246IFlvdSB3aWxsIG5lZWQgdG8gcGFzcyBpbiBhbiBpMThuZXh0IGluc3RhbmNlIGJ5IHVzaW5nIGluaXRSZWFjdEkxOG5leHQnKTtcbiAgICBjb25zdCBub3RSZWFkeVQgPSAoaywgb3B0c09yRGVmYXVsdFZhbHVlKSA9PiB7XG4gICAgICBpZiAoaXNTdHJpbmcob3B0c09yRGVmYXVsdFZhbHVlKSkgcmV0dXJuIG9wdHNPckRlZmF1bHRWYWx1ZTtcbiAgICAgIGlmIChpc09iamVjdChvcHRzT3JEZWZhdWx0VmFsdWUpICYmIGlzU3RyaW5nKG9wdHNPckRlZmF1bHRWYWx1ZS5kZWZhdWx0VmFsdWUpKSByZXR1cm4gb3B0c09yRGVmYXVsdFZhbHVlLmRlZmF1bHRWYWx1ZTtcbiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KGspID8ga1trLmxlbmd0aCAtIDFdIDogaztcbiAgICB9O1xuICAgIGNvbnN0IHJldE5vdFJlYWR5ID0gW25vdFJlYWR5VCwge30sIGZhbHNlXTtcbiAgICByZXROb3RSZWFkeS50ID0gbm90UmVhZHlUO1xuICAgIHJldE5vdFJlYWR5LmkxOG4gPSB7fTtcbiAgICByZXROb3RSZWFkeS5yZWFkeSA9IGZhbHNlO1xuICAgIHJldHVybiByZXROb3RSZWFkeTtcbiAgfVxuICBpZiAoaTE4bi5vcHRpb25zLnJlYWN0Py53YWl0KSB3YXJuT25jZShpMThuLCAnREVQUkVDQVRFRF9PUFRJT04nLCAndXNlVHJhbnNsYXRpb246IEl0IHNlZW1zIHlvdSBhcmUgc3RpbGwgdXNpbmcgdGhlIG9sZCB3YWl0IG9wdGlvbiwgeW91IG1heSBtaWdyYXRlIHRvIHRoZSBuZXcgdXNlU3VzcGVuc2UgYmVoYXZpb3VyLicpO1xuICBjb25zdCBpMThuT3B0aW9ucyA9IHtcbiAgICAuLi5nZXREZWZhdWx0cygpLFxuICAgIC4uLmkxOG4ub3B0aW9ucy5yZWFjdCxcbiAgICAuLi5wcm9wc1xuICB9O1xuICBjb25zdCB7XG4gICAgdXNlU3VzcGVuc2UsXG4gICAga2V5UHJlZml4XG4gIH0gPSBpMThuT3B0aW9ucztcbiAgbGV0IG5hbWVzcGFjZXMgPSBucyB8fCBkZWZhdWx0TlNGcm9tQ29udGV4dCB8fCBpMThuLm9wdGlvbnM/LmRlZmF1bHROUztcbiAgbmFtZXNwYWNlcyA9IGlzU3RyaW5nKG5hbWVzcGFjZXMpID8gW25hbWVzcGFjZXNdIDogbmFtZXNwYWNlcyB8fCBbJ3RyYW5zbGF0aW9uJ107XG4gIGkxOG4ucmVwb3J0TmFtZXNwYWNlcy5hZGRVc2VkTmFtZXNwYWNlcz8uKG5hbWVzcGFjZXMpO1xuICBjb25zdCByZWFkeSA9IChpMThuLmlzSW5pdGlhbGl6ZWQgfHwgaTE4bi5pbml0aWFsaXplZFN0b3JlT25jZSkgJiYgbmFtZXNwYWNlcy5ldmVyeShuID0+IGhhc0xvYWRlZE5hbWVzcGFjZShuLCBpMThuLCBpMThuT3B0aW9ucykpO1xuICBjb25zdCBtZW1vR2V0VCA9IHVzZU1lbW9pemVkVChpMThuLCBwcm9wcy5sbmcgfHwgbnVsbCwgaTE4bk9wdGlvbnMubnNNb2RlID09PSAnZmFsbGJhY2snID8gbmFtZXNwYWNlcyA6IG5hbWVzcGFjZXNbMF0sIGtleVByZWZpeCk7XG4gIGNvbnN0IGdldFQgPSAoKSA9PiBtZW1vR2V0VDtcbiAgY29uc3QgZ2V0TmV3VCA9ICgpID0+IGFsd2F5c05ld1QoaTE4biwgcHJvcHMubG5nIHx8IG51bGwsIGkxOG5PcHRpb25zLm5zTW9kZSA9PT0gJ2ZhbGxiYWNrJyA/IG5hbWVzcGFjZXMgOiBuYW1lc3BhY2VzWzBdLCBrZXlQcmVmaXgpO1xuICBjb25zdCBbdCwgc2V0VF0gPSB1c2VTdGF0ZShnZXRUKTtcbiAgbGV0IGpvaW5lZE5TID0gbmFtZXNwYWNlcy5qb2luKCk7XG4gIGlmIChwcm9wcy5sbmcpIGpvaW5lZE5TID0gYCR7cHJvcHMubG5nfSR7am9pbmVkTlN9YDtcbiAgY29uc3QgcHJldmlvdXNKb2luZWROUyA9IHVzZVByZXZpb3VzKGpvaW5lZE5TKTtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlUmVmKHRydWUpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIGJpbmRJMThuLFxuICAgICAgYmluZEkxOG5TdG9yZVxuICAgIH0gPSBpMThuT3B0aW9ucztcbiAgICBpc01vdW50ZWQuY3VycmVudCA9IHRydWU7XG4gICAgaWYgKCFyZWFkeSAmJiAhdXNlU3VzcGVuc2UpIHtcbiAgICAgIGlmIChwcm9wcy5sbmcpIHtcbiAgICAgICAgbG9hZExhbmd1YWdlcyhpMThuLCBwcm9wcy5sbmcsIG5hbWVzcGFjZXMsICgpID0+IHtcbiAgICAgICAgICBpZiAoaXNNb3VudGVkLmN1cnJlbnQpIHNldFQoZ2V0TmV3VCk7XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbG9hZE5hbWVzcGFjZXMoaTE4biwgbmFtZXNwYWNlcywgKCkgPT4ge1xuICAgICAgICAgIGlmIChpc01vdW50ZWQuY3VycmVudCkgc2V0VChnZXROZXdUKTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChyZWFkeSAmJiBwcmV2aW91c0pvaW5lZE5TICYmIHByZXZpb3VzSm9pbmVkTlMgIT09IGpvaW5lZE5TICYmIGlzTW91bnRlZC5jdXJyZW50KSB7XG4gICAgICBzZXRUKGdldE5ld1QpO1xuICAgIH1cbiAgICBjb25zdCBib3VuZFJlc2V0ID0gKCkgPT4ge1xuICAgICAgaWYgKGlzTW91bnRlZC5jdXJyZW50KSBzZXRUKGdldE5ld1QpO1xuICAgIH07XG4gICAgaWYgKGJpbmRJMThuKSBpMThuPy5vbihiaW5kSTE4biwgYm91bmRSZXNldCk7XG4gICAgaWYgKGJpbmRJMThuU3RvcmUpIGkxOG4/LnN0b3JlLm9uKGJpbmRJMThuU3RvcmUsIGJvdW5kUmVzZXQpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpc01vdW50ZWQuY3VycmVudCA9IGZhbHNlO1xuICAgICAgaWYgKGkxOG4pIGJpbmRJMThuPy5zcGxpdCgnICcpLmZvckVhY2goZSA9PiBpMThuLm9mZihlLCBib3VuZFJlc2V0KSk7XG4gICAgICBpZiAoYmluZEkxOG5TdG9yZSAmJiBpMThuKSBiaW5kSTE4blN0b3JlLnNwbGl0KCcgJykuZm9yRWFjaChlID0+IGkxOG4uc3RvcmUub2ZmKGUsIGJvdW5kUmVzZXQpKTtcbiAgICB9O1xuICB9LCBbaTE4biwgam9pbmVkTlNdKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNNb3VudGVkLmN1cnJlbnQgJiYgcmVhZHkpIHtcbiAgICAgIHNldFQoZ2V0VCk7XG4gICAgfVxuICB9LCBbaTE4biwga2V5UHJlZml4LCByZWFkeV0pO1xuICBjb25zdCByZXQgPSBbdCwgaTE4biwgcmVhZHldO1xuICByZXQudCA9IHQ7XG4gIHJldC5pMThuID0gaTE4bjtcbiAgcmV0LnJlYWR5ID0gcmVhZHk7XG4gIGlmIChyZWFkeSkgcmV0dXJuIHJldDtcbiAgaWYgKCFyZWFkeSAmJiAhdXNlU3VzcGVuc2UpIHJldHVybiByZXQ7XG4gIHRocm93IG5ldyBQcm9taXNlKHJlc29sdmUgPT4ge1xuICAgIGlmIChwcm9wcy5sbmcpIHtcbiAgICAgIGxvYWRMYW5ndWFnZXMoaTE4biwgcHJvcHMubG5nLCBuYW1lc3BhY2VzLCAoKSA9PiByZXNvbHZlKCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBsb2FkTmFtZXNwYWNlcyhpMThuLCBuYW1lc3BhY2VzLCAoKSA9PiByZXNvbHZlKCkpO1xuICAgIH1cbiAgfSk7XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   warn: () => (/* binding */ warn),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nconst warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nconst loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nconst loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nconst hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nconst getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nconst isString = obj => typeof obj === 'string';\nconst isObject = obj => typeof obj === 'object' && obj !== null;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withSSR.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withSSR.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSSR: () => (/* binding */ withSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\n\nconst withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    (0,_useSSR_js__WEBPACK_IMPORTED_MODULE_1__.useSSR)(initialI18nStore, initialLanguage);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.composeInitialProps)(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3dpdGhTU1IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDRDtBQUNjO0FBQ1A7QUFDckM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxJQUFJLGtEQUFNO0FBQ1YsV0FBVyxvREFBYTtBQUN4QjtBQUNBLEtBQUs7QUFDTDtBQUNBLG1DQUFtQyxnRUFBbUI7QUFDdEQsaURBQWlELHlEQUFjLG1CQUFtQjtBQUNsRjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRGVza3RvcFxcUmVzdCBEZWxpdmVyeVxccmVzdGF1cmFudF9tYW5nZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcd2l0aFNTUi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVFbGVtZW50IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU1NSIH0gZnJvbSAnLi91c2VTU1IuanMnO1xuaW1wb3J0IHsgY29tcG9zZUluaXRpYWxQcm9wcyB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5pbXBvcnQgeyBnZXREaXNwbGF5TmFtZSB9IGZyb20gJy4vdXRpbHMuanMnO1xuZXhwb3J0IGNvbnN0IHdpdGhTU1IgPSAoKSA9PiBmdW5jdGlvbiBFeHRlbmQoV3JhcHBlZENvbXBvbmVudCkge1xuICBmdW5jdGlvbiBJMThuZXh0V2l0aFNTUih7XG4gICAgaW5pdGlhbEkxOG5TdG9yZSxcbiAgICBpbml0aWFsTGFuZ3VhZ2UsXG4gICAgLi4ucmVzdFxuICB9KSB7XG4gICAgdXNlU1NSKGluaXRpYWxJMThuU3RvcmUsIGluaXRpYWxMYW5ndWFnZSk7XG4gICAgcmV0dXJuIGNyZWF0ZUVsZW1lbnQoV3JhcHBlZENvbXBvbmVudCwge1xuICAgICAgLi4ucmVzdFxuICAgIH0pO1xuICB9XG4gIEkxOG5leHRXaXRoU1NSLmdldEluaXRpYWxQcm9wcyA9IGNvbXBvc2VJbml0aWFsUHJvcHMoV3JhcHBlZENvbXBvbmVudCk7XG4gIEkxOG5leHRXaXRoU1NSLmRpc3BsYXlOYW1lID0gYHdpdGhJMThuZXh0U1NSKCR7Z2V0RGlzcGxheU5hbWUoV3JhcHBlZENvbXBvbmVudCl9KWA7XG4gIEkxOG5leHRXaXRoU1NSLldyYXBwZWRDb21wb25lbnQgPSBXcmFwcGVkQ29tcG9uZW50O1xuICByZXR1cm4gSTE4bmV4dFdpdGhTU1I7XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withTranslation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTranslation: () => (/* binding */ withTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(forwardRef) : I18nextWithTranslation;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\n");

/***/ })

};
;
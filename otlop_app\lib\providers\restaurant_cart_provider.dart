import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/services/cart_storage_service.dart';
import 'package:otlop_app/services/restaurant_service.dart';

class RestaurantCartProvider with ChangeNotifier {
  final CartStorageService _cartStorageService = CartStorageService();
  final RestaurantService _restaurantService = RestaurantService();

  Map<int, List<CartItem>> _restaurantCarts = {};
  Map<int, bool> _restaurantOpenStatus = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  Map<int, List<CartItem>> get restaurantCarts => _restaurantCarts;
  Map<int, bool> get restaurantOpenStatus => _restaurantOpenStatus;
  bool get isLoading => _isLoading;
  String? get error => _error;

  RestaurantCartProvider() {
    _initCartStorage();
  }

  // Initialize cart storage
  Future<void> _initCartStorage() async {
    debugPrint('DEBUG: Initializing restaurant cart storage');
    await _cartStorageService.init();
    debugPrint('DEBUG: Restaurant cart storage initialized');
    await _loadAllRestaurantCarts();
  }

  // Load all restaurant carts
  Future<void> _loadAllRestaurantCarts() async {
    try {
      debugPrint('DEBUG: Loading all restaurant carts');
      final allCartItems = await _cartStorageService.getCartItems();
      
      // Group items by restaurant
      _restaurantCarts.clear();
      for (final item in allCartItems) {
        _restaurantCarts.putIfAbsent(item.restaurantId, () => []);
        _restaurantCarts[item.restaurantId]!.add(item);
      }
      
      debugPrint('DEBUG: Loaded carts for ${_restaurantCarts.length} restaurants');
      notifyListeners();
    } catch (e) {
      debugPrint('ERROR: Failed to load restaurant carts: ${e.toString()}');
      _setError('Failed to load restaurant carts: ${e.toString()}');
    }
  }

  // Get cart items for specific restaurant
  List<CartItem> getCartItemsForRestaurant(int restaurantId) {
    return _restaurantCarts[restaurantId] ?? [];
  }

  // Get cart item count for specific restaurant
  int getCartItemCountForRestaurant(int restaurantId) {
    final items = _restaurantCarts[restaurantId] ?? [];
    return items.fold<int>(0, (total, item) => total + item.quantity);
  }

  // Get cart total for specific restaurant
  double getCartTotalForRestaurant(int restaurantId) {
    final items = _restaurantCarts[restaurantId] ?? [];
    return items.fold<double>(0.0, (total, item) => total + (item.price * item.quantity));
  }

  // Check if restaurant has items in cart
  bool hasItemsForRestaurant(int restaurantId) {
    return (_restaurantCarts[restaurantId]?.isNotEmpty) ?? false;
  }

  // Check if restaurant is open
  Future<bool> isRestaurantOpen(int restaurantId) async {
    // Check cached status first
    if (_restaurantOpenStatus.containsKey(restaurantId)) {
      return _restaurantOpenStatus[restaurantId]!;
    }

    try {
      // Fetch restaurant details to check status
      final response = await _restaurantService.getRestaurantById(restaurantId.toString());

      debugPrint('DEBUG: Restaurant status response: $response');

      // Handle the response structure - the data is nested under 'data' key
      final restaurantData = response['data'] ?? response;

      debugPrint('DEBUG: Restaurant status data: $restaurantData');

      // Check if restaurant is open based on IsClosed field
      // IsClosed = true means restaurant is closed
      // IsClosed = false means restaurant is open
      final isClosed = restaurantData['IsClosed'] == true;
      final isOpen = !isClosed;

      debugPrint('DEBUG: Restaurant $restaurantId - IsClosed: $isClosed, IsOpen: $isOpen');

      _restaurantOpenStatus[restaurantId] = isOpen;
      return isOpen;
    } catch (e) {
      debugPrint('ERROR: Failed to check restaurant status: $e');
      // Default to closed if we can't check
      _restaurantOpenStatus[restaurantId] = false;
      return false;
    }
  }

  // Add item to restaurant cart
  Future<void> addItemToRestaurantCart(Product product, int quantity, List<SelectedIngredient>? selectedIngredients, {bool? isRestaurantOpen}) async {
    try {
      _setLoading(true);
      _setError(null);

      final restaurantId = product.restaurantId ?? 0;
      if (restaurantId == 0) {
        throw Exception('Product must have a valid restaurant ID');
      }

      // Check if restaurant is open - use provided status or fetch it
      bool isOpen;
      if (isRestaurantOpen != null) {
        isOpen = isRestaurantOpen;
        // Cache the status for future use
        _restaurantOpenStatus[restaurantId] = isOpen;
      } else {
        isOpen = await this.isRestaurantOpen(restaurantId);
      }

      if (!isOpen) {
        throw Exception('Restaurant is currently closed. You cannot add items to cart.');
      }

      // Add to storage
      await _cartStorageService.addToCart(product, quantity, selectedIngredients);

      // Reload restaurant carts
      await _loadAllRestaurantCarts();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Remove item from restaurant cart
  Future<void> removeItemFromRestaurantCart(int cartItemId) async {
    try {
      _setLoading(true);
      _setError(null);

      await _cartStorageService.removeCartItem(cartItemId);
      await _loadAllRestaurantCarts();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Update cart item quantity
  Future<void> updateCartItemQuantity(int cartItemId, int quantity) async {
    try {
      _setLoading(true);
      _setError(null);

      await _cartStorageService.updateCartItemQuantity(cartItemId, quantity);
      await _loadAllRestaurantCarts();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Clear cart for specific restaurant
  Future<void> clearRestaurantCart(int restaurantId) async {
    try {
      _setLoading(true);
      _setError(null);

      await _cartStorageService.clearCartForRestaurant(restaurantId);
      await _loadAllRestaurantCarts();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Clear all carts
  Future<void> clearAllCarts() async {
    try {
      _setLoading(true);
      _setError(null);

      await _cartStorageService.clearCart();
      await _loadAllRestaurantCarts();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Validate restaurant status before checkout
  Future<bool> validateRestaurantForCheckout(int restaurantId) async {
    try {
      final isOpen = await isRestaurantOpen(restaurantId);
      if (!isOpen) {
        _setError('Restaurant is currently closed. Cannot proceed with checkout.');
        return false;
      }
      return true;
    } catch (e) {
      _setError('Failed to validate restaurant status: $e');
      return false;
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? errorMessage) {
    _error = errorMessage;
    notifyListeners();
  }

  // Refresh restaurant status
  Future<void> refreshRestaurantStatus(int restaurantId) async {
    _restaurantOpenStatus.remove(restaurantId);
    await isRestaurantOpen(restaurantId);
    notifyListeners();
  }

  // Get all restaurant IDs that have items in cart
  List<int> getRestaurantIdsWithItems() {
    return _restaurantCarts.keys.where((restaurantId) =>
      _restaurantCarts[restaurantId]?.isNotEmpty == true
    ).toList();
  }

  // Check if a specific product is in the cart for a restaurant
  bool isProductInRestaurantCart(int restaurantId, int productId) {
    final items = _restaurantCarts[restaurantId] ?? [];
    return items.any((item) => item.productId == productId);
  }

  // Check if there's a cart for a different restaurant
  bool hasCartForDifferentRestaurant(int restaurantId) {
    final restaurantIdsWithItems = getRestaurantIdsWithItems();
    return restaurantIdsWithItems.isNotEmpty && !restaurantIdsWithItems.contains(restaurantId);
  }

  // Get the current restaurant ID (first one with items)
  int? getCurrentRestaurantId() {
    final restaurantIdsWithItems = getRestaurantIdsWithItems();
    return restaurantIdsWithItems.isNotEmpty ? restaurantIdsWithItems.first : null;
  }
}

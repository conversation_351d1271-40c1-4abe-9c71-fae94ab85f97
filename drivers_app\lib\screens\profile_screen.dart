import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../services/api_client.dart';
import 'login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Map<String, dynamic>? _driverData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDriverProfile();
  }

  Future<void> _loadDriverProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      print('Auth status: ${authProvider.isAuthenticated}');
      print('Driver data: ${authProvider.driver}');

      if (authProvider.isAuthenticated && authProvider.driver != null) {
        final driverId = authProvider.driver!.id;
        print('Loading profile for driver ID: $driverId');

        final apiClient = ApiClient();

        // Add timeout and retry logic for better reliability
        final response = await apiClient.get('/drivers/$driverId').timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            throw Exception('انتهت مهلة الاتصال بالخادم');
          },
        );

        print('Profile response: $response');

        if (response != null && response['success'] == true) {
          setState(() {
            _driverData = response['data'];
            _isLoading = false;
          });
        } else {
          throw Exception('فشل في تحميل بيانات السائق');
        }
      } else {
        throw Exception('لم يتم تسجيل الدخول أو بيانات السائق غير متوفرة');
      }
    } catch (e) {
      print('Error loading driver profile: $e');
      setState(() {
        _isLoading = false;
      });

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _loadDriverProfile,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
        ),
      );
    }

    if (_driverData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "لم يتم تسجيل الدخول",
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => LoginScreen()),
                );
              },
              child: const Text("تسجيل الدخول"),
            ),
          ],
        ),
      );
    }



    return Container(
      color: Colors.grey[50],
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section with Gradient Background
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 40, 20, 30),
              child: Column(
                children: [
                  // Profile Avatar
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(60),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.person,
                      size: 60,
                      color: Colors.purple[600],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Driver Name
                  Text(
                    "${_driverData!['FirstName'] ?? ''} ${_driverData!['LastName'] ?? ''}".trim().isEmpty
                        ? "بدون اسم"
                        : "${_driverData!['FirstName'] ?? ''} ${_driverData!['LastName'] ?? ''}".trim(),
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Username
                  Text(
                    _driverData!['Username'] ?? "بدون اسم مستخدم",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Status Badge
                  _buildModernDriverStatus(_driverData!),

                  const SizedBox(height: 20),

                  // Wallet Card
                  _buildWalletCard(),
                ],
              ),
            ),

            // Content Section
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Personal Information Card
                  _buildModernInfoCard(
                    title: "معلومات شخصية",
                    icon: Icons.person_outline,
                    items: [
                      InfoItem(
                        icon: Icons.phone,
                        label: "رقم الهاتف",
                        value: _driverData!['PhoneNumber'] ?? "غير متوفر",
                      ),
                      InfoItem(
                        icon: Icons.location_on,
                        label: "العنوان",
                        value: _driverData!['Address'] ?? "غير متوفر",
                      ),
                      InfoItem(
                        icon: Icons.calendar_today,
                        label: "تاريخ الانضمام",
                        value: _formatDate(_driverData!['JoinDate']),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Vehicle Information Card
                  _buildModernInfoCard(
                    title: "معلومات المركبة",
                    icon: Icons.drive_eta,
                    items: [
                      InfoItem(
                        icon: Icons.directions_car,
                        label: "نوع المركبة",
                        value: _driverData!['CarData'] ?? "غير متوفر",
                      ),
                      InfoItem(
                        icon: Icons.confirmation_number,
                        label: "رقم اللوحة",
                        value: _driverData!['PlateNumber'] ?? "غير متوفر",
                      ),
                      InfoItem(
                        icon: Icons.business,
                        label: "سيارة الشركة",
                        value: _driverData!['CompanyCar'] == true ? "نعم" : "لا",
                      ),
                    ],
                  ),

                  const SizedBox(height: 30),

                  // Logout Button
                  _buildLogoutButton(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Modern status badge
  Widget _buildModernDriverStatus(Map<String, dynamic> driverData) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (driverData['IsWorking'] == true) {
      statusColor = Colors.green;
      statusText = "متاح للعمل";
      statusIcon = Icons.check_circle;
    } else {
      statusColor = Colors.orange;
      statusText = "غير متاح";
      statusIcon = Icons.pause_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 20,
            color: statusColor,
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  // Wallet card widget
  Widget _buildWalletCard() {
    final wallet = _driverData!['Wallet'] ?? 0.0;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_wallet,
            size: 40,
            color: Colors.purple[600],
          ),
          const SizedBox(height: 12),
          Text(
            'رصيد المحفظة',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${wallet.toStringAsFixed(2)} د.ل',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.purple[600],
            ),
          ),
        ],
      ),
    );
  }

  // Modern info card
  Widget _buildModernInfoCard({
    required String title,
    required IconData icon,
    required List<InfoItem> items,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.purple[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.purple[600],
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Card Items
            ...items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;

              return Column(
                children: [
                  _buildInfoRow(item),
                  if (index < items.length - 1)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Divider(
                        color: Colors.grey[200],
                        height: 1,
                      ),
                    ),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  // Info row widget
  Widget _buildInfoRow(InfoItem item) {
    return Row(
      children: [
        Icon(
          item.icon,
          color: Colors.grey[600],
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                item.value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Logout button
  Widget _buildLogoutButton() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    return Container(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: () async {
          await authProvider.logout();
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => LoginScreen()),
          );
        },
        icon: const Icon(Icons.logout, size: 20),
        label: const Text(
          "تسجيل الخروج",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  // Format date helper
  String _formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return "غير متوفر";

    try {
      final date = DateTime.parse(dateStr);
      return "${date.day}/${date.month}/${date.year}";
    } catch (e) {
      return dateStr;
    }
  }


}

class InfoItem {
  final IconData icon;
  final String label;
  final String value;
  
  const InfoItem({
    required this.icon,
    required this.label,
    required this.value,
  });
} 
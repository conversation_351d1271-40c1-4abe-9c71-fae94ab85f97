const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Order', {
    OrderID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    DriverID: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    CartID: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    CustomerID: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    StartPointLongitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    StartPointLatitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    EndPointLongitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    EndPointLatitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    Duration: {
      type: DataTypes.TIME,
      allowNull: true
    },
    OrderDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    Status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    Claimed: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    TotalPrice: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    Distance: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    DriverProfit: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    DeliveryFee: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    OrderPrice: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'Order',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Order__C3905BAF88A77E1C",
        unique: true,
        fields: [
          { name: "OrderID" },
        ]
      },
    ]
  });
};

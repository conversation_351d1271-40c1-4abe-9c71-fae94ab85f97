"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/./node_modules/@iconify/react/dist/iconify.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/react/dist/iconify.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   InlineIcon: () => (/* binding */ InlineIcon),\n/* harmony export */   _api: () => (/* binding */ _api),\n/* harmony export */   addAPIProvider: () => (/* binding */ addAPIProvider),\n/* harmony export */   addCollection: () => (/* binding */ addCollection),\n/* harmony export */   addIcon: () => (/* binding */ addIcon),\n/* harmony export */   buildIcon: () => (/* binding */ iconToSVG),\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize),\n/* harmony export */   disableCache: () => (/* binding */ disableCache),\n/* harmony export */   enableCache: () => (/* binding */ enableCache),\n/* harmony export */   getIcon: () => (/* binding */ getIcon),\n/* harmony export */   iconExists: () => (/* binding */ iconExists),\n/* harmony export */   listIcons: () => (/* binding */ listIcons),\n/* harmony export */   loadIcon: () => (/* binding */ loadIcon),\n/* harmony export */   loadIcons: () => (/* binding */ loadIcons),\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!((icon.provider === \"\" || icon.provider.match(matchIconName)) && (allowSimpleName && icon.prefix === \"\" || icon.prefix.match(matchIconName)) && icon.name.match(matchIconName));\n};\n\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\n\nfunction parseIconSet(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (!name.match(matchIconName) || typeof icon.body !== \"string\" || !checkOptionalProps(\n      icon,\n      defaultExtendedIconProps\n    )) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (!name.match(matchIconName) || typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || !checkOptionalProps(\n      icon,\n      defaultExtendedIconProps\n    )) {\n      return null;\n    }\n  }\n  return data;\n}\n\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ new Set()\n  };\n}\nfunction getStorage(provider, prefix) {\n  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage.icons[name] = icon;\n    } else {\n      storage.missing.add(name);\n    }\n  });\n}\nfunction addIconToStorage(storage, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage.icons[name] = { ...icon };\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  const providers = typeof provider === \"string\" ? [provider] : Object.keys(dataStorage);\n  providers.forEach((provider2) => {\n    const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [prefix] : Object.keys(dataStorage[provider2] || {});\n    prefixes.forEach((prefix2) => {\n      const storage = getStorage(provider2, prefix2);\n      allIcons = allIcons.concat(\n        Object.keys(storage.icons).map(\n          (name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name\n        )\n      );\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  if (icon) {\n    const storage = getStorage(icon.provider, icon.prefix);\n    const iconName = icon.name;\n    return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n  }\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  return addIconToStorage(storage, icon.name, data);\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = data.provider || \"\";\n  }\n  if (simpleNames && !provider && !data.prefix) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (icon && addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  const prefix = data.prefix;\n  if (!validateIconName({\n    provider,\n    prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconExists(name) {\n  return !!getIconData(name);\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? {\n    ...defaultIconProps,\n    ...result\n  } : null;\n}\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = '<g transform=\"' + transformations.join(\" \") + '\">' + body + \"</g>\";\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  attributes.viewBox = box.left.toString() + \" \" + box.top.toString() + \" \" + boxWidth.toString() + \" \" + boxHeight.toString();\n  return {\n    attributes,\n    body\n  };\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    // API hosts\n    resources,\n    // Root path\n    path: source.path || \"/\",\n    // URL length limit\n    maxURL: source.maxURL || 500,\n    // Timeout before next host is used.\n    rotate: source.rotate || 750,\n    // Timeout before failing query.\n    timeout: source.timeout || 5e3,\n    // Randomise default API end point.\n    random: source.random === true,\n    // Start index\n    index: source.index || 0,\n    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = prefix + \".json?icons=\";\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  const maxLength = calculateMaxLength(provider, prefix);\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    const config = getAPIConfig(provider);\n    if (config) {\n      return config.path;\n    }\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      const urlParams = new URLSearchParams({\n        icons: iconsList\n      });\n      path += prefix + \".json?\" + urlParams.toString();\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        if (data === 404) {\n          callback(\"abort\", data);\n        } else {\n          callback(\"next\", defaultError);\n        }\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n    let list;\n    if (name in localStorage.icons) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing.has(name)) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nfunction removeCallback(storages, id) {\n  storages.forEach((storage) => {\n    const items = storage.loaderCallbacks;\n    if (items) {\n      storage.loaderCallbacks = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(storage) {\n  if (!storage.pendingCallbacksFlag) {\n    storage.pendingCallbacksFlag = true;\n    setTimeout(() => {\n      storage.pendingCallbacksFlag = false;\n      const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n      if (!items.length) {\n        return;\n      }\n      let hasPending = false;\n      const provider = storage.provider;\n      const prefix = storage.prefix;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name]) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing.has(name)) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([storage], item.id);\n          }\n          item.callback(\n            icons.loaded.slice(0),\n            icons.missing.slice(0),\n            icons.pending.slice(0),\n            item.abort\n          );\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((storage) => {\n    (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n    if (icon) {\n      result.push(icon);\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction initRedundancy(cfg) {\n  const config = {\n    ...defaultConfig,\n    ...cfg\n  };\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(\n      config,\n      payload,\n      queryCallback,\n      (data, error) => {\n        cleanup();\n        if (doneCallback) {\n          doneCallback(data, error);\n        }\n      }\n    );\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    return queries.find((value) => {\n      return callback(value);\n    }) || null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (!redundancyCache[provider]) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nconst browserCacheVersion = \"iconify2\";\nconst browserCachePrefix = \"iconify\";\nconst browserCacheCountKey = browserCachePrefix + \"-count\";\nconst browserCacheVersionKey = browserCachePrefix + \"-version\";\nconst browserStorageHour = 36e5;\nconst browserStorageCacheExpiration = 168;\n\nfunction getStoredItem(func, key) {\n  try {\n    return func.getItem(key);\n  } catch (err) {\n  }\n}\nfunction setStoredItem(func, key, value) {\n  try {\n    func.setItem(key, value);\n    return true;\n  } catch (err) {\n  }\n}\nfunction removeStoredItem(func, key) {\n  try {\n    func.removeItem(key);\n  } catch (err) {\n  }\n}\n\nfunction setBrowserStorageItemsCount(storage, value) {\n  return setStoredItem(storage, browserCacheCountKey, value.toString());\n}\nfunction getBrowserStorageItemsCount(storage) {\n  return parseInt(getStoredItem(storage, browserCacheCountKey)) || 0;\n}\n\nconst browserStorageConfig = {\n  local: true,\n  session: true\n};\nconst browserStorageEmptyItems = {\n  local: /* @__PURE__ */ new Set(),\n  session: /* @__PURE__ */ new Set()\n};\nlet browserStorageStatus = false;\nfunction setBrowserStorageStatus(status) {\n  browserStorageStatus = status;\n}\n\nlet _window = typeof window === \"undefined\" ? {} : window;\nfunction getBrowserStorage(key) {\n  const attr = key + \"Storage\";\n  try {\n    if (_window && _window[attr] && typeof _window[attr].length === \"number\") {\n      return _window[attr];\n    }\n  } catch (err) {\n  }\n  browserStorageConfig[key] = false;\n}\n\nfunction iterateBrowserStorage(key, callback) {\n  const func = getBrowserStorage(key);\n  if (!func) {\n    return;\n  }\n  const version = getStoredItem(func, browserCacheVersionKey);\n  if (version !== browserCacheVersion) {\n    if (version) {\n      const total2 = getBrowserStorageItemsCount(func);\n      for (let i = 0; i < total2; i++) {\n        removeStoredItem(func, browserCachePrefix + i.toString());\n      }\n    }\n    setStoredItem(func, browserCacheVersionKey, browserCacheVersion);\n    setBrowserStorageItemsCount(func, 0);\n    return;\n  }\n  const minTime = Math.floor(Date.now() / browserStorageHour) - browserStorageCacheExpiration;\n  const parseItem = (index) => {\n    const name = browserCachePrefix + index.toString();\n    const item = getStoredItem(func, name);\n    if (typeof item !== \"string\") {\n      return;\n    }\n    try {\n      const data = JSON.parse(item);\n      if (typeof data === \"object\" && typeof data.cached === \"number\" && data.cached > minTime && typeof data.provider === \"string\" && typeof data.data === \"object\" && typeof data.data.prefix === \"string\" && // Valid item: run callback\n      callback(data, index)) {\n        return true;\n      }\n    } catch (err) {\n    }\n    removeStoredItem(func, name);\n  };\n  let total = getBrowserStorageItemsCount(func);\n  for (let i = total - 1; i >= 0; i--) {\n    if (!parseItem(i)) {\n      if (i === total - 1) {\n        total--;\n        setBrowserStorageItemsCount(func, total);\n      } else {\n        browserStorageEmptyItems[key].add(i);\n      }\n    }\n  }\n}\n\nfunction initBrowserStorage() {\n  if (browserStorageStatus) {\n    return;\n  }\n  setBrowserStorageStatus(true);\n  for (const key in browserStorageConfig) {\n    iterateBrowserStorage(key, (item) => {\n      const iconSet = item.data;\n      const provider = item.provider;\n      const prefix = iconSet.prefix;\n      const storage = getStorage(\n        provider,\n        prefix\n      );\n      if (!addIconSet(storage, iconSet).length) {\n        return false;\n      }\n      const lastModified = iconSet.lastModified || -1;\n      storage.lastModifiedCached = storage.lastModifiedCached ? Math.min(storage.lastModifiedCached, lastModified) : lastModified;\n      return true;\n    });\n  }\n}\n\nfunction updateLastModified(storage, lastModified) {\n  const lastValue = storage.lastModifiedCached;\n  if (\n    // Matches or newer\n    lastValue && lastValue >= lastModified\n  ) {\n    return lastValue === lastModified;\n  }\n  storage.lastModifiedCached = lastModified;\n  if (lastValue) {\n    for (const key in browserStorageConfig) {\n      iterateBrowserStorage(key, (item) => {\n        const iconSet = item.data;\n        return item.provider !== storage.provider || iconSet.prefix !== storage.prefix || iconSet.lastModified === lastModified;\n      });\n    }\n  }\n  return true;\n}\nfunction storeInBrowserStorage(storage, data) {\n  if (!browserStorageStatus) {\n    initBrowserStorage();\n  }\n  function store(key) {\n    let func;\n    if (!browserStorageConfig[key] || !(func = getBrowserStorage(key))) {\n      return;\n    }\n    const set = browserStorageEmptyItems[key];\n    let index;\n    if (set.size) {\n      set.delete(index = Array.from(set).shift());\n    } else {\n      index = getBrowserStorageItemsCount(func);\n      if (!setBrowserStorageItemsCount(func, index + 1)) {\n        return;\n      }\n    }\n    const item = {\n      cached: Math.floor(Date.now() / browserStorageHour),\n      provider: storage.provider,\n      data\n    };\n    return setStoredItem(\n      func,\n      browserCachePrefix + index.toString(),\n      JSON.stringify(item)\n    );\n  }\n  if (data.lastModified && !updateLastModified(storage, data.lastModified)) {\n    return;\n  }\n  if (!Object.keys(data.icons).length) {\n    return;\n  }\n  if (data.not_found) {\n    data = Object.assign({}, data);\n    delete data.not_found;\n  }\n  if (!store(\"local\")) {\n    store(\"session\");\n  }\n}\n\nfunction emptyCallback() {\n}\nfunction loadedNewIcons(storage) {\n  if (!storage.iconsLoaderFlag) {\n    storage.iconsLoaderFlag = true;\n    setTimeout(() => {\n      storage.iconsLoaderFlag = false;\n      updateCallbacks(storage);\n    });\n  }\n}\nfunction loadNewIcons(storage, icons) {\n  if (!storage.iconsToLoad) {\n    storage.iconsToLoad = icons;\n  } else {\n    storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n  }\n  if (!storage.iconsQueueFlag) {\n    storage.iconsQueueFlag = true;\n    setTimeout(() => {\n      storage.iconsQueueFlag = false;\n      const { provider, prefix } = storage;\n      const icons2 = storage.iconsToLoad;\n      delete storage.iconsToLoad;\n      let api;\n      if (!icons2 || !(api = getAPIModule(provider))) {\n        return;\n      }\n      const params = api.prepare(provider, prefix, icons2);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data) => {\n          if (typeof data !== \"object\") {\n            item.icons.forEach((name) => {\n              storage.missing.add(name);\n            });\n          } else {\n            try {\n              const parsed = addIconSet(\n                storage,\n                data\n              );\n              if (!parsed.length) {\n                return;\n              }\n              const pending = storage.pendingIcons;\n              if (pending) {\n                parsed.forEach((name) => {\n                  pending.delete(name);\n                });\n              }\n              storeInBrowserStorage(storage, data);\n            } catch (err) {\n              console.error(err);\n            }\n          }\n          loadedNewIcons(storage);\n        });\n      });\n    });\n  }\n}\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(\n            sortedIcons.loaded,\n            sortedIcons.missing,\n            sortedIcons.pending,\n            emptyCallback\n          );\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix } = icon;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push(getStorage(provider, prefix));\n    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n    if (!providerNewIcons[prefix]) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix, name } = icon;\n    const storage = getStorage(provider, prefix);\n    const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n    if (!pendingQueue.has(name)) {\n      pendingQueue.add(name);\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((storage) => {\n    const { provider, prefix } = storage;\n    if (newIcons[provider][prefix].length) {\n      loadNewIcons(storage, newIcons[provider][prefix]);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n    if (!iconObj) {\n      reject(icon);\n      return;\n    }\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const data = getIconData(iconObj);\n        if (data) {\n          fulfill({\n            ...defaultIconProps,\n            ...data\n          });\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nfunction toggleBrowserCache(storage, value) {\n  switch (storage) {\n    case \"local\":\n    case \"session\":\n      browserStorageConfig[storage] = value;\n      break;\n    case \"all\":\n      for (const key in browserStorageConfig) {\n        browserStorageConfig[key] = value;\n      }\n      break;\n  }\n}\n\nfunction mergeCustomisations(defaults, item) {\n  const result = {\n    ...defaults\n  };\n  for (const key in item) {\n    const value = item[key];\n    const valueType = typeof value;\n    if (key in defaultIconSizeCustomisations) {\n      if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n        result[key] = value;\n      }\n    } else if (valueType === typeof result[key]) {\n      result[key] = key === \"rotate\" ? value % 4 : value;\n    }\n  }\n  return result;\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nfunction encodeSVGforURL(svg) {\n  return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n  return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n  return 'url(\"' + svgToData(svg) + '\")';\n}\n\nlet policy;\nfunction createPolicy() {\n  try {\n    policy = window.trustedTypes.createPolicy(\"iconify\", {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      createHTML: (s) => s\n    });\n  } catch (err) {\n    policy = null;\n  }\n}\nfunction cleanUpInnerHTML(html) {\n  if (policy === void 0) {\n    createPolicy();\n  }\n  return policy ? policy.createHTML(html) : html;\n}\n\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false,\n};\n\n/**\n * Default SVG attributes\n */\nconst svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlnsXlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img',\n};\n/**\n * Style modes\n */\nconst commonProps = {\n    display: 'inline-block',\n};\nconst monotoneProps = {\n    backgroundColor: 'currentColor',\n};\nconst coloredProps = {\n    backgroundColor: 'transparent',\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: 'var(--svg)',\n    Repeat: 'no-repeat',\n    Size: '100% 100%',\n};\nconst propsToAddTo = {\n    WebkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps,\n};\nfor (const prefix in propsToAddTo) {\n    const list = propsToAddTo[prefix];\n    for (const prop in propsToAdd) {\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Default values for customisations for inline icon\n */\nconst inlineDefaults = {\n    ...defaultExtendedIconCustomisations,\n    inline: true,\n};\n/**\n * Fix size: add 'px' to numbers\n */\nfunction fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? 'px' : '');\n}\n/**\n * Render icon\n */\nconst render = (\n// Icon must be validated before calling this function\nicon, \n// Partial properties\nprops, \n// True if icon should have vertical-align added\ninline, \n// Optional reference for SVG/SPAN, extracted by React.forwardRef()\nref) => {\n    // Get default properties\n    const defaultProps = inline\n        ? inlineDefaults\n        : defaultExtendedIconCustomisations;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Check mode\n    const mode = props.mode || 'svg';\n    // Create style\n    const style = {};\n    const customStyle = props.style || {};\n    // Create SVG component properties\n    const componentProps = {\n        ...(mode === 'svg' ? svgDefaults : {}),\n        ref,\n    };\n    // Get element properties\n    for (let key in props) {\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch (key) {\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'children':\n            case 'onLoad':\n            case 'mode':\n            case '_ref':\n            case '_inline':\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] =\n                    value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                }\n                else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = '-0.125em';\n    }\n    if (mode === 'svg') {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle,\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === 'string') {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, '_');\n        }\n        // Add icon stuff\n        componentProps.dangerouslySetInnerHTML = {\n            __html: cleanUpInnerHTML(replaceIDs(item.body, id ? () => id + 'ID' + localCounter++ : 'iconifyReact')),\n        };\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement('svg', componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === 'mask' ||\n        (mode === 'bg' ? false : body.indexOf('currentColor') !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + '',\n        height: height + '',\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        '--svg': svgToURL(html),\n        'width': fixSize(renderAttribs.width),\n        'height': fixSize(renderAttribs.height),\n        ...commonProps,\n        ...(useMask ? monotoneProps : coloredProps),\n        ...customStyle,\n    };\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement('span', componentProps);\n};\n\n/**\n * Enable cache\n */\nfunction enableCache(storage) {\n    toggleBrowserCache(storage, true);\n}\n/**\n * Disable cache\n */\nfunction disableCache(storage) {\n    toggleBrowserCache(storage, false);\n}\n/**\n * Initialise stuff\n */\n// Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    // Set cache and load existing cache\n    initBrowserStorage();\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (let key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\nclass IconComponent extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            // Render placeholder before component is mounted\n            icon: null,\n        };\n    }\n    /**\n     * Abort loading icon\n     */\n    _abortLoading() {\n        if (this._loading) {\n            this._loading.abort();\n            this._loading = null;\n        }\n    }\n    /**\n     * Update state\n     */\n    _setData(icon) {\n        if (this.state.icon !== icon) {\n            this.setState({\n                icon,\n            });\n        }\n    }\n    /**\n     * Check if icon should be loaded\n     */\n    _checkIcon(changed) {\n        const state = this.state;\n        const icon = this.props.icon;\n        // Icon is an object\n        if (typeof icon === 'object' &&\n            icon !== null &&\n            typeof icon.body === 'string') {\n            // Stop loading\n            this._icon = '';\n            this._abortLoading();\n            if (changed || state.icon === null) {\n                // Set data if it was changed\n                this._setData({\n                    data: icon,\n                });\n            }\n            return;\n        }\n        // Invalid icon?\n        let iconName;\n        if (typeof icon !== 'string' ||\n            (iconName = stringToIcon(icon, false, true)) === null) {\n            this._abortLoading();\n            this._setData(null);\n            return;\n        }\n        // Load icon\n        const data = getIconData(iconName);\n        if (!data) {\n            // Icon data is not available\n            if (!this._loading || this._loading.name !== icon) {\n                // New icon to load\n                this._abortLoading();\n                this._icon = '';\n                this._setData(null);\n                if (data !== null) {\n                    // Icon was not loaded\n                    this._loading = {\n                        name: icon,\n                        abort: loadIcons([iconName], this._checkIcon.bind(this, false)),\n                    };\n                }\n            }\n            return;\n        }\n        // Icon data is available\n        if (this._icon !== icon || state.icon === null) {\n            // New icon or icon has been loaded\n            this._abortLoading();\n            this._icon = icon;\n            // Add classes\n            const classes = ['iconify'];\n            if (iconName.prefix !== '') {\n                classes.push('iconify--' + iconName.prefix);\n            }\n            if (iconName.provider !== '') {\n                classes.push('iconify--' + iconName.provider);\n            }\n            // Set data\n            this._setData({\n                data,\n                classes,\n            });\n            if (this.props.onLoad) {\n                this.props.onLoad(icon);\n            }\n        }\n    }\n    /**\n     * Component mounted\n     */\n    componentDidMount() {\n        this._checkIcon(false);\n    }\n    /**\n     * Component updated\n     */\n    componentDidUpdate(oldProps) {\n        if (oldProps.icon !== this.props.icon) {\n            this._checkIcon(true);\n        }\n    }\n    /**\n     * Abort loading\n     */\n    componentWillUnmount() {\n        this._abortLoading();\n    }\n    /**\n     * Render\n     */\n    render() {\n        const props = this.props;\n        const icon = this.state.icon;\n        if (icon === null) {\n            // Render placeholder\n            return props.children\n                ? props.children\n                : react__WEBPACK_IMPORTED_MODULE_0__.createElement('span', {});\n        }\n        // Add classes\n        let newProps = props;\n        if (icon.classes) {\n            newProps = {\n                ...props,\n                className: (typeof props.className === 'string'\n                    ? props.className + ' '\n                    : '') + icon.classes.join(' '),\n            };\n        }\n        // Render icon\n        return render({\n            ...defaultIconProps,\n            ...icon.data,\n        }, newProps, props._inline, props._ref);\n    }\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */\nconst Icon = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Icon(props, ref) {\n    const newProps = {\n        ...props,\n        _ref: ref,\n        _inline: false,\n    };\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconComponent, newProps);\n});\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */\nconst InlineIcon = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function InlineIcon(props, ref) {\n    const newProps = {\n        ...props,\n        _ref: ref,\n        _inline: true,\n    };\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconComponent, newProps);\n});\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/react/dist/iconify.mjs\n");

/***/ })

};
;
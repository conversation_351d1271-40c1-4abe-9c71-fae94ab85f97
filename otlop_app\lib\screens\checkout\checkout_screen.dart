import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/providers/restaurant_cart_provider.dart';
import 'package:otlop_app/providers/location_provider.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/services/order_service.dart';
import 'package:otlop_app/services/restaurant_service.dart';
import 'package:otlop_app/models/restaurant_model.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/widgets/location_dialog.dart';
import 'package:otlop_app/widgets/order_success_dialog.dart';

class CheckoutScreen extends StatefulWidget {
  final Map<int, Restaurant>? restaurantData;

  const CheckoutScreen({super.key, this.restaurantData});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _noteController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _orderPlaced = false; // Flag to prevent showing empty cart after order placement
  double _deliveryFee = 5.0; // Default fallback
  bool _isCalculatingDeliveryFee = false;
  final OrderService _orderService = OrderService();
  final RestaurantService _restaurantService = RestaurantService();
  Map<int, Restaurant> _restaurantData = {};
  bool _isLoadingRestaurantData = false;

  @override
  void initState() {
    super.initState();
    // Initialize restaurant data from passed parameter
    if (widget.restaurantData != null) {
      _restaurantData = Map.from(widget.restaurantData!);
    }
    // Use post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadSelectedLocation();
        if (widget.restaurantData == null) {
          _loadRestaurantData();
        }
        _calculateDeliveryFee();
      }
    });
  }

  // Load restaurant data for all restaurants in cart
  Future<void> _loadRestaurantData() async {
    setState(() {
      _isLoadingRestaurantData = true;
    });

    try {
      final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
      final restaurantIds = cartProvider.getRestaurantIdsWithItems();

      for (final restaurantId in restaurantIds) {
        try {
          final restaurantResponse = await _restaurantService.getRestaurantById(restaurantId.toString());
          final restaurant = Restaurant.fromJson(restaurantResponse);
          _restaurantData[restaurantId] = restaurant;
        } catch (e) {
          debugPrint('Error loading restaurant $restaurantId: $e');
        }
      }
    } catch (e) {
      debugPrint('Error loading restaurant data: $e');
    } finally {
      setState(() {
        _isLoadingRestaurantData = false;
      });
    }
  }

  // Calculate delivery fee based on restaurant and customer location
  Future<void> _calculateDeliveryFee() async {
    final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);

    final restaurantIds = cartProvider.getRestaurantIdsWithItems();
    if (restaurantIds.isEmpty) return;

    setState(() {
      _isCalculatingDeliveryFee = true;
    });

    try {
      // Calculate total delivery fee for all restaurants
      double totalDeliveryFee = 0.0;

      for (final restaurantId in restaurantIds) {
        // Check if we have restaurant data loaded
        final restaurant = _restaurantData[restaurantId];
        if (restaurant != null && restaurant.deliveryFee != null) {
          totalDeliveryFee += restaurant.deliveryFee!;
          continue;
        }

        // If no restaurant data, try to get it from API
        final restaurantData = await _restaurantService.getRestaurantById(restaurantId.toString());
        final restaurantLat = restaurantData['latitude'] ?? restaurantData['Latitude'];
        final restaurantLng = restaurantData['longitude'] ?? restaurantData['Longitude'];

        if (restaurantLat == null || restaurantLng == null) {
          debugPrint('Restaurant coordinates not available, using default delivery fee');
          totalDeliveryFee += 5.0; // Default fee
          continue;
        }

        // Get customer location
        double? customerLat, customerLng;

        // Try to get from selected location first
        if (locationProvider.selectedLocation != null) {
          customerLat = locationProvider.selectedLocation!.latitude;
          customerLng = locationProvider.selectedLocation!.longitude;
        } else {
          // Try to get current position
          final position = await locationProvider.getCurrentPosition();
          if (position != null) {
            customerLat = position.latitude;
            customerLng = position.longitude;
          }
        }

        if (customerLat == null || customerLng == null) {
          debugPrint('Customer coordinates not available, using default delivery fee');
          totalDeliveryFee += 5.0; // Default fee
          continue;
        }

        // Calculate delivery fee from backend for this restaurant
        final calculatedFee = await _orderService.calculateDeliveryFee(
          restaurantLat: restaurantLat.toDouble(),
          restaurantLng: restaurantLng.toDouble(),
          customerLat: customerLat,
          customerLng: customerLng,
        );

        totalDeliveryFee += calculatedFee;
      }

      setState(() {
        _deliveryFee = totalDeliveryFee;
      });

    } catch (e) {
      print('Error calculating delivery fee: $e');
    } finally {
      setState(() {
        _isCalculatingDeliveryFee = false;
      });
    }
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  void _loadSelectedLocation() {
    if (!mounted) return;

    try {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      final selectedLocation = locationProvider.selectedLocation;

      if (selectedLocation != null && mounted) {
        setState(() {
        });
      }
    } catch (e) {
      debugPrint('Error loading selected location: $e');
    }
  }

  Future<void> _showLocationDialog() async {
    await showDialog(
      context: context,
      builder: (context) => const LocationDialog(),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadSelectedLocation();
      }
    });
  }

  Future<void> _placeOrder() async {
    print('_placeOrder method called');

    if (!_formKey.currentState!.validate()) {
      print('Form validation failed');
      return;
    }

    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    final selectedLocation = locationProvider.selectedLocation;

    if (selectedLocation == null) {
      print('No location selected');
      setState(() {
        _errorMessage = 'الرجاء اختيار عنوان التوصيل';
      });
      return;
    }

    print('Starting order placement...');
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Check if user is logged in
      if (authProvider.user == null || authProvider.user!.id.isEmpty) {
        debugPrint('User not logged in');
        setState(() {
          _isLoading = false;
          _errorMessage = 'يجب تسجيل الدخول أولاً';
        });
        return;
      }

      // Check if cart has items
      final restaurantIds = cartProvider.getRestaurantIdsWithItems();
      if (restaurantIds.isEmpty) {
        debugPrint('Cart is empty');
        setState(() {
          _isLoading = false;
          _errorMessage = 'السلة فارغة';
        });
        return;
      }

      // Get all cart items from all restaurants
      List<dynamic> allCartItems = [];
      double subtotal = 0;
      String restaurantName = '';

      for (final restaurantId in restaurantIds) {
        final restaurantCartItems = cartProvider.getCartItemsForRestaurant(restaurantId);
        allCartItems.addAll(restaurantCartItems);
        subtotal += cartProvider.getCartTotalForRestaurant(restaurantId);

        // Get restaurant name from first restaurant
        if (restaurantName.isEmpty && restaurantCartItems.isNotEmpty) {
          restaurantName = _restaurantData[restaurantId]?.name ?? restaurantCartItems.first.restaurantName;
        }
      }

      debugPrint('Cart has ${allCartItems.length} items');
      debugPrint('User ID: ${authProvider.user!.id}');

      // Create order data with location coordinates
      final orderData = {
        'address': selectedLocation.address ?? selectedLocation.name,
        'paymentMethod': 'cash', // Only cash on delivery
        'note': _noteController.text.trim(),
        'endPointLatitude': selectedLocation.latitude,
        'endPointLongitude': selectedLocation.longitude,
      };

      debugPrint('Order data prepared: $orderData');
      debugPrint('Cart items count: ${allCartItems.length}');
      debugPrint('Customer ID from context: ${Provider.of<AuthProvider>(context, listen: false).user?.id}');

      final double deliveryFee = _deliveryFee; // Dynamic delivery fee from backend
      final double totalPrice = subtotal + deliveryFee;
      final int itemCount = allCartItems.length;

      debugPrint('Cart details before order: $itemCount items, total: $totalPrice, restaurant: $restaurantName');

      // For now, we'll use the OrderProvider to place the order
      // In the future, this should be updated to handle multiple restaurants
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);

      // Convert cart items to the format expected by OrderProvider
      // This is a temporary solution until we implement multi-restaurant order support
      if (restaurantIds.length == 1) {
        final restaurantId = restaurantIds.first;
        final restaurantCartItems = cartProvider.getCartItemsForRestaurant(restaurantId);

        // Clear the order provider's cart and add our items
        await orderProvider.clearCart();
        for (final cartItem in restaurantCartItems) {
          // Convert CartItem to Product format for OrderProvider
          final product = Product(
            id: cartItem.productId,
            name: cartItem.productName,
            price: cartItem.price,
            restaurantId: cartItem.restaurantId,
            restaurantName: cartItem.restaurantName,
            image: cartItem.productImage,
            available: true, // Assume available since it's in cart
          );

          await orderProvider.addItemToCart(
            product,
            cartItem.quantity,
            cartItem.selectedIngredients,
            null, // note
          );
        }
      }

      // Call API to place order
      debugPrint('About to call orderProvider.placeOrder with data: $orderData');
      final orderResult = await orderProvider.placeOrder(orderData, context: context);

      debugPrint('Order placement result: $orderResult');
      debugPrint('Order provider error after placement: ${orderProvider.error}');
      debugPrint('Order result type: ${orderResult.runtimeType}');
      debugPrint('Order result is null: ${orderResult == null}');

      // Set flag to prevent showing empty cart immediately
      setState(() {
        _isLoading = false;
        _orderPlaced = true;
      });

      if (!mounted) return;

      // Check for errors from order provider
      if (orderProvider.error != null) {
        print('Order provider error: ${orderProvider.error}');
        setState(() {
          _errorMessage = _getArabicErrorMessage(orderProvider.error!);
          _orderPlaced = false; // Reset flag on error
        });
        return;
      }

      // Clear the cart after successful order
      if (orderResult != null && orderResult['orderId'] != null && orderResult['orderId'].toString().isNotEmpty) {
        // Clear the restaurant cart
        await cartProvider.clearAllCarts();
      }

      // Show success dialog with order details
      if (orderResult != null && orderResult['orderId'] != null && orderResult['orderId'].toString().isNotEmpty) {
        print('Showing success dialog with order details: $orderResult');

        // Show success dialog immediately
        print('About to show success dialog...');
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => OrderSuccessDialog(
              orderId: orderResult['orderId'].toString(),
              totalPrice: totalPrice, // Use calculated total
              deliveryFee: deliveryFee,
              restaurantName: restaurantName, // Use saved restaurant name
              itemCount: itemCount, // Use saved item count
              autoNavigate: true, // Enable auto-navigation
            ),
          ).then((_) {
            print('Dialog was closed');
          });
        }
      } else {
        print('Order result is null or missing orderId. OrderResult: $orderResult');
        print('Showing test dialog anyway for debugging...');

        // Show test dialog for debugging
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => OrderSuccessDialog(
            orderId: 'DEBUG_ORDER_${DateTime.now().millisecondsSinceEpoch}',
            totalPrice: totalPrice,
            deliveryFee: deliveryFee,
            restaurantName: restaurantName,
            itemCount: itemCount,
            autoNavigate: true,
          ),
        );

        // Also show error message
        setState(() {
          _errorMessage = 'تم إنشاء الطلب ولكن لم يتم إرجاع رقم الطلب من الخادم.';
          _orderPlaced = false; // Reset flag on error
        });
      }
    } catch (e) {
      print('Error placing order: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = _getArabicErrorMessage(e.toString());
      });
    }
  }

  // Helper method to convert error messages to Arabic
  String _getArabicErrorMessage(String error) {
    final lowerError = error.toLowerCase();

    // Network and connection errors
    if (lowerError.contains('network') || lowerError.contains('connection') || lowerError.contains('timeout')) {
      return 'خطأ في الاتصال بالإنترنت. تأكد من اتصالك وحاول مرة أخرى.';
    }

    // Authentication errors
    if (lowerError.contains('customer id is required') || lowerError.contains('unauthorized') || lowerError.contains('authentication')) {
      return 'خطأ في المصادقة. الرجاء تسجيل الدخول مرة أخرى.';
    }

    // Cart validation errors
    if (lowerError.contains('cart is empty') || lowerError.contains('your cart is empty')) {
      return 'السلة فارغة. أضف منتجات إلى السلة أولاً.';
    }

    if (lowerError.contains('different restaurant') || lowerError.contains('same restaurant')) {
      return 'جميع المنتجات يجب أن تكون من نفس المطعم.';
    }

    // Server errors
    if (lowerError.contains('500') || lowerError.contains('server error') || lowerError.contains('internal server')) {
      return 'خطأ في الخادم. الرجاء المحاولة مرة أخرى لاحقاً.';
    }

    // Validation errors
    if (lowerError.contains('400') || lowerError.contains('bad request') || lowerError.contains('validation')) {
      return 'بيانات الطلب غير صحيحة. تأكد من جميع البيانات وحاول مرة أخرى.';
    }

    // Location errors
    if (lowerError.contains('location') || lowerError.contains('address')) {
      return 'خطأ في عنوان التوصيل. تأكد من اختيار عنوان صحيح.';
    }

    // Payment errors
    if (lowerError.contains('payment')) {
      return 'خطأ في طريقة الدفع. الرجاء المحاولة مرة أخرى.';
    }

    // Order creation specific errors
    if (lowerError.contains('order creation failed')) {
      return 'فشل في إنشاء الطلب. تحقق من البيانات وحاول مرة أخرى.';
    }

    // Check for specific backend errors
    if (lowerError.contains('invalid restaurant id') || lowerError.contains('restaurant not found')) {
      return 'خطأ في معرف المطعم. الرجاء إعادة اختيار المنتجات من المطعم.';
    }

    if (lowerError.contains('product not found')) {
      return 'أحد المنتجات غير متوفر. الرجاء تحديث السلة.';
    }

    if (lowerError.contains('database schema issue') || lowerError.contains('failed to create cart item')) {
      return 'خطأ في قاعدة البيانات. الرجاء الاتصال بالدعم الفني.';
    }

    // Generic fallback with original error for debugging
    return 'فشل في إتمام الطلب: $error';
  }

  // Show detailed error dialog for debugging
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: SelectableText(message), // Make text selectable for copying
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  // Test API connection
  void _testSuccessDialog() {
    print('Testing success dialog...');
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => OrderSuccessDialog(
        orderId: 'TEST123',
        totalPrice: 25.0,
        deliveryFee: 5.0,
        restaurantName: 'مطعم تجريبي',
        itemCount: 3,
        autoNavigate: false, // Don't auto-navigate in test
      ),
    );
  }

  Future<void> _testApiConnection() async {
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      print('Testing API connection...');
      print('User logged in: ${authProvider.user != null}');
      print('User ID: ${authProvider.user?.id}');
      print('Cart items: ${orderProvider.cartItems.length}');

      // Test a simple API call
      await orderProvider.loadUserOrders(context: context);
      print('API connection test successful');

      _showErrorDialog('اختبار الاتصال', 'الاتصال بالخادم يعمل بشكل صحيح\nمعرف المستخدم: ${authProvider.user?.id}\nعدد المنتجات في السلة: ${orderProvider.cartItems.length}');
    } catch (e) {
      print('API connection test failed: $e');
      _showErrorDialog('خطأ في الاتصال', 'فشل في الاتصال بالخادم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إتمام الطلب',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.deepPurple,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),

      ),
      body: Consumer<RestaurantCartProvider>(
        builder: (context, cartProvider, child) {
          final restaurantIds = cartProvider.getRestaurantIdsWithItems();
          final hasItems = restaurantIds.isNotEmpty;

          // Don't show empty cart if order was just placed or if we're loading
          if (!hasItems && !_orderPlaced && !_isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'السلة فارغة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('العودة للسلة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          // Calculate subtotal from all restaurants
          double subtotal = 0.0;
          int totalItems = 0;
          for (final restaurantId in restaurantIds) {
            subtotal += cartProvider.getCartTotalForRestaurant(restaurantId);
            totalItems += cartProvider.getCartItemCountForRestaurant(restaurantId);
          }
          final deliveryFee = _deliveryFee; // Dynamic delivery fee from backend
          final total = subtotal + deliveryFee;

          return SingleChildScrollView(
            child: Column(
              children: [
                // Order Summary Card
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.receipt_long,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'ملخص الطلب',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withAlpha(15),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.primary.withAlpha(50),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${cartItems.length} منتج',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            InkWell(
                              onTap: () => Navigator.pop(context),
                              child: Text(
                                'عرض السلة',
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Delivery Address Card
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'عنوان التوصيل',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Address Selection
                      Consumer<LocationProvider>(
                        builder: (context, locationProvider, child) {
                          // Safely get the selected location without triggering setState during build
                          final selectedLocation = locationProvider.selectedLocation;

                          return InkWell(
                            onTap: _showLocationDialog,
                            borderRadius: BorderRadius.circular(12),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: selectedLocation != null
                                    ? theme.colorScheme.primary.withAlpha(100)
                                    : Colors.grey[300]!,
                                  width: selectedLocation != null ? 2 : 1,
                                ),
                                borderRadius: BorderRadius.circular(12),
                                color: selectedLocation != null
                                  ? theme.colorScheme.primary.withAlpha(15)
                                  : Colors.grey[50],
                              ),
                              child: selectedLocation != null
                                ? Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            selectedLocation.isDefault ? Icons.home : Icons.location_on,
                                            color: theme.colorScheme.primary,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              selectedLocation.name,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: theme.colorScheme.primary,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                          Icon(
                                            Icons.edit,
                                            color: theme.colorScheme.primary,
                                            size: 18,
                                          ),
                                        ],
                                      ),
                                      if (selectedLocation.address != null) ...[
                                        const SizedBox(height: 8),
                                        Text(
                                          selectedLocation.address!,
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ],
                                  )
                                : Row(
                                    children: [
                                      Icon(
                                        Icons.add_location_alt,
                                        color: Colors.grey[600],
                                        size: 24,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'اختر عنوان التوصيل',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const Spacer(),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.grey[400],
                                        size: 16,
                                      ),
                                    ],
                                  ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),


                // Additional Notes Card
                // Container(
                //   margin: const EdgeInsets.symmetric(horizontal: 16),
                //   padding: const EdgeInsets.all(20),
                //   decoration: BoxDecoration(
                //     color: Colors.white,
                //     borderRadius: BorderRadius.circular(16),
                //     boxShadow: [
                //       BoxShadow(
                //         color: Colors.black.withAlpha(10),
                //         blurRadius: 10,
                //         offset: const Offset(0, 2),
                //       ),
                //     ],
                //   ),
                  // child: Form(
                  //   key: _formKey,
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Icon(
                  //             Icons.note_alt,
                  //             color: theme.colorScheme.primary,
                  //             size: 24,
                  //           ),
                  //           const SizedBox(width: 12),
                  //           const Text(
                  //             'ملاحظات إضافية',
                  //             style: TextStyle(
                  //               fontSize: 18,
                  //               fontWeight: FontWeight.bold,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       const SizedBox(height: 16),
                  //       TextFormField(
                  //         controller: _noteController,
                  //         decoration: InputDecoration(
                  //           hintText: 'أي تعليمات خاصة للتوصيل (اختياري)',
                  //           border: OutlineInputBorder(
                  //             borderRadius: BorderRadius.circular(12),
                  //             borderSide: BorderSide(color: Colors.grey[300]!),
                  //           ),
                  //           enabledBorder: OutlineInputBorder(
                  //             borderRadius: BorderRadius.circular(12),
                  //             borderSide: BorderSide(color: Colors.grey[300]!),
                  //           ),
                  //           focusedBorder: OutlineInputBorder(
                  //             borderRadius: BorderRadius.circular(12),
                  //             borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
                  //           ),
                  //           filled: true,
                  //           fillColor: Colors.grey[50],
                  //           contentPadding: const EdgeInsets.all(16),
                  //         ),
                  //         maxLines: 3,
                  //         textDirection: TextDirection.rtl,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                // ),

                const SizedBox(height: 16),

                // Payment Method Card
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.payment,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'طريقة الدفع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.green[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.money,
                              color: Colors.green[700],
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            const Expanded(
                              child: Text(
                                'الدفع عند الاستلام',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Icon(
                              Icons.check_circle,
                              color: Colors.green[700],
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Price Summary Card
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.calculate,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'تفاصيل السعر',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Subtotal
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'المجموع الفرعي',
                            style: TextStyle(fontSize: 16),
                          ),
                          Text(
                            '${subtotal.toStringAsFixed(2)} د.ل',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Delivery fee
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'رسوم التوصيل',
                            style: TextStyle(fontSize: 16),
                          ),
                          Text(
                            '${deliveryFee.toStringAsFixed(2)} د.ل',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),

                      const Divider(height: 24, thickness: 1),

                      // Total
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'المجموع الكلي',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${total.toStringAsFixed(2)} د.ل',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Error message
                if (_errorMessage != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.red[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.error, color: Colors.red[700], size: 24),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: () {
                            _showErrorDialog('تفاصيل الخطأ', _errorMessage!);
                          },
                          child: Text(
                            'عرض التفاصيل',
                            style: TextStyle(
                              color: Colors.red[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 24),
              ],
            ),
          );
        },
      ),

      // Bottom Place Order Button
      bottomNavigationBar: Consumer<OrderProvider>(
        builder: (context, orderProvider, child) {
          final cartItems = orderProvider.cartItems;
          if (cartItems.isEmpty) {
            return const SizedBox.shrink();
          }

          final subtotal = orderProvider.cartTotal;
          final deliveryFee = _deliveryFee; // Use dynamic delivery fee
          final total = subtotal + deliveryFee;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : () {
                    print('تأكيد الطلب button pressed'); // Debug print
                    _placeOrder();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.shopping_bag, size: 24),
                            const SizedBox(width: 12),
                            Text(
                              'تأكيد الطلب - ${total.toStringAsFixed(2)} د.ل',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
const sequelize = require("../config/database");
const models = require('../models/init-models')(sequelize);
const path = require('path');
const fs = require('fs/promises');  // Use the promise-based API
const fsSync = require('fs');  // Keep sync version for some operations

const { Product, Category, Restaurant_Category_Product, Ingredient, IngredientUsage } = models;

// Helper function to get the correct image URL for products
const getProductImageUrl = (imagePath) => {
    if (!imagePath) {
        // Return default image URL instead of null
        const apiBaseUrl = process.env.API_URL || 'http://localhost:3000';
        return `${apiBaseUrl}/uploads/products/default-product.png`;
    }
    
    // Normalize path (replace backslashes with forward slashes)
    const normalizedPath = imagePath.replace(/\\/g, '/');
    
    // If it's already a full URL, return it as is
    if (normalizedPath.startsWith('http')) {
        return normalizedPath;
    }
    
    const apiBaseUrl = process.env.API_URL || 'http://localhost:3000';
    
    // For paths that start with /, prepend the API URL but ensure no double slashes
    if (normalizedPath.startsWith('/')) {
        return `${apiBaseUrl}${normalizedPath}`;
    }
    
    // If the path contains 'public/', remove it since it's implied when serving static files
    if (normalizedPath.includes('public/')) {
        const publicPath = normalizedPath.replace(/^.*public\//, '/');
        return `${apiBaseUrl}${publicPath}`;
    }
    
    // If the path starts with 'uploads/', just prepend with the API URL and a slash
    if (normalizedPath.startsWith('uploads/')) {
        return `${apiBaseUrl}/${normalizedPath}`;
    }
    
    // For simple filenames, assume they're in the uploads/products directory
    return `${apiBaseUrl}/uploads/products/${normalizedPath}`;
};

exports.getAllProducts = async(req, res) => {
    try {
        const products = await Product.findAll({
            where: {
                Status: 1
            },
            include: [{
                model: Category,
                as: 'Category',
                where: {
                    Status: 1
                },
                attributes: ['CategoryID', 'CategoryName']
            }, {
                model: Ingredient,
                as: 'Ingredients',
                attributes: ['IngredientID', 'IngredientName']
            }]
        });
        
        // Format products and add image URLs
        const formattedProducts = products.map(product => {
            const productData = product.toJSON();

            // Map Details to Description for frontend compatibility
            if (productData.Details) {
                productData.Description = productData.Details;
            }

            // Add RestaurantID from the relationship
            if (productData.Restaurant_Category_Products && productData.Restaurant_Category_Products.length > 0) {
                productData.RestaurantID = productData.Restaurant_Category_Products[0].RestaurantID;
            }

            // Normalize Image path
            if (productData.Image) {
                // First normalize any backslashes to forward slashes
                productData.Image = productData.Image.replace(/\\/g, '/');

                // Generate the full URL
                productData.ImageUrl = getProductImageUrl(productData.Image);
            } else {
                // Set a default image URL
                const apiBaseUrl = process.env.API_URL || 'http://localhost:3000';
                productData.ImageUrl = `${apiBaseUrl}/uploads/products/default-product.png`;
            }

            return productData;
        });
        
        res.json({
            success: true,
            data: formattedProducts
        });
    } catch (error) {
        console.error('Error in getAllProducts:', error);
        res.status(500).json({ 
            success: false,
            message: error.message 
        });
    }
};

exports.getProduct = async(req, res) => {
    try {
        const product = await Product.findOne({
            where: {
                ProductID: req.params.id,
                Status: 1
            },
            include: [{
                model: Category,
                as: 'Category',
                where: {
                    Status: 1
                },
                attributes: ['CategoryID', 'CategoryName']
            }, {
                model: Ingredient,
                as: 'Ingredients',
                attributes: ['IngredientID', 'IngredientName']
            }]
        });
        if (!product) {
            return res.status(404).json({ 
                success: false,
                message: 'Product not found' 
            });
        }
        
        // Convert to JSON to be able to modify
        const productData = product.toJSON();
        
        // Map Details to Description for frontend compatibility
        if (productData.Details) {
            productData.Description = productData.Details;
        }
        
        // Ensure Category data is properly included
        if (productData.Category) {
            productData.CategoryName = productData.Category.CategoryName;
        }
        
        // Add image URL handling
        console.log('Product image path:', productData.Image);
        
        // Normalize any backslashes to forward slashes if Image exists
        if (productData.Image) {
            productData.Image = productData.Image.replace(/\\/g, '/');
            console.log('Normalized image path:', productData.Image);
        }
        
        // Always generate the ImageUrl, even if Image is null (will use default)
        productData.ImageUrl = getProductImageUrl(productData.Image);
        console.log('Generated image URL:', productData.ImageUrl);

        // Format ingredients for frontend compatibility if they exist
        if (productData.Ingredients && productData.Ingredients.length > 0) {
            console.log('Formatting ingredients for frontend:', productData.Ingredients.length);
            productData.Ingredients = productData.Ingredients.map(ingredient => ({
                IngredientID: ingredient.IngredientID,
                IngredientName: ingredient.IngredientName,
                // Add any additional fields if needed in the future
                Price: null, // Ingredients don't have prices in current schema
                Optional: true // All ingredients are optional for customer selection
            }));
        }

        res.json({
            success: true,
            data: productData
        });
    } catch (error) {
        console.error('Error in getProduct:', error);
        res.status(500).json({ 
            success: false,
            message: error.message 
        });
    }
};

exports.createProduct = async(req, res) => {
    try {
        const { ProductName, Price, CategoryID, RestaurantID, ingredients, Description } = req.body;
        
        // Process the image through imageHandler if there's a file
        let imagePath = null;
        if (req.file) {
            const { processProductImage } = require('../utils/imageHandler');
            console.log('Processing product image from file upload');
            // The processProductImage handles the file storage and returns the public path
            imagePath = await processProductImage(req.file, 'new');
            // Remove the 'public/' prefix from the path since it's implied when serving static files
            // and normalize slashes for consistent path format
            imagePath = imagePath.replace(/^public\//, '/').replace(/\\/g, '/');
            console.log('Processed image path:', imagePath);
        } else {
            console.log('No file provided for product image');
        }
        
        // Ensure image path is correctly formatted for database storage
        console.log('Image path being stored in database:', imagePath);
        
        const product = await Product.create({
            ProductName,
            Price: parseFloat(Price),
            CategoryID: parseInt(CategoryID),
            RestaurantID: parseInt(RestaurantID),
            Image: imagePath, // Store relative path without 'public/' prefix
            Status: 1,
            AvailabilityStatus: true,
            InsertDate: sequelize.fn('GETDATE'),
            UpdateDate: sequelize.fn('GETDATE'),
            Details: Description || null // Store Description as Details
        });

        console.log(`Created product with ID ${product.ProductID}, Image path: ${product.Image}`);

        // Handle ingredients
        if (ingredients) {
            console.log('Processing ingredients for new product:', product.ProductID);
            let processedIngredients = [];
            
            // Handle different formats that might come from the client
            if (Array.isArray(ingredients)) {
                // Case 1: Array of objects with IngredientName property
                if (ingredients.length > 0 && typeof ingredients[0] === 'object' && 'IngredientName' in ingredients[0]) {
                    console.log('Ingredients format: Array of objects with IngredientName');
                    processedIngredients = ingredients;
                } 
                // Case 2: Array of strings (ingredient names)
                else if (ingredients.length > 0 && typeof ingredients[0] === 'string') {
                    console.log('Ingredients format: Array of strings');
                    processedIngredients = ingredients.map(name => ({ IngredientName: name }));
                }
                // Case 3: Empty array or other format
                else {
                    console.log('Ingredients format: Empty array or unexpected format', ingredients);
                    processedIngredients = [];
                }
            } else if (typeof ingredients === 'string') {
                // Case 4: Single string (single ingredient)
                console.log('Ingredients format: Single string');
                processedIngredients = [{ IngredientName: ingredients }];
            } else {
                console.log('Unexpected ingredients format:', typeof ingredients, ingredients);
            }
            
            // Filter out empty ingredients
            processedIngredients = processedIngredients.filter(ing => 
                ing && ing.IngredientName && ing.IngredientName.trim() !== ''
            );
            
            if (processedIngredients.length > 0) {
                console.log(`Adding ${processedIngredients.length} ingredients to new product ${product.ProductID}`);
                
                const ingredientPromises = processedIngredients.map(ing => 
                    Ingredient.create({
                        ProductID: product.ProductID,
                        IngredientName: ing.IngredientName,
                        Status: 1
                    })
                );
                await Promise.all(ingredientPromises);
                console.log(`Successfully added ${processedIngredients.length} ingredients`);
            } else {
                console.log('No valid ingredients to process');
            }
        } else {
            console.log('No ingredients data provided for new product');
        }

        await Restaurant_Category_Product.create({
            RestaurantID: RestaurantID,
            CategoryID: CategoryID,
            ProductID: product.ProductID
        });

        // Return the complete product data with properly formatted image path
        const productData = {
            ...product.toJSON(),
            Image: imagePath // Use the normalized path
        };
        
        // Always add ImageUrl for direct use in the frontend
        productData.ImageUrl = getProductImageUrl(imagePath);
        console.log('Product data being returned to client:', {
            ProductID: productData.ProductID,
            Image: productData.Image,
            ImageUrl: productData.ImageUrl
        });

        res.status(201).json({
            success: true,
            message: 'تم إضافة المنتج بنجاح',
            data: productData
        });
    } catch (error) {
        console.error('Error in createProduct:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

exports.updateProduct = async(req, res) => {
    try {
        const { ProductName, Price, CategoryID, ingredients, Description } = req.body;
        const productId = req.params.id;

        const product = await Product.findByPk(productId);
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'المنتج غير موجود'
            });
        }

        const updateData = {
            ProductName,
            Price: parseFloat(Price),
            CategoryID: parseInt(CategoryID),
            Details: Description || null,
            UpdateDate: sequelize.fn('GETDATE')
        };

        if (req.file) {
            // Process the new image through imageHandler
            const { processProductImage, deleteImage } = require('../utils/imageHandler');
            
            // Delete old image if exists
            if (product.Image) {
                try {
                    await deleteImage(product.Image);
                } catch (err) {
                    console.error('Error deleting old image:', err);
                }
            }
            
            // Process and store the new image
            const newImagePath = await processProductImage(req.file, productId);
            // Remove the 'public/' prefix from the path and normalize slashes
            updateData.Image = newImagePath.replace(/^public\//, '/').replace(/\\/g, '/');
        }

        await product.update(updateData);

        // Handle ingredients
        if (ingredients) {
            console.log('Processing ingredients for product:', productId);
            let processedIngredients = [];
            
            // Handle different formats that might come from the client
            if (Array.isArray(ingredients)) {
                // Case 1: Array of objects with IngredientName property
                if (ingredients.length > 0 && typeof ingredients[0] === 'object' && 'IngredientName' in ingredients[0]) {
                    console.log('Ingredients format: Array of objects with IngredientName');
                    processedIngredients = ingredients;
                } 
                // Case 2: Array of strings (ingredient names)
                else if (ingredients.length > 0 && typeof ingredients[0] === 'string') {
                    console.log('Ingredients format: Array of strings');
                    processedIngredients = ingredients.map(name => ({ IngredientName: name }));
                }
                // Case 3: Empty array or other format
                else {
                    console.log('Ingredients format: Empty array or unexpected format', ingredients);
                    processedIngredients = [];
                }
            } else if (typeof ingredients === 'string') {
                // Case 4: Single string (single ingredient)
                console.log('Ingredients format: Single string');
                processedIngredients = [{ IngredientName: ingredients }];
            } else {
                console.log('Unexpected ingredients format:', typeof ingredients, ingredients);
            }
            
            // Filter out empty ingredients
            processedIngredients = processedIngredients.filter(ing => 
                ing && ing.IngredientName && ing.IngredientName.trim() !== ''
            );
            
            if (processedIngredients.length > 0) {
                console.log(`Adding ${processedIngredients.length} ingredients to product ${productId}`);

                // First, delete any IngredientUsage records that reference ingredients for this product
                const existingIngredients = await Ingredient.findAll({
                    where: { ProductID: productId },
                    attributes: ['IngredientID']
                });

                if (existingIngredients.length > 0) {
                    const ingredientIds = existingIngredients.map(ing => ing.IngredientID);

                    // Delete IngredientUsage records first to avoid foreign key constraint
                    await IngredientUsage.destroy({
                        where: { IngredientID: ingredientIds }
                    });
                    console.log(`Deleted IngredientUsage records for ${ingredientIds.length} ingredients`);

                    // Now safely delete the ingredients
                    await Ingredient.destroy({ where: { ProductID: productId } });
                    console.log(`Deleted ${existingIngredients.length} existing ingredients`);
                }

                // Create new ingredients
                const ingredientPromises = processedIngredients.map(ing =>
                    Ingredient.create({
                        ProductID: productId,
                        IngredientName: ing.IngredientName,
                        Status: 1
                    })
                );
                await Promise.all(ingredientPromises);
                console.log(`Successfully added ${processedIngredients.length} ingredients`);
            } else {
                console.log('No valid ingredients to process');
                // Still remove any existing ingredients if the array is empty
                const existingIngredients = await Ingredient.findAll({
                    where: { ProductID: productId },
                    attributes: ['IngredientID']
                });

                if (existingIngredients.length > 0) {
                    const ingredientIds = existingIngredients.map(ing => ing.IngredientID);

                    // Delete IngredientUsage records first
                    await IngredientUsage.destroy({
                        where: { IngredientID: ingredientIds }
                    });

                    // Then delete the ingredients
                    await Ingredient.destroy({ where: { ProductID: productId } });
                    console.log(`Cleaned up ${existingIngredients.length} ingredients and their usage records`);
                }
            }
        } else {
            console.log('No ingredients data provided');
        }

        // Return the complete product data with properly formatted image path
        const updatedProduct = await Product.findByPk(productId, {
            include: [{
                model: Category,
                as: 'Category',
                attributes: ['CategoryID', 'CategoryName']
            }, {
                model: Ingredient,
                as: 'Ingredients',
                attributes: ['IngredientID', 'IngredientName']
            }]
        });

        if (!updatedProduct) {
            throw new Error('Failed to retrieve updated product');
        }

        // Format the product data for the response
        const formattedProduct = updatedProduct.toJSON();
        
        // Normalize the image path if it exists
        if (formattedProduct.Image) {
            formattedProduct.Image = formattedProduct.Image.replace(/\\/g, '/');
        }
        
        // Always add ImageUrl for direct use in frontend
        formattedProduct.ImageUrl = getProductImageUrl(formattedProduct.Image);

        res.json({
            success: true,
            message: 'تم تحديث المنتج بنجاح',
            data: formattedProduct
        });
    } catch (error) {
        console.error('Error in updateProduct:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

exports.setAvailability = async(req, res) => {
    const t = await sequelize.transaction();
    try {
        const product = await Product.findOne({
            where: {
                ProductID: req.params.id,
                Status: 1
            }
        }, { transaction: t });

        if (!product) {
            await t.rollback();
            return res.status(404).json({ 
                success: false,
                message: 'Product not found' 
            });
        }

        await product.update({ 
            AvailabilityStatus: req.body.isAvailable,
            UpdateDate: sequelize.fn('GETDATE')
        }, { transaction: t });

        await t.commit();

        res.json({
            success: true,
            data: product,
            message: 'Product availability updated successfully'
        });
    } catch (error) {
        await t.rollback();
        console.error('Error in setAvailability:', error);
        res.status(400).json({ 
            success: false,
            message: error.message 
        });
    }
};

exports.changeStatus = async(req, res) => {
    const t = await sequelize.transaction();
    try {
        const product = await Product.findByPk(req.params.id, { transaction: t });
        if (!product) {
            await t.rollback();
            return res.status(404).json({ 
                success: false,
                message: 'Product not found' 
            });
        }
        await product.update({ 
            Status: req.body.status,
            UpdateDate: sequelize.fn('GETDATE')
        }, { transaction: t });

        await t.commit();

        res.json({ 
            success: true,
            message: 'Product status updated successfully' 
        });
    } catch (error) {
        await t.rollback();
        console.error('Error in changeStatus:', error);
        res.status(400).json({ 
            success: false,
            message: error.message 
        });
    }
};

exports.deleteProduct = async(req, res) => {
    const t = await sequelize.transaction();
    try {
        const product = await Product.findByPk(req.params.id, { transaction: t });
        if (!product) {
            await t.rollback();
            return res.status(404).json({ 
                success: false,
                message: 'Product not found' 
            });
        }
        await product.update({ 
            Status: 2,
            UpdateDate: sequelize.fn('GETDATE')
        }, { transaction: t });

        await t.commit();

        res.json({ 
            success: true,
            message: 'Product deleted successfully' 
        });
    } catch (error) {
        await t.rollback();
        console.error('Error in deleteProduct:', error);
        res.status(500).json({ 
            success: false,
            message: error.message 
        });
    }
};

exports.getProductsByRestaurantId = async(req, res) => {
    try {
        const restaurantIdParam = req.params.restaurantId;
        const restaurantId = parseInt(restaurantIdParam);

        // Validate if restaurantId is a valid integer
        if (isNaN(restaurantId) || restaurantId <= 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid Restaurant ID provided.'
            });
        }

        const products = await Product.findAll({
            include: [{
                model: Restaurant_Category_Product,
                as: 'Restaurant_Category_Products',
                where: {
                    RestaurantID: restaurantId // Use the validated integer ID
                },
                attributes: ['RestaurantID'] // Include RestaurantID in the response
            }, {
                model: Category,
                as: 'Category',
                required: true, // Ensure products belong to a category
                attributes: ['CategoryID', 'CategoryName']
            }],
            where: {
                Status: 1 // Only fetch active products
            },
            order: [
                // Order by Category Name first, then Product Name within each category
                [{ model: Category, as: 'Category' }, 'CategoryName', 'ASC'],
                ['ProductName', 'ASC']
            ]
        });

        // Group products by category
        const groupedProducts = {};
        products.forEach(product => {
            const productData = product.toJSON(); // Get plain data object

            // Ensure category data exists (though `required: true` should guarantee this)
            if (!productData.Category || !productData.Category.CategoryID) {
                console.warn(`Product ${productData.ProductID} is missing category information despite 'required: true'. Skipping.`);
                return;
            }

            const categoryId = productData.Category.CategoryID;
            const categoryName = productData.Category.CategoryName;

            // Initialize category group if it doesn't exist
            if (!groupedProducts[categoryId]) {
                groupedProducts[categoryId] = {
                    CategoryID: categoryId,
                    CategoryName: categoryName,
                    products: []
                };
            }

            // Format the product data for the response
            const formattedProduct = {
                ...productData,
                // Add RestaurantID from the relationship
                RestaurantID: productData.Restaurant_Category_Products && productData.Restaurant_Category_Products.length > 0
                    ? productData.Restaurant_Category_Products[0].RestaurantID
                    : null,
                // Normalize the image path if it exists
                Image: productData.Image ? productData.Image.replace(/\\/g, '/') : null,
                // Always add an ImageUrl using our helper function
                // ImageUrl: getProductImageUrl(productData.Image)
            };

            // Remove the nested objects since we have flat properties
            delete formattedProduct.Category;
            delete formattedProduct.Restaurant_Category_Products;

            groupedProducts[categoryId].products.push(formattedProduct);
        });

        // Convert the grouped object into an array of categories
        const result = Object.values(groupedProducts);

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('Error in getProductsByRestaurantId:', error);
        // Check for specific Sequelize errors if needed, otherwise send generic error
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve products for the restaurant. ' + error.message
        });
    }
};

// Simplified function for serving product images directly by ID
exports.getProductImageById = async(req, res) => {
    try {
        const productId = req.params.id;
        console.log(`Image request for product ${productId}`);
        
        // Get product to find the image URL
        const product = await Product.findOne({
            where: {
                ProductID: productId
            },
            attributes: ['ProductID', 'Image']
        });
        
        console.log(`Product record found:`, product ? `ID: ${product.ProductID}, Image: ${product.Image}` : 'No product found');
        
        // Normalize the image path if product exists
        let originalPath = null;
        if (product && product.Image) {
            originalPath = product.Image.replace(/\\/g, '/');
        }
        
        // Generate the image URL (will use default if no image)
        const imageUrl = getProductImageUrl(originalPath);
        
        console.log(`Sending image URL for product ${productId}:`, imageUrl);
        return res.json({ 
            // imageUrl: imageUrl,
            originalPath: originalPath,
            isDefault: !originalPath
        });
    } catch (error) {
        console.error('Error in getProductImageById:', error);
        // Still return a default image URL on error
        const apiBaseUrl = process.env.API_URL || `http://${req.headers.host}`;
        return res.json({
            success: false,
            message: 'Error retrieving product image',
            imageUrl: `${apiBaseUrl}/uploads/products/default-product.png`,
            isDefault: true,
            error: error.message
        });
    }
};

// Ensure default product image exists
const ensureDefaultProductImage = async () => {
    const defaultImageDir = path.join(process.cwd(), 'public', 'uploads', 'products');
    const defaultImagePath = path.join(defaultImageDir, 'default-product.png');
    
    try {
        // Check if the default image already exists
        await fs.access(defaultImagePath);
        console.log('Default product image exists.');
    } catch (err) {
        // Create directory if it doesn't exist
        try {
            await fs.mkdir(defaultImageDir, { recursive: true });
        } catch (mkdirErr) {
            // Ignore if directory already exists
            if (mkdirErr.code !== 'EEXIST') {
                console.error('Error creating default product image directory:', mkdirErr);
            }
        }
        
        // Create a simple 1x1 transparent PNG as fallback
        // This is a base64-encoded minimal transparent PNG
        const transparentPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
        
        try {
            const buffer = Buffer.from(transparentPNG, 'base64');
            await fs.writeFile(defaultImagePath, buffer);
            console.log('Created default product image.');
        } catch (writeErr) {
            console.error('Error creating default product image:', writeErr);
        }
    }
};

// Call this function during startup
ensureDefaultProductImage().catch(err => {
    console.error('Failed to ensure default product image exists:', err);
});

module.exports = {
    getAllProducts: exports.getAllProducts,
    getProduct: exports.getProduct,
    createProduct: exports.createProduct,
    updateProduct: exports.updateProduct,
    setAvailability: exports.setAvailability,
    changeStatus: exports.changeStatus,
    deleteProduct: exports.deleteProduct,
    getProductsByRestaurantId: exports.getProductsByRestaurantId,
    getProductImageById: exports.getProductImageById
};
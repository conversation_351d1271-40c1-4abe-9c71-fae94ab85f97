import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/restaurant_cart_provider.dart';
import 'package:otlop_app/models/product_model.dart' as models;
import 'package:otlop_app/screens/product/product_detail_screen.dart';
import 'package:otlop_app/screens/cart/cart_screen.dart';
import 'package:otlop_app/services/api_service.dart';

class RestaurantDetailScreen extends StatefulWidget {
  final String restaurantId;

  const RestaurantDetailScreen({
    super.key,
    required this.restaurantId,
  });

  @override
  State<RestaurantDetailScreen> createState() => _RestaurantDetailScreenState();
}

class _RestaurantDetailScreenState extends State<RestaurantDetailScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch restaurant details
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);
      restaurantProvider.fetchRestaurantById(widget.restaurantId);
      restaurantProvider.fetchRestaurantMenu(widget.restaurantId);
      restaurantProvider.fetchRestaurantWorkingHours(widget.restaurantId);
    });
  }

  // Build modern working hours bottom modal
  Widget _buildWorkingHoursBottomModal(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final today = DateTime.now().weekday; // 1 = Monday, 7 = Sunday

    // Get working hours from provider
    final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);
    final workingHours = restaurantProvider.workingHours;

    // Map weekday to Arabic day name (0 = Sunday, 6 = Saturday in API)
    final List<String> weekdays = [
      'الأحد',      // Sunday (0)
      'الإثنين',    // Monday (1)
      'الثلاثاء',   // Tuesday (2)
      'الأربعاء',   // Wednesday (3)
      'الخميس',    // Thursday (4)
      'الجمعة',     // Friday (5)
      'السبت',      // Saturday (6)
    ];

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.access_time,
                    color: primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'مواقيت العمل',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      fontFamily: 'Alx',
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'جميع الأيام',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Working hours list
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: workingHours.isEmpty
                ? Container(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد معلومات عن مواقيت العمل',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                            fontFamily: 'Alx',
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      for (int i = 0; i < workingHours.length; i++)
                        _buildCleanWorkingHoursRow(
                          context,
                          weekdays[workingHours[i].dayOfWeek],
                          workingHours[i].isClosed
                              ? 'مغلق'
                              : '${workingHours[i].formattedOpeningTime} - ${workingHours[i].formattedClosingTime}',
                          !workingHours[i].isClosed,
                          isToday: workingHours[i].dayOfWeek == (today % 7 == 0 ? 0 : today - 1),
                        ),
                    ],
                  ),
          ),

          // Info note
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.blue[100]!,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'قد تختلف ساعات العمل خلال العطلات والمناسبات الخاصة',
                    style: TextStyle(
                      color: Colors.blue[800],
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom padding for safe area
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  // Clean working hours row for bottom modal
  Widget _buildCleanWorkingHoursRow(
    BuildContext context,
    String day,
    String hours,
    bool isOpen,
    {bool isToday = false}
  ) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isToday ? primaryColor.withOpacity(0.08) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday ? primaryColor.withOpacity(0.2) : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Day name
          Expanded(
            flex: 2,
            child: Text(
              day,
              style: TextStyle(
                fontWeight: isToday ? FontWeight.w700 : FontWeight.w600,
                fontSize: 16,
                color: isToday ? primaryColor : Colors.black87,
                fontFamily: 'Alx',
              ),
            ),
          ),

          // Hours
          Expanded(
            flex: 3,
            child: Text(
              hours,
              style: TextStyle(
                fontSize: 15,
                color: isToday ? primaryColor : Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Status indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: isOpen
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: isOpen ? Colors.green : Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  isOpen ? 'مفتوح' : 'مغلق',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: isOpen ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show restaurant info modal
  void _showRestaurantInfoModal(BuildContext context, restaurant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'معلومات المطعم',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Restaurant info
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Restaurant name
                  _buildInfoRow(
                    icon: Icons.restaurant,
                    label: 'اسم المطعم',
                    value: restaurant.name,
                  ),

                  // City
                  if (restaurant.city != null && restaurant.city!.isNotEmpty)
                    _buildInfoRow(
                      icon: Icons.location_city,
                      label: 'المدينة',
                      value: restaurant.city!,
                    ),

                  // Address
                  if (restaurant.address != null && restaurant.address!.isNotEmpty)
                    _buildInfoRow(
                      icon: Icons.location_on,
                      label: 'العنوان',
                      value: restaurant.address!,
                    ),

                  // Restaurant type
                  if (restaurant.restaurantTypeName != null && restaurant.restaurantTypeName!.isNotEmpty)
                    _buildInfoRow(
                      icon: Icons.category,
                      label: 'نوع المطعم',
                      value: restaurant.restaurantTypeName!,
                    ),

                  // Phone numbers
                  if (restaurant.firstNumber != null && restaurant.firstNumber!.isNotEmpty)
                    _buildInfoRow(
                      icon: Icons.phone,
                      label: 'رقم الهاتف الأول',
                      value: restaurant.firstNumber!,
                      isPhone: true,
                    ),

                  if (restaurant.secondNumber != null && restaurant.secondNumber!.isNotEmpty)
                    _buildInfoRow(
                      icon: Icons.phone,
                      label: 'رقم الهاتف الثاني',
                      value: restaurant.secondNumber!,
                      isPhone: true,
                    ),
                ],
              ),
            ),

            // Close button
            Padding(
              padding: const EdgeInsets.all(20),
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إغلاق',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build info row for the modal
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isPhone = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          if (isPhone)
            IconButton(
              onPressed: () {
                // You can add phone call functionality here
                // launch('tel:$value');
              },
              icon: Icon(
                Icons.call,
                color: Colors.green[600],
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to format image URLs (both restaurant and product images)
  String _getImageUrl(String imageUrl) {
    if (imageUrl.isEmpty) return '';
    if (imageUrl.startsWith('http')) return imageUrl;

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }


  @override
  Widget build(BuildContext context) {
    return Consumer<RestaurantProvider>(
      builder: (context, restaurantProvider, child) {
        final restaurant = restaurantProvider.selectedRestaurant;

        if (restaurant == null) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              restaurant.name,
              style: const TextStyle(fontFamily: 'Alx'),
            ),
            actions: [
              IconButton(
                onPressed: () => _showRestaurantInfoModal(context, restaurant),
                icon: const Icon(Icons.info_outline),
                tooltip: 'معلومات المطعم',
              ),
            ],
          ),
          floatingActionButton: Consumer<RestaurantCartProvider>(
            builder: (context, cartProvider, child) {
              final restaurantId = int.tryParse(widget.restaurantId) ?? 0;
              final cartItems = cartProvider.getCartItemsForRestaurant(restaurantId);
              final itemCount = cartItems.fold<int>(0, (sum, item) => sum + item.quantity);

              if (itemCount == 0) {
                return const SizedBox.shrink(); // Hide button when cart is empty
              }

              return FloatingActionButton.extended(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CartScreen(specificRestaurantId: restaurantId),
                    ),
                  );
                },
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                icon: const Icon(Icons.shopping_cart),
                label: Text(
                  'عرض السلة ($itemCount)',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Alx',
                  ),
                ),
              );
            },
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Restaurant Header
                SizedBox(
                  height: 200,
                  child: Stack(
                    children: [
                      // Restaurant Image
                      restaurant.image != null && restaurant.image!.isNotEmpty
                          ? FadeInImage.assetNetwork(
                              placeholder: 'assets/images/loading.gif',
                              image: _getImageUrl(restaurant.image!),
                              width: double.infinity,
                              height: 200,
                              fit: BoxFit.cover,
                              fadeInDuration: const Duration(milliseconds: 300),
                              imageErrorBuilder: (context, error, stackTrace) {
                                debugPrint('Error loading restaurant image: ${error.toString()}');
                                debugPrint('Attempted URL: ${_getImageUrl(restaurant.image!)}');
                                return Image.asset(
                                  'assets/images/rest.jpg',
                                  width: double.infinity,
                                  height: 200,
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              'assets/images/rest.jpg',
                              width: double.infinity,
                              height: 200,
                              fit: BoxFit.cover,
                            ),

                      // Restaurant Info Card - Enhanced Design
                      Positioned(
                        bottom: 0,
                        left: 16,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(20),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      restaurant.name,
                                      style: const TextStyle(
                                        fontSize: 22,
                                        fontFamily: 'Alx',
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 6),
                                    if (restaurant.restaurantTypeName != null)
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).colorScheme.primary.withAlpha(30),
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.restaurant,
                                                  color: Theme.of(context).colorScheme.primary,
                                                  size: 14,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  restaurant.restaurantTypeName!,
                                                  style: TextStyle(
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.w500,
                                                    color: Theme.of(context).colorScheme.primary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: restaurant.isOpen
                                                  ? Colors.green.withAlpha(30)
                                                  : Colors.red.withAlpha(30),
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  restaurant.isOpen
                                                      ? Icons.check_circle
                                                      : Icons.cancel,
                                                  color: restaurant.isOpen
                                                      ? Colors.green
                                                      : Colors.red,
                                                  size: 14,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  restaurant.isOpen ? 'مفتوح' : 'مغلق',
                                                  style: TextStyle(
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.w500,
                                                    color: restaurant.isOpen
                                                        ? Colors.green
                                                        : Colors.red,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed: () {
                                  // Show working hours bottom modal
                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    builder: (context) => _buildWorkingHoursBottomModal(context),
                                  );
                                },
                                icon: const Icon(Icons.access_time),
                                label: const Text('مواقيت العمل'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context).colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Distance and Delivery Fee Section - Simple and Clean
                if (restaurant.distance != null || restaurant.deliveryFee != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        if (restaurant.distance != null) ...[
                          Icon(
                            Icons.location_on,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${restaurant.distance!.toStringAsFixed(1)} كم',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                        if (restaurant.distance != null && restaurant.deliveryFee != null) ...[
                          const SizedBox(width: 16),
                          Text(
                            '•',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                        if (restaurant.deliveryFee != null) ...[
                          Icon(
                            Icons.delivery_dining,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'رسوم التوصيل ${restaurant.deliveryFee!.toStringAsFixed(2)} د.ل',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                // Menu Sections - Enhanced Design
                Container(
                  margin: const EdgeInsets.only(top: 16),
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(28),
                      topRight: Radius.circular(28),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary.withAlpha(20),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.restaurant_menu,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'قائمة الطعام',
                                  style: TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'Alx',
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withAlpha(25),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.fastfood,
                                    size: 16,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'جميع الأصناف',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Menu items
                      _buildMenuSections(context, restaurantProvider),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuSections(BuildContext context, RestaurantProvider provider) {
    if (provider.isLoading) {
      return SizedBox(
        height: 200,
        child: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    final menu = provider.restaurantMenu;

    if (menu.isEmpty) {
      return SizedBox(
        height: 250,
        child: Center(
  child: Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Icon(
        Icons.restaurant_menu,
        size: 60,
        color: Colors.grey.shade300,
      ),
      const SizedBox(height: 20),
      Text(
        'لا توجد عناصر في القائمة',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey.shade700,
          letterSpacing: 0.2,
          fontFamily: 'Alx',
        ),
      ),
      const SizedBox(height: 10),
      Text(
        'الرجاء تحديث القائمة أو المحاولة لاحقاً',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Colors.grey.shade500,
          fontFamily: 'Alx',
        ),
      ),
      const SizedBox(height: 24),
      ElevatedButton.icon(
        onPressed: () {
          provider.fetchRestaurantMenu(widget.restaurantId);
        },
        icon: const Icon(Icons.refresh, size: 20),
        label: const Text(
          'تحديث القائمة',
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            fontFamily: 'Alx',
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6B46C1), // Primary purple
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 1,
        ),
      ),
    ],
  ),
)

      );
    }

    // Group products by category
    final Map<String, List<models.Product>> categorizedMenu = {};

    for (final product in menu) {
      final category = product.categoryName ?? 'Other';
      if (!categorizedMenu.containsKey(category)) {
        categorizedMenu[category] = [];
      }
      categorizedMenu[category]!.add(product);
    }

    return Column(
      children: categorizedMenu.entries.map((entry) {
        return _buildMenuCategory(context, entry.key, entry.value);
      }).toList(),
    );
  }

  Widget _buildMenuCategory(BuildContext context, String categoryName, List<models.Product> products) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
          child: Row(
            children: [
              // Category icon based on name
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: primaryColor.withAlpha(15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(categoryName),
                  size: 18,
                  color: primaryColor,
                ),
              ),
              const SizedBox(width: 12),

              // Category name
              Expanded(
                child: Text(
                  categoryName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Alx',
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Item count badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: primaryColor.withAlpha(20),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${products.length} ${products.length == 1 ? 'صنف' : 'أصناف'}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Products horizontal list
        SizedBox(
          height: 190, // Reduced height for smaller cards
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: products.length,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            itemBuilder: (context, index) {
              return _buildProductCard(context, products[index]);
            },
          ),
        ),

        // Divider between categories
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Divider(
            color: Colors.grey.withAlpha(40),
            height: 1,
          ),
        ),
      ],
    );
  }

  // Helper method to get appropriate icon for category
  IconData _getCategoryIcon(String categoryName) {
    final lowerCategory = categoryName.toLowerCase();

    if (lowerCategory.contains('سندويتش') || lowerCategory.contains('sandwich')) {
      return Icons.lunch_dining;
    } else if (lowerCategory.contains('بيتزا') || lowerCategory.contains('pizza')) {
      return Icons.local_pizza;
    } else if (lowerCategory.contains('مشروب') || lowerCategory.contains('drink')) {
      return Icons.local_drink;
    } else if (lowerCategory.contains('حلو') || lowerCategory.contains('dessert')) {
      return Icons.cake;
    } else if (lowerCategory.contains('سلطة') || lowerCategory.contains('salad')) {
      return Icons.spa;
    } else if (lowerCategory.contains('مقبلات') || lowerCategory.contains('appetizer')) {
      return Icons.tapas;
    } else {
      return Icons.restaurant;
    }
  }

  Widget _buildProductCard(BuildContext context, models.Product product) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return GestureDetector(
      onTap: () {
        // Navigate to product detail screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(
              product: product,
              restaurantId: widget.restaurantId,
            ),
          ),
        );
      },
      child: Container(
        width: 140, // Further reduced width for smaller cards
        margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 12,
              offset: const Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.08),
            width: 0.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced Product Image Section
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      child: product.image != null
                          ? FadeInImage.assetNetwork(
                              placeholder: 'assets/images/loading.gif',
                              image: _getImageUrl(product.image!),
                              fit: BoxFit.cover,
                              fadeInDuration: const Duration(milliseconds: 200),
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.grey[100]!,
                                        Colors.grey[200]!,
                                      ],
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.restaurant_menu,
                                    size: 32,
                                    color: Colors.grey[400],
                                  ),
                                );
                              },
                            )
                          : Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.grey[100]!,
                                    Colors.grey[200]!,
                                  ],
                                ),
                              ),
                              child: Icon(
                                Icons.restaurant_menu,
                                size: 32,
                                color: Colors.grey[400],
                              ),
                            ),
                    ),
                  ),

                  // Enhanced availability status overlay
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: product.available
                            ? Colors.green.withOpacity(0.9)
                            : Colors.red.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: (product.available ? Colors.green : Colors.red).withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            product.available ? Icons.check_circle : Icons.cancel,
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            product.available ? 'متوفر' : 'غير متوفر',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Gradient overlay for better text readability
                  if (!product.available)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Enhanced Product Details Section
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Product name with better styling
                    Text(
                      product.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 13,
                        color: product.available ? const Color(0xFF1F2937) : Colors.grey[600],
                        height: 1.2,
                        letterSpacing: -0.1,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Price with enhanced styling
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${product.price.toStringAsFixed(0)} د.ل',
                          style: TextStyle(
                            fontSize: 15,
                            color: product.available ? primaryColor : Colors.grey[500],
                            fontWeight: FontWeight.w800,
                            letterSpacing: 0.2,
                          ),
                        ),
                        if (product.available)
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Icons.add,
                              size: 14,
                              color: primaryColor,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

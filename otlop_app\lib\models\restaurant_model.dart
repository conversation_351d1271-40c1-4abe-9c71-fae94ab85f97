import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/services/api_service.dart';

class Restaurant {
  final int id;
  final String name;
  final String? description;
  final String address;
  final String city;
  final String? firstNumber;
  final String? secondNumber;
  final String? image;
  final double? rating;
  final int? reviewCount;
  final String? deliveryTime;
  final double? deliveryFee;
  final double? minOrderAmount;
  final double? latitude;
  final double? longitude;
  final int? restaurantTypeId;
  final String? restaurantTypeName;
  final List<String>? cuisines;
  final List<Product>? featuredItems;
  final bool isFavorite;
  final bool isOpen;
  final int? status;
  final double? distance;
  final bool? activated;
  final String? owner;

  Restaurant({
    required this.id,
    required this.name,
    this.description,
    required this.address,
    required this.city,
    this.firstNumber,
    this.secondNumber,
    this.image,
    this.rating,
    this.reviewCount,
    this.deliveryTime,
    this.deliveryFee,
    this.minOrderAmount,
    this.latitude,
    this.longitude,
    this.restaurantTypeId,
    this.restaurantTypeName,
    this.cuisines,
    this.featuredItems,
    this.isFavorite = false,
    this.isOpen = true,
    this.status,
    this.distance,
    this.activated,
    this.owner,
  });

  // Helper method to format image URLs
  static String? _formatImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return null; // Return null for empty images, let UI handle fallback
    }

    // If it's already a full URL, return it as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    List<String>? cuisinesList;
    if (json['cuisines'] != null) {
      cuisinesList = List<String>.from(json['cuisines']);
    }

    List<Product>? featuredItemsList;
    if (json['featured_items'] != null) {
      featuredItemsList = (json['featured_items'] as List)
          .map((item) => Product.fromJson(item))
          .toList();
    }

    return Restaurant(
      id: json['RestaurantID'] ?? json['id'] ?? 0,
      name: json['Name'] ?? json['name'] ?? '',
      description: json['description'],
      address: json['Address'] ?? json['address'] ?? '',
      city: json['City'] ?? json['city'] ?? '',
      firstNumber: json['firstNumber'],
      secondNumber: json['secondNumber'],
      image: _formatImageUrl(json['Image'] ?? json['image']),
      rating: json['rating'] != null ? double.parse(json['rating'].toString()) : null,
      reviewCount: json['reviewCount'] != null ? int.parse(json['reviewCount'].toString()) : 0,
      deliveryTime: json['delivery_time'] ?? '30-45 min',
      deliveryFee: json['DeliveryFee'] != null ? double.parse(json['DeliveryFee'].toString()) :
                   (json['delivery_fee'] != null ? double.parse(json['delivery_fee'].toString()) : 0.0),
      minOrderAmount: json['min_order_amount'] != null ? double.parse(json['min_order_amount'].toString()) : 0.0,
      latitude: json['latitude'] != null ? double.parse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.parse(json['longitude'].toString()) : null,
      restaurantTypeId: json['RestaurantTypeID'],
      // Fix the restaurant type name mapping to match backend response
      restaurantTypeName: json['TypeName'] ?? json['RestaurantType']?['TypeName'] ?? json['restaurantTypeName'],
      cuisines: cuisinesList,
      featuredItems: featuredItemsList,
      isFavorite: json['is_favorite'] == true || json['is_favorite'] == 1,
      // Fix the isOpen status based on backend response
      isOpen: !(json['IsClosed'] == true) && (json['IsOperational'] == true || json['Status'] == 1),
      status: json['Status'],
      distance: json['Distance'] != null ? double.parse(json['Distance'].toString()) :
                (json['distance'] != null ? double.parse(json['distance'].toString()) : null),
      activated: json['Activated'] == 1 || json['Activated'] == true,
      owner: json['Owner'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'RestaurantID': id,
      'Name': name,
      'description': description,
      'Address': address,
      'City': city,
      'firstNumber': firstNumber,
      'secondNumber': secondNumber,
      'Image': image,
      'rating': rating,
      'reviewCount': reviewCount,
      'delivery_time': deliveryTime,
      'delivery_fee': deliveryFee,
      'min_order_amount': minOrderAmount,
      'latitude': latitude,
      'longitude': longitude,
      'RestaurantTypeID': restaurantTypeId,
      'restaurantTypeName': restaurantTypeName,
      'cuisines': cuisines,
      'featured_items': featuredItems?.map((item) => item.toJson()).toList(),
      'is_favorite': isFavorite,
      'Status': status,
      'distance': distance,
      'Activated': activated,
      'Owner': owner,
    };
  }
}

class Category {
  final int id;
  final String name;
  final String? image;

  Category({
    required this.id,
    required this.name,
    this.image,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    // Format the image URL
    String? imageUrl;
    if (json['image'] != null) {
      if (json['image'].toString().startsWith('http')) {
        imageUrl = json['image'];
      } else {
        imageUrl = 'http://192.168.220.194:3001/uploads/categories/${json['image']}';
      }
    }

    return Category(
      id: json['id'],
      name: json['name'],
      image: imageUrl,
    );
  }
}

class WorkingTime {
  final int id;
  final int dayOfWeek;
  final String openTime;
  final String closeTime;
  final bool isClosed;

  WorkingTime({
    required this.id,
    required this.dayOfWeek,
    required this.openTime,
    required this.closeTime,
    required this.isClosed,
  });

  factory WorkingTime.fromJson(Map<String, dynamic> json) {
    return WorkingTime(
      id: json['id'],
      dayOfWeek: json['dayOfWeek'],
      openTime: json['openTime'],
      closeTime: json['closeTime'],
      isClosed: json['isClosed'] ?? false,
    );
  }
}
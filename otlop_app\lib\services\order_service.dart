import 'package:otlop_app/services/api_service.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

class OrderService {
  final ApiService _apiService;

  // Constructor
  OrderService({ApiService? apiService}) : _apiService = apiService ?? ApiService();

  // Calculate delivery fee based on distance
  Future<double> calculateDeliveryFee({
    required double restaurantLat,
    required double restaurantLng,
    required double customerLat,
    required double customerLng,
  }) async {
    try {
      final response = await _apiService.post('orders/calculate-delivery-fee', data: {
        'restaurantLatitude': restaurantLat,
        'restaurantLongitude': restaurantLng,
        'customerLatitude': customerLat,
        'customerLongitude': customerLng,
      });

      if (response is Map<String, dynamic> && response['success'] == true) {
        return (response['deliveryFee'] as num?)?.toDouble() ?? 5.0;
      } else {
        print('Failed to calculate delivery fee: ${response['message'] ?? 'Unknown error'}');
        return 5.0; // Fallback
      }
    } catch (e) {
      print('Error calculating delivery fee: $e');
      return 5.0; // Fallback
    }
  }

  // Get customer orders
  Future<List<dynamic>> getCustomerOrders({BuildContext? context}) async {
    try {
      // Get customer ID from auth provider if context is provided
      String? customerId;
      if (context != null) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        customerId = authProvider.user?.id;
        print('Getting orders for customer ID: $customerId');
      }

      String endpoint = 'orders/all';
      if (customerId != null && customerId.isNotEmpty) {
        endpoint = 'orders/customer/$customerId';
        print('Using customer-specific endpoint: $endpoint');
      } else {
        print('No customer ID found, using general endpoint: $endpoint');
      }

      print('Making API call to: $endpoint');
      final response = await _apiService.get(endpoint);
      print('API response: $response');

      final orders = response['items'] ?? response['rows'] ?? [];
      print('Extracted orders: ${orders.length} orders found');
      return orders;
    } catch (e) {
      print('Error loading orders: ${e.toString()}');
      throw Exception('Failed to load orders: ${e.toString()}');
    }
  }

  // Get order details by ID
  Future<Map<String, dynamic>> getOrderById(String orderId) async {
    try {
      final response = await _apiService.get('orders/details/$orderId');
      if (response is Map<String, dynamic>) {
        return response;
      }
      return {};
    } catch (e) {
      throw Exception('Failed to load order details: ${e.toString()}');
    }
  }

  // Get order cart details
  Future<Map<String, dynamic>> getOrderCart(String orderId) async {
    try {
      final response = await _apiService.get('orders/cart/$orderId');
      if (response is Map<String, dynamic>) {
        return response;
      }
      return {};
    } catch (e) {
      throw Exception('Failed to load order cart: ${e.toString()}');
    }
  }

  // Create a new order
  Future<String> createOrder(Map<String, dynamic> orderData) async {
    try {
      print('Creating order with data: ${orderData.toString()}');
      final response = await _apiService.post('orders/create', data: orderData);

      // Handle different response formats
      if (response is Map<String, dynamic>) {
        if (response['success'] == true) {
          final orderId = response['OrderID']?.toString() ??
                         response['data']?['orderId']?.toString() ?? '';
          print('Order created successfully with ID: $orderId');
          return orderId;
        } else {
          final errorMessage = response['message'] ?? 'Unknown error occurred';
          throw Exception('Order creation failed: $errorMessage');
        }
      }

      // Fallback for direct OrderID response
      return response['OrderID']?.toString() ?? '';
    } catch (e) {
      print('Error creating order: ${e.toString()}');

      // Parse different types of errors for better user feedback
      final errorString = e.toString().toLowerCase();

      // Check for specific backend error messages first
      if (errorString.contains('customer id') && errorString.contains('required')) {
        throw Exception('معرف العميل مطلوب. يرجى تسجيل الدخول مرة أخرى.');
      } else if (errorString.contains('cart is empty') || errorString.contains('items are required')) {
        throw Exception('السلة فارغة. يرجى إضافة منتجات إلى السلة أولاً.');
      } else if (errorString.contains('restaurant not found')) {
        throw Exception('المطعم غير موجود. يرجى اختيار مطعم آخر.');
      } else if (errorString.contains('product not found')) {
        throw Exception('المنتج غير موجود. يرجى تحديث السلة.');
      } else if (errorString.contains('invalid product id')) {
        throw Exception('معرف المنتج غير صحيح. يرجى تحديث السلة.');
      } else if (errorString.contains('invalid quantity')) {
        throw Exception('الكمية غير صحيحة. يرجى التحقق من الكمية.');
      } else if (errorString.contains('400') || errorString.contains('bad request')) {
        throw Exception('بيانات الطلب غير صحيحة. يرجى التحقق من السلة والمحاولة مرة أخرى.');
      } else if (errorString.contains('401') || errorString.contains('unauthorized')) {
        throw Exception('فشل في المصادقة. يرجى تسجيل الدخول مرة أخرى.');
      } else if (errorString.contains('403') || errorString.contains('forbidden')) {
        throw Exception('تم رفض الوصول. يرجى التحقق من الصلاحيات.');
      } else if (errorString.contains('404') || errorString.contains('not found')) {
        throw Exception('خدمة الطلبات غير موجودة. يرجى الاتصال بالدعم.');
      } else if (errorString.contains('500') || errorString.contains('internal server')) {
        throw Exception('خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.');
      } else if (errorString.contains('network') || errorString.contains('connection')) {
        throw Exception('خطأ في الشبكة. تحقق من اتصال الإنترنت.');
      } else if (errorString.contains('timeout')) {
        throw Exception('انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.');
      }

      // For any other error, include the original message for debugging
      throw Exception('فشل في إنشاء الطلب: ${e.toString()}');
    }
  }

  // Cancel an order
  Future<void> cancelOrder(String orderId) async {
    try {
      // Update order status to cancelled (6)
      await _apiService.put('orders/$orderId', {
        'Status': 6
      });
    } catch (e) {
      throw Exception('Failed to cancel order: ${e.toString()}');
    }
  }

  // Add item to cart
  Future<Map<String, dynamic>> addToCart(Map<String, dynamic> cartItem) async {
    try {
      final response = await _apiService.post('cart', data: cartItem);
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to add item to cart: ${e.toString()}');
    }
  }

  // Get cart items
  Future<List<dynamic>> getCartItems() async {
    try {
      final response = await _apiService.get('cart');
      return response['items'] ?? [];
    } catch (e) {
      throw Exception('Failed to fetch cart items: ${e.toString()}');
    }
  }

  // Update cart item quantity
  Future<Map<String, dynamic>> updateCartItemQuantity(String cartItemId, int quantity) async {
    try {
      final response = await _apiService.put('cart/$cartItemId', {
        'Quantity': quantity,
      });
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to update cart item: ${e.toString()}');
    }
  }

  // Remove cart item
  Future<void> removeCartItem(String cartItemId) async {
    try {
      await _apiService.delete('cart/$cartItemId');
    } catch (e) {
      throw Exception('Failed to remove cart item: ${e.toString()}');
    }
  }

  // Checkout from cart
  Future<Map<String, dynamic>> checkout(Map<String, dynamic> checkoutData) async {
    try {
      final response = await _apiService.post('checkout', data: checkoutData);
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to checkout: ${e.toString()}');
    }
  }

  // Get restaurant orders
  Future<List<dynamic>> getRestaurantOrders(String restaurantId) async {
    try {
      final response = await _apiService.get('orders/restaurant/$restaurantId');
      if (response is Map<String, dynamic> && response.containsKey('items')) {
        return response['items'] as List<dynamic>;
      } else if (response is List<dynamic>) {
        return response;
      }
      return [];
    } catch (e) {
      throw Exception('Failed to load restaurant orders: ${e.toString()}');
    }
  }

  // Get driver orders
  Future<List<dynamic>> getDriverOrders(String driverId) async {
    try {
      final response = await _apiService.get('orders/drivers/$driverId');
      if (response is Map<String, dynamic> && response.containsKey('items')) {
        return response['items'] as List<dynamic>;
      } else if (response is List<dynamic>) {
        return response;
      }
      return [];
    } catch (e) {
      throw Exception('Failed to load driver orders: ${e.toString()}');
    }
  }
}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/simplebar-core";
exports.ids = ["vendor-chunks/simplebar-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/simplebar-core/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/simplebar-core/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleBarCore)\n/* harmony export */ });\n/* harmony import */ var lodash_debounce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/debounce.js */ \"(ssr)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_throttle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/throttle.js */ \"(ssr)/./node_modules/lodash/throttle.js\");\n/**\n * simplebar-core - v1.3.0\n * Scrollbars, simpler.\n * https://grsmto.github.io/simplebar/\n *\n * Made by Adrien Denat from a fork by Jonathan Nicol\n * Under MIT License\n */\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction getElementWindow$1(element) {\n    if (!element ||\n        !element.ownerDocument ||\n        !element.ownerDocument.defaultView) {\n        return window;\n    }\n    return element.ownerDocument.defaultView;\n}\nfunction getElementDocument$1(element) {\n    if (!element || !element.ownerDocument) {\n        return document;\n    }\n    return element.ownerDocument;\n}\n// Helper function to retrieve options from element attributes\nvar getOptions$1 = function (obj) {\n    var initialObj = {};\n    var options = Array.prototype.reduce.call(obj, function (acc, attribute) {\n        var option = attribute.name.match(/data-simplebar-(.+)/);\n        if (option) {\n            var key = option[1].replace(/\\W+(.)/g, function (_, chr) { return chr.toUpperCase(); });\n            switch (attribute.value) {\n                case 'true':\n                    acc[key] = true;\n                    break;\n                case 'false':\n                    acc[key] = false;\n                    break;\n                case undefined:\n                    acc[key] = true;\n                    break;\n                default:\n                    acc[key] = attribute.value;\n            }\n        }\n        return acc;\n    }, initialObj);\n    return options;\n};\nfunction addClasses$1(el, classes) {\n    var _a;\n    if (!el)\n        return;\n    (_a = el.classList).add.apply(_a, classes.split(' '));\n}\nfunction removeClasses$1(el, classes) {\n    if (!el)\n        return;\n    classes.split(' ').forEach(function (className) {\n        el.classList.remove(className);\n    });\n}\nfunction classNamesToQuery$1(classNames) {\n    return \".\".concat(classNames.split(' ').join('.'));\n}\nvar canUseDOM = !!(typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement);\n\nvar helpers = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    addClasses: addClasses$1,\n    canUseDOM: canUseDOM,\n    classNamesToQuery: classNamesToQuery$1,\n    getElementDocument: getElementDocument$1,\n    getElementWindow: getElementWindow$1,\n    getOptions: getOptions$1,\n    removeClasses: removeClasses$1\n});\n\nvar cachedScrollbarWidth = null;\nvar cachedDevicePixelRatio = null;\nif (canUseDOM) {\n    window.addEventListener('resize', function () {\n        if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n            cachedDevicePixelRatio = window.devicePixelRatio;\n            cachedScrollbarWidth = null;\n        }\n    });\n}\nfunction scrollbarWidth() {\n    if (cachedScrollbarWidth === null) {\n        if (typeof document === 'undefined') {\n            cachedScrollbarWidth = 0;\n            return cachedScrollbarWidth;\n        }\n        var body = document.body;\n        var box = document.createElement('div');\n        box.classList.add('simplebar-hide-scrollbar');\n        body.appendChild(box);\n        var width = box.getBoundingClientRect().right;\n        body.removeChild(box);\n        cachedScrollbarWidth = width;\n    }\n    return cachedScrollbarWidth;\n}\n\nvar getElementWindow = getElementWindow$1, getElementDocument = getElementDocument$1, getOptions = getOptions$1, addClasses = addClasses$1, removeClasses = removeClasses$1, classNamesToQuery = classNamesToQuery$1;\nvar SimpleBarCore = /** @class */ (function () {\n    function SimpleBarCore(element, options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        this.removePreventClickId = null;\n        this.minScrollbarWidth = 20;\n        this.stopScrollDelay = 175;\n        this.isScrolling = false;\n        this.isMouseEntering = false;\n        this.isDragging = false;\n        this.scrollXTicking = false;\n        this.scrollYTicking = false;\n        this.wrapperEl = null;\n        this.contentWrapperEl = null;\n        this.contentEl = null;\n        this.offsetEl = null;\n        this.maskEl = null;\n        this.placeholderEl = null;\n        this.heightAutoObserverWrapperEl = null;\n        this.heightAutoObserverEl = null;\n        this.rtlHelpers = null;\n        this.scrollbarWidth = 0;\n        this.resizeObserver = null;\n        this.mutationObserver = null;\n        this.elStyles = null;\n        this.isRtl = null;\n        this.mouseX = 0;\n        this.mouseY = 0;\n        this.onMouseMove = function () { };\n        this.onWindowResize = function () { };\n        this.onStopScrolling = function () { };\n        this.onMouseEntered = function () { };\n        /**\n         * On scroll event handling\n         */\n        this.onScroll = function () {\n            var elWindow = getElementWindow(_this.el);\n            if (!_this.scrollXTicking) {\n                elWindow.requestAnimationFrame(_this.scrollX);\n                _this.scrollXTicking = true;\n            }\n            if (!_this.scrollYTicking) {\n                elWindow.requestAnimationFrame(_this.scrollY);\n                _this.scrollYTicking = true;\n            }\n            if (!_this.isScrolling) {\n                _this.isScrolling = true;\n                addClasses(_this.el, _this.classNames.scrolling);\n            }\n            _this.showScrollbar('x');\n            _this.showScrollbar('y');\n            _this.onStopScrolling();\n        };\n        this.scrollX = function () {\n            if (_this.axis.x.isOverflowing) {\n                _this.positionScrollbar('x');\n            }\n            _this.scrollXTicking = false;\n        };\n        this.scrollY = function () {\n            if (_this.axis.y.isOverflowing) {\n                _this.positionScrollbar('y');\n            }\n            _this.scrollYTicking = false;\n        };\n        this._onStopScrolling = function () {\n            removeClasses(_this.el, _this.classNames.scrolling);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isScrolling = false;\n        };\n        this.onMouseEnter = function () {\n            if (!_this.isMouseEntering) {\n                addClasses(_this.el, _this.classNames.mouseEntered);\n                _this.showScrollbar('x');\n                _this.showScrollbar('y');\n                _this.isMouseEntering = true;\n            }\n            _this.onMouseEntered();\n        };\n        this._onMouseEntered = function () {\n            removeClasses(_this.el, _this.classNames.mouseEntered);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isMouseEntering = false;\n        };\n        this._onMouseMove = function (e) {\n            _this.mouseX = e.clientX;\n            _this.mouseY = e.clientY;\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseMoveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseMoveForAxis('y');\n            }\n        };\n        this.onMouseLeave = function () {\n            _this.onMouseMove.cancel();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseLeaveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseLeaveForAxis('y');\n            }\n            _this.mouseX = -1;\n            _this.mouseY = -1;\n        };\n        this._onWindowResize = function () {\n            // Recalculate scrollbarWidth in case it's a zoom\n            _this.scrollbarWidth = _this.getScrollbarWidth();\n            _this.hideNativeScrollbar();\n        };\n        this.onPointerEvent = function (e) {\n            if (!_this.axis.x.track.el ||\n                !_this.axis.y.track.el ||\n                !_this.axis.x.scrollbar.el ||\n                !_this.axis.y.scrollbar.el)\n                return;\n            var isWithinTrackXBounds, isWithinTrackYBounds;\n            _this.axis.x.track.rect = _this.axis.x.track.el.getBoundingClientRect();\n            _this.axis.y.track.rect = _this.axis.y.track.el.getBoundingClientRect();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                isWithinTrackXBounds = _this.isWithinBounds(_this.axis.x.track.rect);\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                isWithinTrackYBounds = _this.isWithinBounds(_this.axis.y.track.rect);\n            }\n            // If any pointer event is called on the scrollbar\n            if (isWithinTrackXBounds || isWithinTrackYBounds) {\n                // Prevent event leaking\n                e.stopPropagation();\n                if (e.type === 'pointerdown' && e.pointerType !== 'touch') {\n                    if (isWithinTrackXBounds) {\n                        _this.axis.x.scrollbar.rect =\n                            _this.axis.x.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.x.scrollbar.rect)) {\n                            _this.onDragStart(e, 'x');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'x');\n                        }\n                    }\n                    if (isWithinTrackYBounds) {\n                        _this.axis.y.scrollbar.rect =\n                            _this.axis.y.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.y.scrollbar.rect)) {\n                            _this.onDragStart(e, 'y');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'y');\n                        }\n                    }\n                }\n            }\n        };\n        /**\n         * Drag scrollbar handle\n         */\n        this.drag = function (e) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n            if (!_this.draggedAxis || !_this.contentWrapperEl)\n                return;\n            var eventOffset;\n            var track = _this.axis[_this.draggedAxis].track;\n            var trackSize = (_b = (_a = track.rect) === null || _a === void 0 ? void 0 : _a[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _b !== void 0 ? _b : 0;\n            var scrollbar = _this.axis[_this.draggedAxis].scrollbar;\n            var contentSize = (_d = (_c = _this.contentWrapperEl) === null || _c === void 0 ? void 0 : _c[_this.axis[_this.draggedAxis].scrollSizeAttr]) !== null && _d !== void 0 ? _d : 0;\n            var hostSize = parseInt((_f = (_e = _this.elStyles) === null || _e === void 0 ? void 0 : _e[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _f !== void 0 ? _f : '0px', 10);\n            e.preventDefault();\n            e.stopPropagation();\n            if (_this.draggedAxis === 'y') {\n                eventOffset = e.pageY;\n            }\n            else {\n                eventOffset = e.pageX;\n            }\n            // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n            var dragPos = eventOffset -\n                ((_h = (_g = track.rect) === null || _g === void 0 ? void 0 : _g[_this.axis[_this.draggedAxis].offsetAttr]) !== null && _h !== void 0 ? _h : 0) -\n                _this.axis[_this.draggedAxis].dragOffset;\n            dragPos =\n                _this.draggedAxis === 'x' && _this.isRtl\n                    ? ((_k = (_j = track.rect) === null || _j === void 0 ? void 0 : _j[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _k !== void 0 ? _k : 0) -\n                        scrollbar.size -\n                        dragPos\n                    : dragPos;\n            // Convert the mouse position into a percentage of the scrollbar height/width.\n            var dragPerc = dragPos / (trackSize - scrollbar.size);\n            // Scroll the content by the same percentage.\n            var scrollPos = dragPerc * (contentSize - hostSize);\n            // Fix browsers inconsistency on RTL\n            if (_this.draggedAxis === 'x' && _this.isRtl) {\n                scrollPos = ((_l = SimpleBarCore.getRtlHelpers()) === null || _l === void 0 ? void 0 : _l.isScrollingToNegative)\n                    ? -scrollPos\n                    : scrollPos;\n            }\n            _this.contentWrapperEl[_this.axis[_this.draggedAxis].scrollOffsetAttr] =\n                scrollPos;\n        };\n        /**\n         * End scroll handle drag\n         */\n        this.onEndDrag = function (e) {\n            _this.isDragging = false;\n            var elDocument = getElementDocument(_this.el);\n            var elWindow = getElementWindow(_this.el);\n            e.preventDefault();\n            e.stopPropagation();\n            removeClasses(_this.el, _this.classNames.dragging);\n            _this.onStopScrolling();\n            elDocument.removeEventListener('mousemove', _this.drag, true);\n            elDocument.removeEventListener('mouseup', _this.onEndDrag, true);\n            _this.removePreventClickId = elWindow.setTimeout(function () {\n                // Remove these asynchronously so we still suppress click events\n                // generated simultaneously with mouseup.\n                elDocument.removeEventListener('click', _this.preventClick, true);\n                elDocument.removeEventListener('dblclick', _this.preventClick, true);\n                _this.removePreventClickId = null;\n            });\n        };\n        /**\n         * Handler to ignore click events during drag\n         */\n        this.preventClick = function (e) {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n        this.el = element;\n        this.options = __assign(__assign({}, SimpleBarCore.defaultOptions), options);\n        this.classNames = __assign(__assign({}, SimpleBarCore.defaultOptions.classNames), options.classNames);\n        this.axis = {\n            x: {\n                scrollOffsetAttr: 'scrollLeft',\n                sizeAttr: 'width',\n                scrollSizeAttr: 'scrollWidth',\n                offsetSizeAttr: 'offsetWidth',\n                offsetAttr: 'left',\n                overflowAttr: 'overflowX',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            },\n            y: {\n                scrollOffsetAttr: 'scrollTop',\n                sizeAttr: 'height',\n                scrollSizeAttr: 'scrollHeight',\n                offsetSizeAttr: 'offsetHeight',\n                offsetAttr: 'top',\n                overflowAttr: 'overflowY',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            }\n        };\n        if (typeof this.el !== 'object' || !this.el.nodeName) {\n            throw new Error(\"Argument passed to SimpleBar must be an HTML element instead of \".concat(this.el));\n        }\n        this.onMouseMove = lodash_throttle_js__WEBPACK_IMPORTED_MODULE_1__(this._onMouseMove, 64);\n        this.onWindowResize = lodash_debounce_js__WEBPACK_IMPORTED_MODULE_0__(this._onWindowResize, 64, { leading: true });\n        this.onStopScrolling = lodash_debounce_js__WEBPACK_IMPORTED_MODULE_0__(this._onStopScrolling, this.stopScrollDelay);\n        this.onMouseEntered = lodash_debounce_js__WEBPACK_IMPORTED_MODULE_0__(this._onMouseEntered, this.stopScrollDelay);\n        this.init();\n    }\n    /**\n     * Helper to fix browsers inconsistency on RTL:\n     *  - Firefox inverts the scrollbar initial position\n     *  - IE11 inverts both scrollbar position and scrolling offset\n     * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n     */\n    SimpleBarCore.getRtlHelpers = function () {\n        if (SimpleBarCore.rtlHelpers) {\n            return SimpleBarCore.rtlHelpers;\n        }\n        var dummyDiv = document.createElement('div');\n        dummyDiv.innerHTML =\n            '<div class=\"simplebar-dummy-scrollbar-size\"><div></div></div>';\n        var scrollbarDummyEl = dummyDiv.firstElementChild;\n        var dummyChild = scrollbarDummyEl === null || scrollbarDummyEl === void 0 ? void 0 : scrollbarDummyEl.firstElementChild;\n        if (!dummyChild)\n            return null;\n        document.body.appendChild(scrollbarDummyEl);\n        scrollbarDummyEl.scrollLeft = 0;\n        var dummyContainerOffset = SimpleBarCore.getOffset(scrollbarDummyEl);\n        var dummyChildOffset = SimpleBarCore.getOffset(dummyChild);\n        scrollbarDummyEl.scrollLeft = -999;\n        var dummyChildOffsetAfterScroll = SimpleBarCore.getOffset(dummyChild);\n        document.body.removeChild(scrollbarDummyEl);\n        SimpleBarCore.rtlHelpers = {\n            // determines if the scrolling is responding with negative values\n            isScrollOriginAtZero: dummyContainerOffset.left !== dummyChildOffset.left,\n            // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n            isScrollingToNegative: dummyChildOffset.left !== dummyChildOffsetAfterScroll.left\n        };\n        return SimpleBarCore.rtlHelpers;\n    };\n    SimpleBarCore.prototype.getScrollbarWidth = function () {\n        // Try/catch for FF 56 throwing on undefined computedStyles\n        try {\n            // Detect browsers supporting CSS scrollbar styling and do not calculate\n            if ((this.contentWrapperEl &&\n                getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n                    .display === 'none') ||\n                'scrollbarWidth' in document.documentElement.style ||\n                '-ms-overflow-style' in document.documentElement.style) {\n                return 0;\n            }\n            else {\n                return scrollbarWidth();\n            }\n        }\n        catch (e) {\n            return scrollbarWidth();\n        }\n    };\n    SimpleBarCore.getOffset = function (el) {\n        var rect = el.getBoundingClientRect();\n        var elDocument = getElementDocument(el);\n        var elWindow = getElementWindow(el);\n        return {\n            top: rect.top +\n                (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n            left: rect.left +\n                (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)\n        };\n    };\n    SimpleBarCore.prototype.init = function () {\n        // We stop here on server-side\n        if (canUseDOM) {\n            this.initDOM();\n            this.rtlHelpers = SimpleBarCore.getRtlHelpers();\n            this.scrollbarWidth = this.getScrollbarWidth();\n            this.recalculate();\n            this.initListeners();\n        }\n    };\n    SimpleBarCore.prototype.initDOM = function () {\n        var _a, _b;\n        // assume that element has his DOM already initiated\n        this.wrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.wrapper));\n        this.contentWrapperEl =\n            this.options.scrollableNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentWrapper));\n        this.contentEl =\n            this.options.contentNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentEl));\n        this.offsetEl = this.el.querySelector(classNamesToQuery(this.classNames.offset));\n        this.maskEl = this.el.querySelector(classNamesToQuery(this.classNames.mask));\n        this.placeholderEl = this.findChild(this.wrapperEl, classNamesToQuery(this.classNames.placeholder));\n        this.heightAutoObserverWrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverWrapperEl));\n        this.heightAutoObserverEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverEl));\n        this.axis.x.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.horizontal)));\n        this.axis.y.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.vertical)));\n        this.axis.x.scrollbar.el =\n            ((_a = this.axis.x.track.el) === null || _a === void 0 ? void 0 : _a.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        this.axis.y.scrollbar.el =\n            ((_b = this.axis.y.track.el) === null || _b === void 0 ? void 0 : _b.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        if (!this.options.autoHide) {\n            addClasses(this.axis.x.scrollbar.el, this.classNames.visible);\n            addClasses(this.axis.y.scrollbar.el, this.classNames.visible);\n        }\n    };\n    SimpleBarCore.prototype.initListeners = function () {\n        var _this = this;\n        var _a;\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.addEventListener('mouseenter', this.onMouseEnter);\n        this.el.addEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.addEventListener('mousemove', this.onMouseMove);\n        this.el.addEventListener('mouseleave', this.onMouseLeave);\n        (_a = this.contentWrapperEl) === null || _a === void 0 ? void 0 : _a.addEventListener('scroll', this.onScroll);\n        // Browser zoom triggers a window resize\n        elWindow.addEventListener('resize', this.onWindowResize);\n        if (!this.contentEl)\n            return;\n        if (window.ResizeObserver) {\n            // Hack for https://github.com/WICG/ResizeObserver/issues/38\n            var resizeObserverStarted_1 = false;\n            var resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n            this.resizeObserver = new resizeObserver(function () {\n                if (!resizeObserverStarted_1)\n                    return;\n                elWindow.requestAnimationFrame(function () {\n                    _this.recalculate();\n                });\n            });\n            this.resizeObserver.observe(this.el);\n            this.resizeObserver.observe(this.contentEl);\n            elWindow.requestAnimationFrame(function () {\n                resizeObserverStarted_1 = true;\n            });\n        }\n        // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n        this.mutationObserver = new elWindow.MutationObserver(function () {\n            elWindow.requestAnimationFrame(function () {\n                _this.recalculate();\n            });\n        });\n        this.mutationObserver.observe(this.contentEl, {\n            childList: true,\n            subtree: true,\n            characterData: true\n        });\n    };\n    SimpleBarCore.prototype.recalculate = function () {\n        if (!this.heightAutoObserverEl ||\n            !this.contentEl ||\n            !this.contentWrapperEl ||\n            !this.wrapperEl ||\n            !this.placeholderEl)\n            return;\n        var elWindow = getElementWindow(this.el);\n        this.elStyles = elWindow.getComputedStyle(this.el);\n        this.isRtl = this.elStyles.direction === 'rtl';\n        var contentElOffsetWidth = this.contentEl.offsetWidth;\n        var isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n        var isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1 || contentElOffsetWidth > 0;\n        var contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n        var elOverflowX = this.elStyles.overflowX;\n        var elOverflowY = this.elStyles.overflowY;\n        this.contentEl.style.padding = \"\".concat(this.elStyles.paddingTop, \" \").concat(this.elStyles.paddingRight, \" \").concat(this.elStyles.paddingBottom, \" \").concat(this.elStyles.paddingLeft);\n        this.wrapperEl.style.margin = \"-\".concat(this.elStyles.paddingTop, \" -\").concat(this.elStyles.paddingRight, \" -\").concat(this.elStyles.paddingBottom, \" -\").concat(this.elStyles.paddingLeft);\n        var contentElScrollHeight = this.contentEl.scrollHeight;\n        var contentElScrollWidth = this.contentEl.scrollWidth;\n        this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n        // Determine placeholder size\n        this.placeholderEl.style.width = isWidthAuto\n            ? \"\".concat(contentElOffsetWidth || contentElScrollWidth, \"px\")\n            : 'auto';\n        this.placeholderEl.style.height = \"\".concat(contentElScrollHeight, \"px\");\n        var contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n        this.axis.x.isOverflowing =\n            contentElOffsetWidth !== 0 && contentElScrollWidth > contentElOffsetWidth;\n        this.axis.y.isOverflowing =\n            contentElScrollHeight > contentWrapperElOffsetHeight;\n        // Set isOverflowing to false if user explicitely set hidden overflow\n        this.axis.x.isOverflowing =\n            elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n        this.axis.y.isOverflowing =\n            elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n        this.axis.x.forceVisible =\n            this.options.forceVisible === 'x' || this.options.forceVisible === true;\n        this.axis.y.forceVisible =\n            this.options.forceVisible === 'y' || this.options.forceVisible === true;\n        this.hideNativeScrollbar();\n        // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n        var offsetForXScrollbar = this.axis.x.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        var offsetForYScrollbar = this.axis.y.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        this.axis.x.isOverflowing =\n            this.axis.x.isOverflowing &&\n                contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n        this.axis.y.isOverflowing =\n            this.axis.y.isOverflowing &&\n                contentElScrollHeight >\n                    contentWrapperElOffsetHeight - offsetForXScrollbar;\n        this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n        this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n        if (this.axis.x.scrollbar.el)\n            this.axis.x.scrollbar.el.style.width = \"\".concat(this.axis.x.scrollbar.size, \"px\");\n        if (this.axis.y.scrollbar.el)\n            this.axis.y.scrollbar.el.style.height = \"\".concat(this.axis.y.scrollbar.size, \"px\");\n        this.positionScrollbar('x');\n        this.positionScrollbar('y');\n        this.toggleTrackVisibility('x');\n        this.toggleTrackVisibility('y');\n    };\n    /**\n     * Calculate scrollbar size\n     */\n    SimpleBarCore.prototype.getScrollbarSize = function (axis) {\n        var _a, _b;\n        if (axis === void 0) { axis = 'y'; }\n        if (!this.axis[axis].isOverflowing || !this.contentEl) {\n            return 0;\n        }\n        var contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = (_b = (_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) !== null && _b !== void 0 ? _b : 0;\n        var scrollbarRatio = trackSize / contentSize;\n        var scrollbarSize;\n        // Calculate new height/position of drag handle.\n        scrollbarSize = Math.max(~~(scrollbarRatio * trackSize), this.options.scrollbarMinSize);\n        if (this.options.scrollbarMaxSize) {\n            scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n        }\n        return scrollbarSize;\n    };\n    SimpleBarCore.prototype.positionScrollbar = function (axis) {\n        var _a, _b, _c;\n        if (axis === void 0) { axis = 'y'; }\n        var scrollbar = this.axis[axis].scrollbar;\n        if (!this.axis[axis].isOverflowing ||\n            !this.contentWrapperEl ||\n            !scrollbar.el ||\n            !this.elStyles) {\n            return;\n        }\n        var contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = ((_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) || 0;\n        var hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n        var scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        scrollOffset =\n            axis === 'x' &&\n                this.isRtl &&\n                ((_b = SimpleBarCore.getRtlHelpers()) === null || _b === void 0 ? void 0 : _b.isScrollOriginAtZero)\n                ? -scrollOffset\n                : scrollOffset;\n        if (axis === 'x' && this.isRtl) {\n            scrollOffset = ((_c = SimpleBarCore.getRtlHelpers()) === null || _c === void 0 ? void 0 : _c.isScrollingToNegative)\n                ? scrollOffset\n                : -scrollOffset;\n        }\n        var scrollPourcent = scrollOffset / (contentSize - hostSize);\n        var handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n        handleOffset =\n            axis === 'x' && this.isRtl\n                ? -handleOffset + (trackSize - scrollbar.size)\n                : handleOffset;\n        scrollbar.el.style.transform =\n            axis === 'x'\n                ? \"translate3d(\".concat(handleOffset, \"px, 0, 0)\")\n                : \"translate3d(0, \".concat(handleOffset, \"px, 0)\");\n    };\n    SimpleBarCore.prototype.toggleTrackVisibility = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var track = this.axis[axis].track.el;\n        var scrollbar = this.axis[axis].scrollbar.el;\n        if (!track || !scrollbar || !this.contentWrapperEl)\n            return;\n        if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n            track.style.visibility = 'visible';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n            this.el.classList.add(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        else {\n            track.style.visibility = 'hidden';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n            this.el.classList.remove(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        // Even if forceVisible is enabled, scrollbar itself should be hidden\n        if (this.axis[axis].isOverflowing) {\n            scrollbar.style.display = 'block';\n        }\n        else {\n            scrollbar.style.display = 'none';\n        }\n    };\n    SimpleBarCore.prototype.showScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.axis[axis].isOverflowing && !this.axis[axis].scrollbar.isVisible) {\n            addClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = true;\n        }\n    };\n    SimpleBarCore.prototype.hideScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.isDragging)\n            return;\n        if (this.axis[axis].isOverflowing && this.axis[axis].scrollbar.isVisible) {\n            removeClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = false;\n        }\n    };\n    SimpleBarCore.prototype.hideNativeScrollbar = function () {\n        if (!this.offsetEl)\n            return;\n        this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n            this.axis.y.isOverflowing || this.axis.y.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n        this.offsetEl.style.bottom =\n            this.axis.x.isOverflowing || this.axis.x.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n    };\n    SimpleBarCore.prototype.onMouseMoveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!currentAxis.track.el || !currentAxis.scrollbar.el)\n            return;\n        currentAxis.track.rect = currentAxis.track.el.getBoundingClientRect();\n        currentAxis.scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        if (this.isWithinBounds(currentAxis.track.rect)) {\n            this.showScrollbar(axis);\n            addClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.isWithinBounds(currentAxis.scrollbar.rect)) {\n                addClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n            else {\n                removeClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n        }\n        else {\n            removeClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.options.autoHide) {\n                this.hideScrollbar(axis);\n            }\n        }\n    };\n    SimpleBarCore.prototype.onMouseLeaveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        removeClasses(this.axis[axis].track.el, this.classNames.hover);\n        removeClasses(this.axis[axis].scrollbar.el, this.classNames.hover);\n        if (this.options.autoHide) {\n            this.hideScrollbar(axis);\n        }\n    };\n    /**\n     * on scrollbar handle drag movement starts\n     */\n    SimpleBarCore.prototype.onDragStart = function (e, axis) {\n        var _a;\n        if (axis === void 0) { axis = 'y'; }\n        this.isDragging = true;\n        var elDocument = getElementDocument(this.el);\n        var elWindow = getElementWindow(this.el);\n        var scrollbar = this.axis[axis].scrollbar;\n        // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n        var eventOffset = axis === 'y' ? e.pageY : e.pageX;\n        this.axis[axis].dragOffset =\n            eventOffset - (((_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) || 0);\n        this.draggedAxis = axis;\n        addClasses(this.el, this.classNames.dragging);\n        elDocument.addEventListener('mousemove', this.drag, true);\n        elDocument.addEventListener('mouseup', this.onEndDrag, true);\n        if (this.removePreventClickId === null) {\n            elDocument.addEventListener('click', this.preventClick, true);\n            elDocument.addEventListener('dblclick', this.preventClick, true);\n        }\n        else {\n            elWindow.clearTimeout(this.removePreventClickId);\n            this.removePreventClickId = null;\n        }\n    };\n    SimpleBarCore.prototype.onTrackClick = function (e, axis) {\n        var _this = this;\n        var _a, _b, _c, _d;\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!this.options.clickOnTrack ||\n            !currentAxis.scrollbar.el ||\n            !this.contentWrapperEl)\n            return;\n        // Preventing the event's default to trigger click underneath\n        e.preventDefault();\n        var elWindow = getElementWindow(this.el);\n        this.axis[axis].scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        var scrollbar = this.axis[axis].scrollbar;\n        var scrollbarOffset = (_b = (_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) !== null && _b !== void 0 ? _b : 0;\n        var hostSize = parseInt((_d = (_c = this.elStyles) === null || _c === void 0 ? void 0 : _c[this.axis[axis].sizeAttr]) !== null && _d !== void 0 ? _d : '0px', 10);\n        var scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        var t = axis === 'y'\n            ? this.mouseY - scrollbarOffset\n            : this.mouseX - scrollbarOffset;\n        var dir = t < 0 ? -1 : 1;\n        var scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n        var speed = 40;\n        var scrollTo = function () {\n            if (!_this.contentWrapperEl)\n                return;\n            if (dir === -1) {\n                if (scrolled > scrollSize) {\n                    scrolled -= speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n            else {\n                if (scrolled < scrollSize) {\n                    scrolled += speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n        };\n        scrollTo();\n    };\n    /**\n     * Getter for content element\n     */\n    SimpleBarCore.prototype.getContentElement = function () {\n        return this.contentEl;\n    };\n    /**\n     * Getter for original scrolling element\n     */\n    SimpleBarCore.prototype.getScrollElement = function () {\n        return this.contentWrapperEl;\n    };\n    SimpleBarCore.prototype.removeListeners = function () {\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.removeEventListener('mouseenter', this.onMouseEnter);\n        this.el.removeEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.removeEventListener('mousemove', this.onMouseMove);\n        this.el.removeEventListener('mouseleave', this.onMouseLeave);\n        if (this.contentWrapperEl) {\n            this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n        }\n        elWindow.removeEventListener('resize', this.onWindowResize);\n        if (this.mutationObserver) {\n            this.mutationObserver.disconnect();\n        }\n        if (this.resizeObserver) {\n            this.resizeObserver.disconnect();\n        }\n        // Cancel all debounced functions\n        this.onMouseMove.cancel();\n        this.onWindowResize.cancel();\n        this.onStopScrolling.cancel();\n        this.onMouseEntered.cancel();\n    };\n    /**\n     * Remove all listeners from DOM nodes\n     */\n    SimpleBarCore.prototype.unMount = function () {\n        this.removeListeners();\n    };\n    /**\n     * Check if mouse is within bounds\n     */\n    SimpleBarCore.prototype.isWithinBounds = function (bbox) {\n        return (this.mouseX >= bbox.left &&\n            this.mouseX <= bbox.left + bbox.width &&\n            this.mouseY >= bbox.top &&\n            this.mouseY <= bbox.top + bbox.height);\n    };\n    /**\n     * Find element children matches query\n     */\n    SimpleBarCore.prototype.findChild = function (el, query) {\n        var matches = el.matches ||\n            el.webkitMatchesSelector ||\n            el.mozMatchesSelector ||\n            el.msMatchesSelector;\n        return Array.prototype.filter.call(el.children, function (child) {\n            return matches.call(child, query);\n        })[0];\n    };\n    SimpleBarCore.rtlHelpers = null;\n    SimpleBarCore.defaultOptions = {\n        forceVisible: false,\n        clickOnTrack: true,\n        scrollbarMinSize: 25,\n        scrollbarMaxSize: 0,\n        ariaLabel: 'scrollable content',\n        tabIndex: 0,\n        classNames: {\n            contentEl: 'simplebar-content',\n            contentWrapper: 'simplebar-content-wrapper',\n            offset: 'simplebar-offset',\n            mask: 'simplebar-mask',\n            wrapper: 'simplebar-wrapper',\n            placeholder: 'simplebar-placeholder',\n            scrollbar: 'simplebar-scrollbar',\n            track: 'simplebar-track',\n            heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n            heightAutoObserverEl: 'simplebar-height-auto-observer',\n            visible: 'simplebar-visible',\n            horizontal: 'simplebar-horizontal',\n            vertical: 'simplebar-vertical',\n            hover: 'simplebar-hover',\n            dragging: 'simplebar-dragging',\n            scrolling: 'simplebar-scrolling',\n            scrollable: 'simplebar-scrollable',\n            mouseEntered: 'simplebar-mouse-entered'\n        },\n        scrollableNode: null,\n        contentNode: null,\n        autoHide: true\n    };\n    /**\n     * Static functions\n     */\n    SimpleBarCore.getOptions = getOptions;\n    SimpleBarCore.helpers = helpers;\n    return SimpleBarCore;\n}());\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/simplebar-core/dist/index.mjs\n");

/***/ })

};
;
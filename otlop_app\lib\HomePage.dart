import 'package:flutter/material.dart';
import 'package:otlop_app/app_bar.dart';
import 'package:otlop_app/ld_themes.dart';
import 'package:otlop_app/screens.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/widgets/location_dialog.dart';
import 'package:provider/provider.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

// Make navindex accessible to other files
int navIndex = 1;

class _HomePageState extends State<HomePage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Setup animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn)
    );

    // Initialize data from the backend using a post-frame callback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    // Get providers before any async operations
    final restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);

    try {
      // Load restaurants
      await restaurantProvider.fetchRestaurants();

      // Check if the widget is still mounted before proceeding
      if (!mounted) return;

      // Load orders if user is authenticated
      if (authProvider.isAuthenticated) {
        await orderProvider.fetchOrders();

        // Check if the widget is still mounted before proceeding
        if (!mounted) return;

        await orderProvider.fetchCartItems();
      }
    } catch (e) {
      // Use a safer logging approach
      debugPrint('Error loading initial data: $e');
      // Handle error if needed
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: screens.elementAt(navIndex),
      ),
      appBar: _buildModernAppBar(),
      endDrawer: buildCustomDrawer(context),
      bottomNavigationBar: _buildModernBottomNavBar(),
    );
  }

  // Modern app bar with clean design
  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      leading: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Material(
          elevation: 0,
          shadowColor: primaryPurple.withAlpha(50),
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: _showLocationPicker,
            child: Container(
              decoration: BoxDecoration(
                color: primaryPurple,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.location_on,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
      ),
      title: const Text(
        'توصيلة',
        style: TextStyle(
          fontSize: 24,
          fontFamily: 'Alx',
          fontWeight: FontWeight.bold,
          color: primaryPurple,
        ),
      ),
      centerTitle: true,
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: IconButton(
            icon: const Icon(
              Icons.fastfood,
              color: primaryPurple,
              size: 28,
            ),
            onPressed: () {},
          ),
        ),
      ],
    );
  }

  // Modern bottom navigation bar with simple design

  Widget _buildModernBottomNavBar() {
return Container(
  decoration: BoxDecoration(
    color: Colors.white,
    border: Border(
      top: BorderSide(color: Colors.grey.shade300, width: 1),
    ),
    boxShadow: const [
      BoxShadow(
        color: Color(0x11000000),
        blurRadius: 12,
        offset: Offset(0, -2),
      ),
    ],
  ),
  child: BottomNavigationBar(
    currentIndex: navIndex,
    onTap: (index) {
      setState(() {
        navIndex = index;
      });
    },
    elevation: 0,
    backgroundColor: Colors.white,
    type: BottomNavigationBarType.fixed,
    showUnselectedLabels: true,
    selectedItemColor: const Color(0xFF6B46C1), // primaryPurple
    unselectedItemColor: Colors.grey.shade600,
    selectedLabelStyle: const TextStyle(
      fontFamily: 'Alx',
      fontSize: 12,
      fontWeight: FontWeight.w700,
      letterSpacing: 0.2,
    ),
    unselectedLabelStyle: const TextStyle(
      fontFamily: 'Alx',
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
    ),
    iconSize: 24,
    items: [
      BottomNavigationBarItem(
        icon: const Icon(Icons.receipt_outlined),
        activeIcon: Container(
          decoration: BoxDecoration(
            color: const Color(0x1A6B46C1), // light tint of primaryPurple
            shape: BoxShape.circle,
          ),
          padding: const EdgeInsets.all(6),
          child: const Icon(Icons.receipt, size: 26),
        ),
        label: 'الطلبات',
      ),
      BottomNavigationBarItem(
        icon: const Icon(Icons.restaurant_outlined),
        activeIcon: Container(
          decoration: BoxDecoration(
            color: const Color(0x1A6B46C1),
            shape: BoxShape.circle,
          ),
          padding: const EdgeInsets.all(6),
          child: const Icon(Icons.restaurant, size: 26),
        ),
        label: 'المطاعم',
      ),
      BottomNavigationBarItem(
        icon: const Icon(Icons.person_outlined),
        activeIcon: Container(
          decoration: BoxDecoration(
            color: const Color(0x1A6B46C1),
            shape: BoxShape.circle,
          ),
          padding: const EdgeInsets.all(6),
          child: const Icon(Icons.person, size: 26),
        ),
        label: 'الملف الشخصي',
      ),
    ],
  ),
);



  }

  void _showLocationPicker() {
    // Show the location dialog
    // The dialog will handle loading the locations in its initState with a post-frame callback
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return const LocationDialog();
      },
    );
  }
}

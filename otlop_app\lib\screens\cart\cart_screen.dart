import 'package:flutter/material.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/models/restaurant_model.dart';
import 'package:otlop_app/providers/restaurant_cart_provider.dart';
import 'package:otlop_app/services/restaurant_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/screens/checkout/checkout_screen.dart';

class CartScreen extends StatefulWidget {
  final int? specificRestaurantId; // Optional parameter to show only specific restaurant's cart

  const CartScreen({Key? key, this.specificRestaurantId}) : super(key: key);

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final RestaurantService _restaurantService = RestaurantService();
  Map<int, Restaurant> _restaurantData = {};
  bool _isLoadingRestaurantData = false;

  @override
  void initState() {
    super.initState();
    // Cart items are automatically loaded by RestaurantCartProvider
    _loadRestaurantData();

    // Listen to cart changes and reload restaurant data when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
      cartProvider.addListener(_onCartChanged);
    });
  }

  @override
  void dispose() {
    // Remove listener
    final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
    cartProvider.removeListener(_onCartChanged);
    super.dispose();
  }

  // Called when cart changes
  void _onCartChanged() {
    if (mounted) {
      _loadRestaurantData();
    }
  }

  // Load restaurant data for all restaurants in cart
  Future<void> _loadRestaurantData() async {
    setState(() {
      _isLoadingRestaurantData = true;
    });

    try {
      final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
      final restaurantIds = cartProvider.getRestaurantIdsWithItems();

      for (final restaurantId in restaurantIds) {
        try {
          final restaurantResponse = await _restaurantService.getRestaurantById(restaurantId.toString());
          final restaurant = Restaurant.fromJson(restaurantResponse);
          _restaurantData[restaurantId] = restaurant;
        } catch (e) {
          debugPrint('Error loading restaurant $restaurantId: $e');
        }
      }
    } catch (e) {
      debugPrint('Error loading restaurant data: $e');
    } finally {
      setState(() {
        _isLoadingRestaurantData = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'سلة التسوق',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'Alx',
          ),
        ),
        actions: [
          // Clear cart button
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () {
              _showClearCartConfirmation(context);
            },
          ),
        ],
      ),
      body: Consumer<RestaurantCartProvider>(
        builder: (context, cartProvider, child) {
          if (cartProvider.isLoading || _isLoadingRestaurantData) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Get restaurant IDs with items - filter by specific restaurant if provided
          List<int> restaurantIds;
          if (widget.specificRestaurantId != null) {
            // Show only the specific restaurant's cart
            final hasItems = cartProvider.getCartItemsForRestaurant(widget.specificRestaurantId!).isNotEmpty;
            restaurantIds = hasItems ? [widget.specificRestaurantId!] : [];
          } else {
            // Show all restaurants' carts
            restaurantIds = cartProvider.getRestaurantIdsWithItems();
          }

          if (restaurantIds.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'سلة التسوق فارغة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أضف منتجات إلى سلة التسوق للمتابعة',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // Navigate to restaurants screen
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text(
                      'استكشف المطاعم',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: restaurantIds.length,
            itemBuilder: (context, index) {
              final restaurantId = restaurantIds[index];
              final restaurantItems = cartProvider.getCartItemsForRestaurant(restaurantId);
              // Get restaurant name from loaded data or fallback to cart item data
              final restaurantName = _restaurantData[restaurantId]?.name ??
                                   (restaurantItems.isNotEmpty ? restaurantItems.first.restaurantName : 'Restaurant $restaurantId');

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Restaurant header
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        const Icon(Icons.store, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            restaurantName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Alx',
                            ),
                          ),
                        ),
                        // Clear restaurant cart button
                        IconButton(
                          icon: const Icon(Icons.delete_outline, size: 20),
                          onPressed: () => _showClearRestaurantCartConfirmation(context, restaurantId, restaurantName),
                        ),
                      ],
                    ),
                  ),

                  // Restaurant items
                  ...restaurantItems.map((item) => _buildCartItem(context, item)),

                  // Restaurant total
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'إجمالي المطعم:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '\$${cartProvider.getCartTotalForRestaurant(restaurantId).toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Divider between restaurants
                  if (index < restaurantIds.length - 1)
                    const Divider(height: 32),
                ],
              );
            },
          );
        },
      ),
      bottomNavigationBar: _buildCheckoutBar(context),
    );
  }

  Widget _buildCartItem(BuildContext context, CartItem item) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image
                if (item.productImage != null && item.productImage!.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: item.productImage!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[300],
                        child: const Icon(Icons.fastfood),
                      ),
                    ),
                  )
                else
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.fastfood),
                  ),

                const SizedBox(width: 16),

                // Item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.productName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),

                      // Price and quantity
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${item.price.toStringAsFixed(2)} د.ل',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),

                          // Quantity control
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.remove_circle_outline, size: 20),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                onPressed: () {
                                  if (item.quantity > 1) {
                                    _updateCartItemQuantity(context, item.id.toString(), item.quantity - 1);
                                  } else {
                                    _showRemoveItemConfirmation(context, item);
                                  }
                                },
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                child: Text(
                                  item.quantity.toString(),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.add_circle_outline, size: 20),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                onPressed: () {
                                  _updateCartItemQuantity(context, item.id.toString(), item.quantity + 1);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Remove button
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    _showRemoveItemConfirmation(context, item);
                  },
                ),
              ],
            ),

            // Enhanced ingredients section (keep the new design)
            if (item.selectedIngredients != null && item.selectedIngredients!.isNotEmpty) ...[
              const SizedBox(height: 16),
Container(
  width: double.infinity,
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
  decoration: BoxDecoration(
    color: Theme.of(context).colorScheme.surface.withOpacity(0.05),
    borderRadius: BorderRadius.circular(14),
    border: Border.all(
      color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
      width: 1,
    ),
  ),
  child: Wrap(
    spacing: 8,
    runSpacing: 8,
    children: item.selectedIngredients!.map((ingredient) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.06),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check,
              size: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              ingredient.name,
              style: TextStyle(
                fontSize: 13,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }).toList(),
  ),
)

            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutBar(BuildContext context) {
    return Consumer<RestaurantCartProvider>(
      builder: (context, cartProvider, child) {
        final restaurantIds = cartProvider.getRestaurantIdsWithItems();

        if (restaurantIds.isEmpty) {
          return const SizedBox.shrink();
        }

        // Calculate total for all restaurants
        double subtotal = 0.0;
        double deliveryFee = 0.0;

        for (final restaurantId in restaurantIds) {
          subtotal += cartProvider.getCartTotalForRestaurant(restaurantId);

          // Get delivery fee from restaurant data
          final restaurant = _restaurantData[restaurantId];
          if (restaurant != null && restaurant.deliveryFee != null) {
            deliveryFee += restaurant.deliveryFee!;
          } else {
            // Fallback to default delivery fee if restaurant data not available
            deliveryFee += 5.0;
          }
        }

        final total = subtotal + deliveryFee;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.3),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Subtotal
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'المجموع الفرعي',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${subtotal.toStringAsFixed(2)} د.ل',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Delivery fee
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'رسوم التوصيل',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${deliveryFee.toStringAsFixed(2)} د.ل',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),

                const Divider(height: 16),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'الإجمالي',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      '${total.toStringAsFixed(2)} د.ل',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Checkout button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: cartProvider.isLoading
                        ? null
                        : () => _proceedToCheckout(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: cartProvider.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'المتابعة إلى الدفع',
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: "Alx",
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _updateCartItemQuantity(BuildContext context, String cartItemId, int newQuantity) async {
    final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
    await cartProvider.updateCartItemQuantity(int.parse(cartItemId), newQuantity);
  }

  Future<void> _removeCartItem(BuildContext context, CartItem item) async {
    final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
    await cartProvider.removeItemFromRestaurantCart(item.id);
  }

  void _showRemoveItemConfirmation(BuildContext context, CartItem item) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('حذف المنتج'),
          content: Text('هل أنت متأكد من حذف "${item.productName}" من السلة؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _removeCartItem(context, item);
              },
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _showClearCartConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إفراغ السلة'),
          content: const Text('هل أنت متأكد من إفراغ السلة؟ سيتم حذف جميع المنتجات.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
                await cartProvider.clearAllCarts();
              },
              child: const Text('إفراغ السلة'),
            ),
          ],
        );
      },
    );
  }

  void _showClearRestaurantCartConfirmation(BuildContext context, int restaurantId, String restaurantName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إفراغ سلة المطعم'),
          content: Text('هل أنت متأكد من إفراغ سلة $restaurantName؟ سيتم حذف جميع المنتجات من هذا المطعم.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final cartProvider = Provider.of<RestaurantCartProvider>(context, listen: false);
                await cartProvider.clearRestaurantCart(restaurantId);
              },
              child: const Text('إفراغ السلة'),
            ),
          ],
        );
      },
    );
  }

  void _proceedToCheckout(BuildContext context) {
    // Pass restaurant data to checkout screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CheckoutScreen(restaurantData: _restaurantData),
      ),
    );
  }
}
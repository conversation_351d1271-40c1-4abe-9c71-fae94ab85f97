import 'dart:async';
import 'package:flutter/material.dart';
import 'package:otlop_app/services/api_service.dart';

class BestOffer {
  final String name;
  final String shopName;
  final double price;
  final int percent;
  final String shopImage;

  BestOffer({
    required this.name,
    required this.shopName,
    required this.price,
    required this.percent,
    required this.shopImage,
  });
}

class BestOffersWidget extends StatefulWidget {
  final List<BestOffer> offers;

  const BestOffersWidget({super.key, required this.offers});

  @override
  State<BestOffersWidget> createState() => _BestOffersWidgetState();
}

class _BestOffersWidgetState extends State<BestOffersWidget>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late Timer _timer;
  int _currentPage = 0;

  // Animation controller for the discount badge
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    // Initialize page controller
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: 0,
    );

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Start the animation
    _animationController.repeat(reverse: true);

    // Auto-scroll timer
    _timer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (widget.offers.length > 1 && mounted) {
        setState(() {
          _currentPage = (_currentPage + 1) % widget.offers.length;
        });
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.offers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Best Offers',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Handle "See All" action
                },
                child: Row(
                  children: [
                    Text(
                      'See All',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Offers Carousel
        SizedBox(
          height: 180,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.offers.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, i) {
              final offer = widget.offers[i];
              return _buildOfferCard(context, offer);
            },
          ),
        ),

        // Page Indicator
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.offers.length,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentPage == index
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.withAlpha(100),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOfferCard(BuildContext context, BestOffer offer) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: offer.shopImage.startsWith('http')
                    ? Image.network(
                        offer.shopImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'assets/images/rest.jpg',
                            fit: BoxFit.cover,
                          );
                        },
                      )
                    : Image.asset(
                        offer.shopImage,
                        fit: BoxFit.cover,
                      ),
              ),

              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(180),
                      ],
                      stops: const [0.5, 1.0],
                    ),
                  ),
                ),
              ),
              // Offer Info
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(15.0),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(179), // 0.7 * 255 = 179
                      ],
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${offer.name} - ${offer.shopName}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              offset: Offset(1, 1),
                              blurRadius: 2,
                              color: Colors.black,
                            ),
                          ],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '\$${offer.price.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${offer.percent}% OFF',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShopCard extends StatelessWidget {
  final String shopName;
  final bool openedStatus;
  final String imageUrl;
  final String? cuisineType;
  final String? city;
  final String? address;
  final double? distance;
  final double? deliveryFee;
  final String? restaurantType;

  const ShopCard({
    super.key,
    required this.shopName,
    required this.openedStatus,
    required this.imageUrl,
    this.cuisineType,
    this.city,
    this.address,
    this.distance,
    this.deliveryFee,
    this.restaurantType,
  });

  // Helper method to fix image URLs
  String _fixImageUrl(String url) {
    if (url.isEmpty) return 'assets/images/rest.jpg';
    if (url.startsWith('http')) return url;
    if (url.startsWith('assets/')) return url;

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = url.startsWith('/') ? url.substring(1) : url;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.1),
          width: 0.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          splashColor: primaryColor.withValues(alpha: 0.08),
          highlightColor: primaryColor.withValues(alpha: 0.04),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // YouTube-style wide image on top
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    child: AspectRatio(
                      aspectRatio: 2.2 / 1, // Reduced height - more rectangular
                      child: Container(
                        width: double.infinity,
                        child: imageUrl.startsWith('assets/')
                            ? Image.asset(
                                imageUrl,
                                fit: BoxFit.cover,
                                errorBuilder: (_, __, ___) => Image.asset(
                                  'assets/images/rest.jpg',
                                  fit: BoxFit.cover,
                                ),
                              )
                            : FadeInImage.assetNetwork(
                                placeholder: 'assets/images/rest.jpg',
                                image: imageUrl.startsWith('http')
                                    ? imageUrl
                                    : _fixImageUrl(imageUrl),
                                fit: BoxFit.cover,
                                fadeInDuration:
                                    const Duration(milliseconds: 200),
                                imageErrorBuilder: (_, __, ___) => Image.asset(
                                  'assets/images/rest.jpg',
                                  fit: BoxFit.cover,
                                ),
                              ),
                      ),
                    ),
                  ),
                  // Distance indicator - Top Left
                  if (distance != null)
                    Positioned(
                      top: 16,
                      left: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 3),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white.withOpacity(0.95),
                          border: Border.all(
                            color: primaryColor.withOpacity(0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.near_me,
                              size: 14,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '${distance!.toStringAsFixed(1)} كم',
                              style: TextStyle(
                                fontSize: 13,
                                color: primaryColor,
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Status indicator - Top Right
                  Positioned(
                    top: 16,
                    right: 16,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 450),
                      curve: Curves.easeInOutQuint,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: openedStatus
                              ? [
                                  const Color(0xFF4CE5B1).withOpacity(1),
                                  const Color(0xFF26A96C).withOpacity(1),
                                ]
                              : [
                                  const Color(0xFFFF6B6B).withOpacity(1),
                                  const Color(0xFFD64045).withOpacity(1),
                                ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: openedStatus
                                ? const Color(0xFF26A96C).withOpacity(0.6)
                                : const Color(0xFFD64045).withOpacity(0.6),
                            blurRadius: 12,
                            spreadRadius: 0.5,
                            offset: const Offset(0, 4),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.white.withOpacity(0.25),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 350),
                            transitionBuilder: (child, animation) =>
                                ScaleTransition(
                              scale: Tween<double>(begin: 0.8, end: 1.0)
                                  .animate(animation),
                              child: FadeTransition(
                                opacity: animation,
                                child: child,
                              ),
                            ),
                            child: Icon(
                              openedStatus
                                  ? Icons.verified_rounded
                                  : Icons.close_rounded,
                              key: ValueKey<bool>(openedStatus),
                              size: 15,
                              color: Colors.white.withOpacity(0.95),
                            ),
                          ),
                          const SizedBox(width: 7),
                          Text(
                            openedStatus ? 'مفتوح' : 'مغلق',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: 'ALX',
                              color: Colors.white.withOpacity(0.98),
                              fontWeight: FontWeight.w700,
                              height: 1.3,
                              letterSpacing: 0.2,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.15),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              // Restaurant info below image
              Padding(
                padding: const EdgeInsets.all(18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Restaurant name with inline type (modern style)
                    Row(
                      children: [
                        Expanded(
                          child: RichText(
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            text: TextSpan(
                              children: [
                                // Restaurant name
                                TextSpan(
                                  text: shopName,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF1F2937),
                                    letterSpacing: -0.1,
                                    height: 1.3,
                                  ),
                                ),
                                // Centered dot and restaurant type
                                if (restaurantType != null && restaurantType!.isNotEmpty) ...[
                                  TextSpan(
                                    text: ' • ',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[400],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  WidgetSpan(
                                    child: Icon(
                                      Icons.restaurant_menu,
                                      size: 14,
                                      color: primaryColor.withOpacity(0.8),
                                    ),
                                  ),
                                  TextSpan(
                                    text: ' $restaurantType',
                                    style: TextStyle(
                                      fontSize: 15,
                                      fontFamily: 'ALX',
                                      color: primaryColor.withOpacity(0.9),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.1,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 10),

                    // Location and delivery fee in the same row
                    Row(
                      children: [
                        // Location info
                        if (city != null || address != null) ...[
                          Icon(
                            Icons.location_on_outlined,
                            size: 15,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              [city, address].where((e) => e != null && e.isNotEmpty).join(', '),
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                                height: 1.2,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                        
                        // Delivery fee
                        if (deliveryFee != null) ...[
                          const SizedBox(width: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                              color: const Color(0xFF10B981).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: const Color(0xFF10B981).withOpacity(0.2),
                                width: 0.8,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.delivery_dining,
                                  size: 14,
                                  color: Color(0xFF10B981),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${deliveryFee!.toStringAsFixed(2)} د.ل',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF10B981),
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 0.1,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class OrderCardWidget extends StatelessWidget {
  final String imageUrl;
  final String name;
  final String shopName;
  final double price;
  final DateTime orderDate;
  final int quantity;
  final int status;

  const OrderCardWidget({
    super.key,
    required this.imageUrl,
    required this.name,
    required this.shopName,
    required this.price,
    required this.orderDate,
    required this.quantity,
    required this.status,
  });

  // Helper method to fix image URLs
  String _fixImageUrl(String url) {
    if (url.isEmpty) return 'assets/images/food.jpg';
    if (url.startsWith('http')) return url;
    if (url.startsWith('assets/')) return url;

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = url.startsWith('/') ? url.substring(1) : url;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // Image with improved robust handling
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Container(
                width: 80,
                height: 80,
                color: Colors.grey[300], // Background color while loading
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Default placeholder image
                    Image.asset(
                      'assets/images/food.jpg',
                      fit: BoxFit.cover,
                    ),

                    // Actual image with error handling
                    if (imageUrl.startsWith('assets/'))
                      // Asset image
                      Image.asset(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const SizedBox
                              .shrink(); // Placeholder is already shown
                        },
                      )
                    else
                      // Network image (or path that needs to be fixed)
                      FadeInImage.assetNetwork(
                        placeholder: 'assets/images/food.jpg',
                        image: imageUrl.startsWith('http')
                            ? imageUrl
                            : _fixImageUrl(imageUrl),
                        fit: BoxFit.cover,
                        fadeInDuration: const Duration(milliseconds: 300),
                        fadeOutDuration: const Duration(milliseconds: 300),
                        imageErrorBuilder: (context, error, stackTrace) {
                          // If the image fails to load, the placeholder is already shown
                          return const SizedBox.shrink();
                        },
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 15),

            // Order details
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          shopName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '\$${price.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.shopping_bag_outlined,
                                size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              'x$quantity',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Order status and date
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  decoration: BoxDecoration(
                    color: status == 0
                        ? Colors.green
                        : status == 1
                            ? Colors.orange
                            : Colors.red,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    status == 0
                        ? 'Pending'
                        : status == 1
                            ? 'Preparing'
                            : 'Delivered',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.calendar_today,
                        size: 14, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      '${orderDate.day}/${orderDate.month}/${orderDate.year}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

List<CartItem> fakeCartItems = const [
  CartItem(
      proName: 'مشروب',
      shopName: 'بورعي للمشاوي',
      imageUrl: 'assets/images/food.jpg',
      quantity: 2,
      price: 100),
  CartItem(
      proName: ' ايرفورس',
      shopName: ' البورعي',
      imageUrl: 'assets/images/food2.jpg',
      quantity: 3,
      price: 80),
  CartItem(
      proName: '303030303',
      shopName: ' البورعي',
      imageUrl: 'assets/images/food3.jpg',
      quantity: 1,
      price: 800),
  CartItem(
      proName: '  ماكس',
      shopName: ' البورعي',
      imageUrl: 'assets/images/food4.jpg',
      quantity: 2,
      price: 120),
];

class CartItem extends StatelessWidget {
  final String proName;
  final String shopName;
  final String imageUrl;
  final int quantity;
  final int price;
  const CartItem(
      {super.key,
      required this.proName,
      required this.shopName,
      required this.quantity,
      required this.price,
      required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 7.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            // Image
            // ClipRRect(
            //   borderRadius: BorderRadius.circular(7.0),
            //   child: Image.asset(
            //     imageUrl,
            //     height: 40,
            //     width: 20,
            //     fit: BoxFit.cover,
            //   ),
            // ),
            // const SizedBox(width: 10),

            // Product Info
            Expanded(
              flex: 4,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    proName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    shopName,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Vertical Divider

            // Quantity
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "Quantity",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '$quantity',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Vertical Divider

            // Price
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    "Price",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '\$${price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Delete Icon
            Expanded(
              flex: 1,
              child: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  // Handle delete action
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

List<String> fakeSearchItems = [
  'مطعم البحباح',
  'مطعم الشيخ برعي',
  'سلاطة للسلاطة',
  'مطعم كازانوفا',
  'مقهى عفراء',
  'مطعم مليح',
  'مقهى ابو فهد',
  'مقهى السقا',
  'مقهى النور',
  'مطعم ابو علي',
  'مقهى الوادي',
  'مقهى ابو بوه',
];

class Search extends SearchDelegate<String> {
  final List<String> searchItems;

  Search(this.searchItems);

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = ''; // Clear the search field
        },
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      icon: AnimatedIcon(
        icon: AnimatedIcons.menu_arrow,
        progress: transitionAnimation,
      ),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results = searchItems
        .where((element) => element.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        return ListTile(
          title: Text(results[index]),
          onTap: () {
            close(context, results[index]);
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final suggestions = query.isEmpty
        ? searchItems
        : searchItems
            .where((element) =>
                element.toLowerCase().contains(query.toLowerCase()))
            .toList();

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        return ListTile(
          title: Text(
            suggestions[index],
            style:
                const TextStyle(fontWeight: FontWeight.bold, fontFamily: 'Alx'),
          ),
          onTap: () {
            query = suggestions[index];
            showResults(context);
          },
        );
      },
    );
  }
}

// class WorkingTimesinDay {
//   final String open;
//   final String close;

//   WorkingTimesinDay({required this.open, required this.close});
// }

// class WorkingTimesCard extends StatelessWidget {
//   final List<WorkingTimesinDay> workingTimes;

//   const WorkingTimesCard({super.key, required this.workingTimes});

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
//       child: Card(
//         child: Padding(
//           padding: const EdgeInsets.all(8.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 workingTimes.day,
//                 style: const TextStyle(fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 5),
//               Row(
//                 children: [
//                   const Icon(Icons.access_time),
//                   const SizedBox(width: 5),
//                   Text(
//                     '${workingTimes.open} - ${workingTimes.close}',
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

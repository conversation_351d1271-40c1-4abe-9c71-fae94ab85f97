import 'package:intl/intl.dart';
import 'dart:math' as math;

class Order {
  // Status constants
  static const int STATUS_PENDING = 1;      // waiting for driver assignment
  static const int STATUS_ACCEPTED = 2;     // driver accepted, ready for pickup
  static const int STATUS_DELIVERING = 3;   // driver picked up, on the way
  static const int STATUS_DELIVERED = 4;    // delivered successfully
  static const int STATUS_CANCELLED = 5;    // cancelled by customer
  static const int STATUS_REJECTED = 6;     // rejected by restaurant
  static const int STATUS_FAILED = 7;       // failed delivery (customer not available, etc)
  
  // Order properties
  final int orderId;
  final int? driverId;
  final int? cartId;
  final int? customerId;
  final double startLat;
  final double startLong;
  final double endLat;
  final double endLong;
  final int? duration;  // in minutes
  final DateTime orderDate;
  final int status;
  final double? price;
  final double? distance;  // in km
  final double? deliveryFee;  // delivery fee
  final double? driverProfit;  // driver profit
  final double? totalPrice;  // total order price
  final double? orderPrice;  // order price without delivery fee

  // Additional info
  final String? restaurantName;
  final String? restaurantAddress;
  final String? restaurantPhone;
  final String? customerName;
  final String? customerPhone;
  final String? customerAddress;
  final Map<String, dynamic>? items;
  
  Order({
    required this.orderId,
    this.driverId,
    this.cartId,
    this.customerId,
    required this.startLat,
    required this.startLong,
    required this.endLat,
    required this.endLong,
    this.duration,
    required this.orderDate,
    required this.status,
    this.price,
    this.distance,
    this.deliveryFee,
    this.driverProfit,
    this.totalPrice,
    this.orderPrice,
    this.restaurantName,
    this.restaurantAddress,
    this.restaurantPhone,
    this.customerName,
    this.customerPhone,
    this.customerAddress,
    this.items,
  });

  // Helper methods for status checking
  bool get isPending => status == STATUS_PENDING;
  bool get isAccepted => status == STATUS_ACCEPTED;
  bool get isDelivering => status == STATUS_DELIVERING;
  bool get isDelivered => status == STATUS_DELIVERED;
  bool get isCancelled => status == STATUS_CANCELLED;
  bool get isRejected => status == STATUS_REJECTED;
  bool get isFailed => status == STATUS_FAILED;
  
  // Active orders are pending, accepted, or delivering
  bool get isActive => status == STATUS_PENDING || 
                      status == STATUS_ACCEPTED || 
                      status == STATUS_DELIVERING;
  
  // Completed orders are delivered, cancelled, rejected, or failed
  bool get isCompleted => status == STATUS_DELIVERED || 
                         status == STATUS_CANCELLED || 
                         status == STATUS_REJECTED ||
                         status == STATUS_FAILED;
  
  // Status text for UI display
  String get statusText {
    switch (status) {
      case STATUS_PENDING: return "في الانتظار";
      case STATUS_ACCEPTED: return "تم القبول";
      case STATUS_DELIVERING: return "جاري التوصيل";
      case STATUS_DELIVERED: return "تم التوصيل";
      case STATUS_CANCELLED: return "ملغى";
      case STATUS_REJECTED: return "مرفوض";
      case STATUS_FAILED: return "فشل التوصيل";
      default: return "غير معروف";
    }
  }
  
  // Status color for UI display
  int get statusColor {
    switch (status) {
      case STATUS_PENDING: return 0xFFFF9800;  // Orange
      case STATUS_ACCEPTED: return 0xFF2196F3;  // Blue
      case STATUS_DELIVERING: return 0xFF9C27B0;  // Purple
      case STATUS_DELIVERED: return 0xFF4CAF50;  // Green
      case STATUS_CANCELLED: return 0xFFF44336;  // Red
      case STATUS_REJECTED: return 0xFF795548;  // Brown
      case STATUS_FAILED: return 0xFF9E9E9E;  // Grey
      default: return 0xFF000000;  // Black
    }
  }
  
  // Formatted price for UI display
  String get formattedPrice {
    if (price == null) return "غير محدد";
    return "${price!.toStringAsFixed(2)} ر.س";
  }
  
  // Formatted distance for UI display
  String get formattedDistance {
    if (distance == null) {
      // If distance is not provided, calculate straight-line distance
      final calculatedDistance = _calculateDistance(startLat, startLong, endLat, endLong);
      return "${calculatedDistance.toStringAsFixed(1)} كم";
    }
    return "${distance!.toStringAsFixed(1)} كم";
  }
  
  // Calculate distance between two coordinates (using Haversine formula)
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const R = 6371.0; // Earth radius in kilometers
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLon = _degreesToRadians(lon2 - lon1);
    
    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
              math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
              math.sin(dLon / 2) * math.sin(dLon / 2);
              
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return R * c;
  }
  
  double _degreesToRadians(double degrees) {
    return degrees * math.pi / 180;
  }
  
  // Helper method to safely parse DateTime
  static DateTime _parseDateTime(dynamic date) {
    if (date == null) return DateTime.now();
    if (date is String) {
      try {
        return DateTime.parse(date);
      } catch (e) {
        print("Error parsing date: $date. Error: $e");
        return DateTime.now();
      }
    }
    return DateTime.now();
  }
  
  // Helper method to safely parse double
  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print("Error parsing double: $value. Error: $e");
        return defaultValue;
      }
    }
    return defaultValue;
  }
  
  // Helper method to safely parse int
  static int _parseInt(dynamic value, {int defaultValue = 0}) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        print("Error parsing int: $value. Error: $e");
        return defaultValue;
      }
    }
    return defaultValue;
  }
  
  // Factory constructor to create an Order from JSON
  factory Order.fromJson(Map<String, dynamic> json) {
    print("Creating Order from JSON: $json");
    return Order(
      orderId: _parseInt(json['orderId'] ?? json['id']),
      driverId: json['driverId'],
      cartId: json['cartId'],
      customerId: json['customerId'],
      startLat: _parseDouble(json['startLat'] ?? json['startPointLatitude']),
      startLong: _parseDouble(json['startLong'] ?? json['startPointLongitude']),
      endLat: _parseDouble(json['endLat'] ?? json['endPointLatitude']),
      endLong: _parseDouble(json['endLong'] ?? json['endPointLongitude']),
      duration: json['duration'],
      orderDate: _parseDateTime(json['orderDate'] ?? json['createdAt']),
      status: _parseInt(json['status']),
      price: _parseDouble(json['price'] ?? json['totalPrice']),
      deliveryFee: _parseDouble(json['deliveryFee'] ?? json['DeliveryFee']),
      driverProfit: _parseDouble(json['driverProfit'] ?? json['DriverProfit']),
      totalPrice: _parseDouble(json['totalPrice'] ?? json['TotalPrice']),
      orderPrice: _parseDouble(json['orderPrice'] ?? json['OrderPrice']),
      distance: _parseDouble(json['distance']),
      restaurantName: json['restaurantName'],
      restaurantAddress: json['restaurantAddress'],
      restaurantPhone: json['restaurantPhone'],
      customerName: json['customerName'],
      customerPhone: json['customerPhone'],
      customerAddress: json['customerAddress'] ?? json['deliveryAddress'],
      items: json['items'],
    );
  }
  
  // Convert an Order instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'driverId': driverId,
      'cartId': cartId,
      'customerId': customerId,
      'startLat': startLat,
      'startLong': startLong,
      'endLat': endLat,
      'endLong': endLong,
      'duration': duration,
      'orderDate': orderDate.toIso8601String(),
      'status': status,
      'price': price,
      'distance': distance,
      'deliveryFee': deliveryFee,
      'driverProfit': driverProfit,
      'totalPrice': totalPrice,
      'orderPrice': orderPrice,
      'restaurantName': restaurantName,
      'restaurantAddress': restaurantAddress,
      'restaurantPhone': restaurantPhone,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerAddress': customerAddress,
      'items': items,
    };
  }
} 
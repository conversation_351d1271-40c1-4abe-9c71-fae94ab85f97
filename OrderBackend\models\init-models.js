var DataTypes = require("sequelize").DataTypes;
var _Cart = require("./Cart");
var _Category = require("./Category");
var _Customer = require("./Customer");
var _CustomerPlaces = require("./CustomerPlaces");
var _Driver = require("./Driver");
var _DriverInvoices = require("./DriverInvoices");
var _DriverModerate = require("./DriverModerate");
var _Ingredient = require("./Ingredient");
var _IngredientUsage = require("./IngredientUsage");
var _Invoice = require("./Invoice");
var _InvoiceItem = require("./InvoiceItem");
var _MainTreasury = require("./MainTreasury");
var _Order = require("./Order");
var _Permission = require("./Permission");
var _Product = require("./Product");
var _ProductCart = require("./ProductCart");
var _Restaurant = require("./Restaurant");
var _RestaurantAccounts = require("./RestaurantAccounts");
var _RestaurantType = require("./RestaurantType");
var _Restaurant_Category_Product = require("./Restaurant_Category_Product");
var _Restaurant_RestaurantType = require("./Restaurant_RestaurantType");
var _ReturnMoneyToRestaurant = require("./ReturnMoneyToRestaurant");
var _UserAccount = require("./UserAccount");
var _UserPermission = require("./UserPermission");
var _WithdrawTransactions = require("./WithdrawTransactions");
var _WorkingTimes = require("./WorkingTimes");

function initModels(sequelize) {
  var Cart = _Cart(sequelize, DataTypes);
  var Category = _Category(sequelize, DataTypes);
  var Customer = _Customer(sequelize, DataTypes);
  var CustomerPlaces = _CustomerPlaces(sequelize, DataTypes);
  var Driver = _Driver(sequelize, DataTypes);
  var DriverInvoices = _DriverInvoices(sequelize, DataTypes);
  var DriverModerate = _DriverModerate(sequelize, DataTypes);
  var Ingredient = _Ingredient(sequelize, DataTypes);
  var IngredientUsage = _IngredientUsage(sequelize, DataTypes);
  var Invoice = _Invoice(sequelize, DataTypes);
  var InvoiceItem = _InvoiceItem(sequelize, DataTypes);
  var MainTreasury = _MainTreasury(sequelize, DataTypes);
  var Order = _Order(sequelize, DataTypes);
  var Permission = _Permission(sequelize, DataTypes);
  var Product = _Product(sequelize, DataTypes);
  var ProductCart = _ProductCart(sequelize, DataTypes);
  var Restaurant = _Restaurant(sequelize, DataTypes);
  var RestaurantAccounts = _RestaurantAccounts(sequelize, DataTypes);
  var RestaurantType = _RestaurantType(sequelize, DataTypes);
  var Restaurant_Category_Product = _Restaurant_Category_Product(sequelize, DataTypes);
  var Restaurant_RestaurantType = _Restaurant_RestaurantType(sequelize, DataTypes);
  var ReturnMoneyToRestaurant = _ReturnMoneyToRestaurant(sequelize, DataTypes);
  var UserAccount = _UserAccount(sequelize, DataTypes);
  var UserPermission = _UserPermission(sequelize, DataTypes);
  var WithdrawTransactions = _WithdrawTransactions(sequelize, DataTypes);
  var WorkingTimes = _WorkingTimes(sequelize, DataTypes);

  Restaurant.belongsToMany(RestaurantType, { as: 'RestaurantTypeID_RestaurantTypes', through: Restaurant_RestaurantType, foreignKey: "RestaurantID", otherKey: "RestaurantTypeID" });
  RestaurantType.belongsToMany(Restaurant, { as: 'RestaurantID_Restaurants', through: Restaurant_RestaurantType, foreignKey: "RestaurantTypeID", otherKey: "RestaurantID" });
  ProductCart.belongsTo(Cart, { as: "Cart", foreignKey: "CartID"});
  Cart.hasMany(ProductCart, { as: "ProductCarts", foreignKey: "CartID"});
  Product.belongsTo(Category, { as: "Category", foreignKey: "CategoryID"});
  Category.hasMany(Product, { as: "Products", foreignKey: "CategoryID"});
  Restaurant_Category_Product.belongsTo(Category, { as: "Category", foreignKey: "CategoryID"});
  Category.hasMany(Restaurant_Category_Product, { as: "Restaurant_Category_Products", foreignKey: "CategoryID"});
  Cart.belongsTo(Customer, { as: "Customer", foreignKey: "CustomerID"});
  Customer.hasMany(Cart, { as: "Carts", foreignKey: "CustomerID"});
  Invoice.belongsTo(Customer, { as: "Customer", foreignKey: "CustomerID"});
  Customer.hasMany(Invoice, { as: "Invoices", foreignKey: "CustomerID"});
  DriverInvoices.belongsTo(Driver, { as: "Driver", foreignKey: "DriverID"});
  Driver.hasMany(DriverInvoices, { as: "DriverInvoices", foreignKey: "DriverID"});
  Invoice.belongsTo(Driver, { as: "Driver", foreignKey: "DriverID"});
  Driver.hasMany(Invoice, { as: "Invoices", foreignKey: "DriverID"});
  IngredientUsage.belongsTo(Ingredient, { as: "Ingredient", foreignKey: "IngredientID"});
  Ingredient.hasMany(IngredientUsage, { as: "IngredientUsages", foreignKey: "IngredientID"});
  InvoiceItem.belongsTo(Invoice, { as: "Invoice", foreignKey: "InvoiceID"});
  Invoice.hasMany(InvoiceItem, { as: "InvoiceItems", foreignKey: "InvoiceID"});
  Invoice.belongsTo(Order, { as: "Order", foreignKey: "OrderID"});
  Order.hasMany(Invoice, { as: "Invoices", foreignKey: "OrderID"});
  Order.belongsTo(Cart, { as: "Cart", foreignKey: "CartID"});
  Cart.hasOne(Order, { as: "Order", foreignKey: "CartID"});
  Order.belongsTo(Driver, { as: "Driver", foreignKey: "DriverID"});
  Driver.hasMany(Order, { as: "Orders", foreignKey: "DriverID"});
  UserPermission.belongsTo(Permission, { as: "Permission", foreignKey: "PermissionID"});
  Permission.hasMany(UserPermission, { as: "UserPermissions", foreignKey: "PermissionID"});
  Ingredient.belongsTo(Product, { as: "Product", foreignKey: "ProductID"});
  Product.hasMany(Ingredient, { as: "Ingredients", foreignKey: "ProductID"});
  ProductCart.belongsTo(Product, { as: "Product", foreignKey: "ProductID"});
  Product.hasMany(ProductCart, { as: "ProductCarts", foreignKey: "ProductID"});
  Restaurant_Category_Product.belongsTo(Product, { as: "Product", foreignKey: "ProductID"});
  Product.hasMany(Restaurant_Category_Product, { as: "Restaurant_Category_Products", foreignKey: "ProductID"});
  IngredientUsage.belongsTo(ProductCart, { as: "ProductCart", foreignKey: "ProductCartID"});
  ProductCart.hasMany(IngredientUsage, { as: "IngredientUsages", foreignKey: "ProductCartID"});
  Invoice.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(Invoice, { as: "Invoices", foreignKey: "RestaurantID"});
  RestaurantAccounts.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(RestaurantAccounts, { as: "RestaurantAccounts", foreignKey: "RestaurantID"});
  Restaurant_Category_Product.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(Restaurant_Category_Product, { as: "Restaurant_Category_Products", foreignKey: "RestaurantID"});
  Restaurant_RestaurantType.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(Restaurant_RestaurantType, { as: "Restaurant_RestaurantTypes", foreignKey: "RestaurantID"});
  ReturnMoneyToRestaurant.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(ReturnMoneyToRestaurant, { as: "ReturnMoneyToRestaurants", foreignKey: "RestaurantID"});
  WorkingTimes.belongsTo(Restaurant, { as: "Restaurant", foreignKey: "RestaurantID"});
  Restaurant.hasMany(WorkingTimes, { as: "WorkingTimes", foreignKey: "RestaurantID"});
  Restaurant.belongsTo(RestaurantType, { as: "RestaurantType", foreignKey: "RestaurantTypeID"});
  RestaurantType.hasMany(Restaurant, { as: "Restaurants", foreignKey: "RestaurantTypeID"});
  Restaurant_RestaurantType.belongsTo(RestaurantType, { as: "RestaurantType", foreignKey: "RestaurantTypeID"});
  RestaurantType.hasMany(Restaurant_RestaurantType, { as: "Restaurant_RestaurantTypes", foreignKey: "RestaurantTypeID"});
  DriverModerate.belongsTo(UserAccount, { as: "UpdateBy_UserAccount", foreignKey: "UpdateBy"});
  UserAccount.hasMany(DriverModerate, { as: "DriverModerates", foreignKey: "UpdateBy"});
  ReturnMoneyToRestaurant.belongsTo(UserAccount, { as: "InsertBy_UserAccount", foreignKey: "InsertBy"});
  UserAccount.hasMany(ReturnMoneyToRestaurant, { as: "ReturnMoneyToRestaurants", foreignKey: "InsertBy"});
  UserPermission.belongsTo(UserAccount, { as: "User", foreignKey: "UserID"});
  UserAccount.hasMany(UserPermission, { as: "UserPermissions", foreignKey: "UserID"});
  WithdrawTransactions.belongsTo(UserAccount, { as: "User", foreignKey: "UserID"});
  UserAccount.hasMany(WithdrawTransactions, { as: "WithdrawTransactions", foreignKey: "UserID"});

  return {
    Cart,
    Category,
    Customer,
    CustomerPlaces,
    Driver,
    DriverInvoices,
    DriverModerate,
    Ingredient,
    IngredientUsage,
    Invoice,
    InvoiceItem,
    MainTreasury,
    Order,
    Permission,
    Product,
    ProductCart,
    Restaurant,
    RestaurantAccounts,
    RestaurantType,
    Restaurant_Category_Product,
    Restaurant_RestaurantType,
    ReturnMoneyToRestaurant,
    UserAccount,
    UserPermission,
    WithdrawTransactions,
    WorkingTimes,
  };
}
module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;
